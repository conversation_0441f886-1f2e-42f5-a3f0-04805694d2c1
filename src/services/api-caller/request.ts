import {
  type RequestOptions,
  fetchWrapper,
  type ResponseType,
} from "./fetch-wrapper";

const request = (() => {
  async function get<T>(
    url: string,
    reponseType: ResponseType = "json",
    options?: RequestOptions
  ): Promise<T> {
    return fetchWrapper<T>("GET", url, undefined, reponseType, options);
  }

  async function post<T>(
    url: string,
    body: Record<string, unknown>,
    reponseType: ResponseType = "json",
    options?: RequestOptions
  ): Promise<T> {
    return fetchWrapper<T>("POST", url, body, reponseType, options);
  }

  async function put<T>(
    url: string,
    body: Record<string, unknown>,
    reponseType: ResponseType = "json",
    options?: RequestOptions
  ): Promise<T> {
    return fetchWrapper<T>("PUT", url, body, reponseType, options);
  }

  async function _delete<T>(url: string, options?: RequestOptions): Promise<T> {
    return fetchWrapper<T>("DELETE", url, undefined, "json", options);
  }

  return {
    get,
    post,
    put,
    delete: _delete,
  };
})();

export { request };
export { type RequestOptions } from "./fetch-wrapper";
