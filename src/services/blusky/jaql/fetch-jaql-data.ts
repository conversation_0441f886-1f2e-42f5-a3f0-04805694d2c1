import { environment } from "@constants/environment";

import { RequestError } from "../../../utils/errors/request-error";
import { request, type RequestOptions } from "../../api-caller/request";

const jaqlUrl = `${environment.bluskyBaseUrl}/datasources/${environment.bluskyCubeId}/jaql`;

const fetchJaqlData = async (
  jaql: Record<string, unknown>,
  token: string
): Promise<Array<Record<string, string | number | undefined>>> => {
  try {
    const options: RequestOptions = {
      headers: {
        accept: "application/json",
        "Content-Type": "application/json",
        // eslint-disable-next-line @typescript-eslint/naming-convention
        Authorization: `Bearer ${token}`,
      },
    };

    const response = await request.post<{
      headers: string[];
      values: Array<Array<{ data: string | number; text: string }>>;
    }>(jaqlUrl, jaql, "json", options);

    const camelCaseHeaders = response.headers.map((header) =>
      _toCamelCase(header)
    );

    return response.values.map((row) => {
      const result: Record<string, string | number | undefined> = {};

      for (const [index, header] of camelCaseHeaders.entries()) {
        const value = row[index]?.data;
        result[header] =
          value === undefined || value === "N\\A" ? undefined : value;
      }

      return result;
    });
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error("Error in fetchJaqlData:", error);
    const error_ =
      error instanceof RequestError
        ? error
        : new Error("Error fetching blusky data");
    throw error_;
  }
};

const _toCamelCase = (string_: string): string => {
  return (
    string_
      // Match both underscores (_) and spaces (\s). The + treats multiple match as a single match.
      .replaceAll(/[\s_]+(.)?/g, (_, chr) => {
        return typeof chr === "string" ? chr.toUpperCase() : "";
      })
      .replace(/^./, (match) => match.toLowerCase())
  );
};

export { fetchJaqlData };
