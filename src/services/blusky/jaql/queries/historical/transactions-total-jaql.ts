import {
  fromDateOnlyToString,
  type DateOnly,
} from "../../../../../utils/date-only";

const getTransactionTotalAmountJaql = (
  serviceNumbers: string[],
  fromDate: DateOnly,
  toDate: DateOnly
): Record<string, unknown> => {
  const jaql = {
    datasource: {
      title: "Historical (2022)",
      fullname: "LocalHost/Historical (2022)",
      id: "aLOCALHOST_aHISTORICALIAAaKAAa2022KQAa",
      address: "LocalHost",
      database: "aHistoricalIAAaKAAa2022KQAa",
    },
    metadata: [
      {
        jaql: {
          table: "platformPivot",
          column: "type",
          dim: "[platformPivot.type]",
          datatype: "text",
          merged: true,
          title: "type",
        },
        instanceid: "0D586-B540-96",
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Final_Amt",
          dim: "[Vw_Transaction_tbl.Final_Amt]",
          datatype: "numeric",
          title: "Total Final_Amt",
          agg: "sum",
        },
        instanceid: "A80E5-15F6-EA",
        format: {
          mask: {
            type: "number",
            abbreviations: {
              t: false,
              b: false,
              m: false,
              k: false,
            },
            separated: true,
            decimals: "auto",
            abbreviateAll: false,
            isdefault: true,
          },
        },
        handlers: [{}],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "status",
          dim: "[Vw_Transaction_tbl.status]",
          datatype: "text",
          merged: true,
          filter: {
            explicit: true,
            multiSelection: true,
            members: ["STATUS_SUCCESS"],
          },
          collapsed: true,
          title: "status",
          datasource: {
            title: "Historical (2022)",
            fullname: "LocalHost/Historical (2022)",
            id: "aLOCALHOST_aHISTORICALIAAaKAAa2022KQAa",
            address: "LocalHost",
            database: "aHistoricalIAAaKAAa2022KQAa",
          },
        },
        instanceid: "FF51F-F225-6E",
        panel: "scope",
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "sandbox",
          dim: "[Vw_Transaction_tbl.sandbox]",
          datatype: "text",
          merged: true,
          filter: {
            explicit: false,
            multiSelection: true,
            exclude: {
              members: ["1", "true"],
            },
          },
          collapsed: true,
          title: "sandbox",
          datasource: {
            title: "Historical (2022)",
            fullname: "LocalHost/Historical (2022)",
            id: "aLOCALHOST_aHISTORICALIAAaKAAa2022KQAa",
            address: "LocalHost",
            database: "aHistoricalIAAaKAAa2022KQAa",
          },
        },
        instanceid: "438E8-34B1-25",
        panel: "scope",
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "updatedAt",
          dim: "[Vw_Transaction_tbl.updatedAt (Calendar)]",
          datatype: "datetime",
          merged: true,
          level: "days",
          collapsed: true,
          title: "Date",
          isDashboardFilter: true,
          datasource: {
            fullname: "localhost/Historical (2022)",
            id: "localhost_aHistoricalIAAaKAAa2022KQAa",
            address: "LocalHost",
            database: "aHistoricalIAAaKAAa2022KQAa",
            live: false,
            title: "Historical (2022)",
          },
          filter: {
            from: fromDateOnlyToString(fromDate),
            to: fromDateOnlyToString(toDate),
          },
        },
        disabled: false,
        isCascading: false,
        instanceid: "108EA-3B35-28",
        panel: "scope",
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Platform",
          dim: "[Vw_Transaction_tbl.Platform]",
          datatype: "text",
          merged: true,
          collapsed: true,
          title: "Platform",
          isDashboardFilter: true,
          datasource: {
            fullname: "localhost/Historical (2022)",
            id: "localhost_aHistoricalIAAaKAAa2022KQAa",
            address: "LocalHost",
            database: "aHistoricalIAAaKAAa2022KQAa",
            live: false,
            title: "Historical (2022)",
          },
          filter: {
            explicit: false,
            multiSelection: true,
            all: true,
          },
        },
        disabled: false,
        isCascading: false,
        instanceid: "0EB07-C67B-9D",
        panel: "scope",
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Transaction_ID",
          dim: "[Vw_Transaction_tbl.Transaction_ID]",
          datatype: "text",
          merged: true,
          title: "Transaction_ID",
          collapsed: true,
          isDashboardFilter: true,
          datasource: {
            fullname: "localhost/Historical (2022)",
            id: "localhost_aHistoricalIAAaKAAa2022KQAa",
            address: "LocalHost",
            database: "aHistoricalIAAaKAAa2022KQAa",
            live: false,
            title: "Historical (2022)",
          },
          filter: {
            explicit: false,
            multiSelection: true,
            all: true,
          },
        },
        disabled: false,
        isCascading: false,
        instanceid: "4FDA8-1F04-A3",
        panel: "scope",
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "Integrator_Name",
          dim: "[Vw_Transaction_Campaigntbl.Integrator_Name]",
          datatype: "text",
          merged: true,
          title: "Integrator Name",
          datasource: {
            fullname: "localhost/Historical (2022)",
            id: "localhost_aHistoricalIAAaKAAa2022KQAa",
            address: "LocalHost",
            database: "aHistoricalIAAaKAAa2022KQAa",
            live: false,
            title: "Historical (2022)",
          },
          collapsed: true,
          isDashboardFilter: true,
          panel: "scope",
          filter: {
            explicit: false,
            multiSelection: true,
            all: true,
          },
          $$hashKey: "object:6922",
        },
        disabled: false,
        isCascading: true,
        instanceid: "832C4-BFC2-A7",
        panel: "scope",
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "Billing_Name",
          dim: "[Vw_Transaction_Campaigntbl.Billing_Name]",
          datatype: "text",
          merged: true,
          title: "Billing Name",
          datasource: {
            fullname: "localhost/Historical (2022)",
            id: "localhost_aHistoricalIAAaKAAa2022KQAa",
            address: "LocalHost",
            database: "aHistoricalIAAaKAAa2022KQAa",
            live: false,
            title: "Historical (2022)",
          },
          collapsed: true,
          isDashboardFilter: true,
          panel: "scope",
          filter: {
            explicit: false,
            multiSelection: true,
            all: true,
          },
          $$hashKey: "object:6923",
        },
        disabled: false,
        isCascading: true,
        instanceid: "594FE-E50D-E3",
        rootInstanceId: "832C4-BFC2-A7",
        panel: "scope",
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "Program_Name",
          dim: "[Vw_Transaction_Campaigntbl.Program_Name]",
          datatype: "text",
          merged: true,
          title: "Program Name",
          datasource: {
            fullname: "localhost/Historical (2022)",
            id: "localhost_aHistoricalIAAaKAAa2022KQAa",
            address: "LocalHost",
            database: "aHistoricalIAAaKAAa2022KQAa",
            live: false,
            title: "Historical (2022)",
          },
          collapsed: true,
          isDashboardFilter: true,
          filter: {
            explicit: false,
            multiSelection: true,
            all: true,
          },
          $$hashKey: "object:6924",
        },
        disabled: false,
        isCascading: true,
        instanceid: "DE4B4-B533-C6",
        rootInstanceId: "832C4-BFC2-A7",
        panel: "scope",
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "Service_Number",
          dim: "[Vw_Transaction_Campaigntbl.Service_Number]",
          datatype: "text",
          merged: true,
          title: "Service_Number",
          collapsed: true,
          isDashboardFilter: true,
          datasource: {
            fullname: "localhost/Historical (2022)",
            id: "localhost_aHistoricalIAAaKAAa2022KQAa",
            address: "LocalHost",
            database: "aHistoricalIAAaKAAa2022KQAa",
            live: false,
            title: "Historical (2022)",
          },
          filter: {
            explicit: true,
            multiSelection: true,
            members: serviceNumbers,
          },
        },
        disabled: false,
        isCascading: false,
        instanceid: "213E1-2647-65",
        panel: "scope",
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "USER_NAME",
          dim: "[Vw_Transaction_tbl.USER_NAME]",
          datatype: "text",
          merged: true,
          title: "USER_NAME",
          collapsed: true,
          isDashboardFilter: true,
          datasource: {
            fullname: "localhost/Historical (2022)",
            id: "localhost_aHistoricalIAAaKAAa2022KQAa",
            address: "LocalHost",
            database: "aHistoricalIAAaKAAa2022KQAa",
            live: false,
            title: "Historical (2022)",
          },
          filter: {
            explicit: false,
            multiSelection: true,
            all: true,
          },
        },
        disabled: false,
        isCascading: false,
        instanceid: "4682D-2C3B-90",
        panel: "scope",
      },
    ],
    ungroup: true,
    offset: 0,
    m2mThresholdFlag: 0,
    useAcceleration: false,
    isMaskedResult: true,
    format: "json",
    widgetType: "tablewidget",
    by: "widget",
    dashboard: "677d5fb647691b0033b03d8d;Historical JAQLs",
  };

  return jaql;
};

export { getTransactionTotalAmountJaql };
