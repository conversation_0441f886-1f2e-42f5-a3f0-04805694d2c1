const getFirstTransactionDateJaql = (serviceNumber: string) => {
  return {
    datasource: {
      title: "Historical (2022)",
      fullname: "LocalHost/Historical (2022)",
      id: "aLOCALHOST_aHISTORICALIAAaKAAa2022KQAa",
      address: "LocalHost",
      database: "aHistoricalIAAaKAAa2022KQAa",
    },
    metadata: [
      {
        jaql: {
          table: "FirstTransactionServiceNumber",
          column: "FirstTransaction",
          dim: "[FirstTransactionServiceNumber.FirstTransaction (Calendar)]",
          datatype: "datetime",
          merged: true,
          level: "days",
          title: "FirstTransaction",
        },
        instanceid: "EF061-4593-4D",
        format: {
          mask: {
            years: "yyyy",
            quarters: "yyyy Q",
            months: "MM/yyyy",
            weeks: "ww yyyy",
            days: "yyyy-MM-dd",
            minutes: "HH:mm",
            dateAndTime: "MM/dd/yyyy HH:mm",
          },
        },
        hierarchies: ["calendar", "calendar - weeks"],
        handlers: [{}],
      },
      {
        jaql: {
          table: "FirstTransactionServiceNumber",
          column: "SERVICE_NUMBER",
          dim: "[FirstTransactionServiceNumber.SERVICE_NUMBER]",
          datatype: "text",
          merged: true,
          title: "ServiceNumber",
        },
        instanceid: "B8BD4-AB66-A1",
        handlers: [],
      },
      {
        jaql: {
          table: "FirstTransactionServiceNumber",
          column: "SERVICE_NUMBER",
          dim: "[FirstTransactionServiceNumber.SERVICE_NUMBER]",
          datatype: "text",
          merged: true,
          title: "SERVICE_NUMBER",
          filter: {
            explicit: true,
            multiSelection: true,
            members: [serviceNumber],
          },
          datasource: {
            title: "Historical (2022)",
            fullname: "LocalHost/Historical (2022)",
            id: "aLOCALHOST_aHISTORICALIAAaKAAa2022KQAa",
            address: "LocalHost",
            database: "aHistoricalIAAaKAAa2022KQAa",
          },
          collapsed: true,
        },
        instanceid: "2E68F-F4ED-D3",
        panel: "scope",
        handlers: [],
      },
    ],
    ungroup: true,
    count: 1,
    offset: 0,
    m2mThresholdFlag: 0,
    useAcceleration: false,
    isMaskedResult: true,
    format: "json",
    widgetType: "tablewidget",
    by: "widget",
    dashboard: "677d5fb647691b0033b03d8d;Historical JAQLs",
  };
};

export { getFirstTransactionDateJaql };
