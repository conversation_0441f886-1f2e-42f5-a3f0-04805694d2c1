import {
  fromDateOnlyToString,
  type DateOnly,
} from "../../../../../utils/date-only";

const getTransactionsJaql = (
  serviceNumber: string,
  fromDate: DateOnly,
  toDate: DateOnly
) => {
  const jaql = {
    datasource: {
      address: "LocalHost",
      title: "Historical (2022)",
      id: "localhost_aHistoricalIAAaKAAa2022KQAa",
      database: "aHistoricalIAAaKAAa2022KQAa",
      fullname: "localhost/Historical (2022)",
      live: false,
    },
    metadata: [
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "DispCreated",
          dim: "[Vw_Transaction_tbl.DispCreated]",
          datatype: "text",
          merged: true,
          title: "Created_Date",
        },
        instanceid: "B9C37-D01A-A3",
        disabled: false,
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "DispUpdated",
          dim: "[Vw_Transaction_tbl.DispUpdated]",
          datatype: "text",
          merged: true,
          title: "Updated_Date",
        },
        instanceid: "A5A55-BF06-AC",
        disabled: false,
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "Service_Number",
          dim: "[Vw_Transaction_Campaigntbl.Service_Number]",
          datatype: "text",
          merged: true,
          title: "Service_Number",
        },
        instanceid: "0AF6B-BA87-72",
        disabled: false,
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Original_Amt",
          dim: "[Vw_Transaction_tbl.Original_Amt]",
          datatype: "numeric",
          title: "Original_Amt",
        },
        instanceid: "06ABC-7EAD-30",
        disabled: false,
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Refund_Amt",
          dim: "[Vw_Transaction_tbl.Refund_Amt]",
          datatype: "numeric",
          title: "Refund_Amt",
        },
        instanceid: "6EF60-3A7D-2C",
        disabled: false,
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Final_Amt",
          dim: "[Vw_Transaction_tbl.Final_Amt]",
          datatype: "numeric",
          title: "Final_Amt",
        },
        instanceid: "890F2-BEC2-07",
        disabled: false,
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Currency",
          dim: "[Vw_Transaction_tbl.Currency]",
          datatype: "text",
          merged: true,
          title: "Currency",
        },
        instanceid: "7FDE6-23EB-A1",
        disabled: false,
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Country",
          dim: "[Vw_Transaction_tbl.Country]",
          datatype: "text",
          merged: true,
          title: "Country",
        },
        instanceid: "55A4D-2B8E-1E",
        disabled: false,
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Billable",
          dim: "[Vw_Transaction_tbl.Billable]",
          datatype: "text",
          merged: true,
          title: "Billable",
        },
        instanceid: "1C6AF-BEA6-B9",
        disabled: false,
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Platform",
          dim: "[Vw_Transaction_tbl.Platform]",
          datatype: "text",
          merged: true,
          title: "Platform",
        },
        instanceid: "77A1A-F892-99",
        disabled: false,
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "Cust_Number",
          dim: "[Vw_Transaction_Campaigntbl.Cust_Number]",
          datatype: "text",
          merged: true,
          title: "Cust_Number",
        },
        instanceid: "68E76-EC38-FB",
        disabled: false,
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "r_code",
          dim: "[Vw_Transaction_tbl.r_code]",
          datatype: "numeric",
          title: "Rcode",
        },
        instanceid: "CAEF1-23D8-4B",
        disabled: false,
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "Integrator_Name",
          dim: "[Vw_Transaction_Campaigntbl.Integrator_Name]",
          datatype: "text",
          merged: true,
          title: "Integrator_Name",
        },
        instanceid: "E7D55-6131-85",
        disabled: false,
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "Program_Name",
          dim: "[Vw_Transaction_Campaigntbl.Program_Name]",
          datatype: "text",
          merged: true,
          title: "Program_Name",
        },
        instanceid: "3016D-00F2-1E",
        disabled: false,
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "Billing_Name",
          dim: "[Vw_Transaction_Campaigntbl.Billing_Name]",
          datatype: "text",
          merged: true,
          title: "Billing_Name",
        },
        instanceid: "C40E7-C343-71",
        disabled: false,
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Transaction_ID",
          dim: "[Vw_Transaction_tbl.Transaction_ID]",
          datatype: "text",
          merged: true,
          title: "Transaction_ID",
        },
        instanceid: "87DDD-B8EB-CF",
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Receipt_ID",
          dim: "[Vw_Transaction_tbl.Receipt_ID]",
          datatype: "text",
          merged: true,
          title: "Receipt_ID",
        },
        instanceid: "54711-182B-7A",
        disabled: false,
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Interac_Ref",
          dim: "[Vw_Transaction_tbl.Interac_Ref]",
          datatype: "text",
          merged: true,
          title: "Interac_Ref",
        },
        instanceid: "7AD23-E85C-19",
        disabled: false,
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "FI_Name",
          dim: "[Vw_Transaction_tbl.FI_Name]",
          datatype: "text",
          merged: true,
          title: "FI_Name",
        },
        instanceid: "07685-E7F3-ED",
        disabled: false,
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "status",
          dim: "[Vw_Transaction_tbl.status]",
          datatype: "text",
          merged: true,
          title: "status",
        },
        instanceid: "CB6E4-742B-6E",
        disabled: false,
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "sandbox",
          dim: "[Vw_Transaction_tbl.sandbox]",
          datatype: "text",
          merged: true,
          filter: {
            explicit: false,
            multiSelection: true,
            exclude: {
              members: ["1", "true"],
            },
          },
          collapsed: true,
          title: "sandbox",
          datasource: {
            address: "LocalHost",
            title: "Historical (2022)",
            id: "localhost_aHistoricalIAAaKAAa2022KQAa",
            database: "aHistoricalIAAaKAAa2022KQAa",
            fullname: "localhost/Historical (2022)",
            live: false,
          },
        },
        instanceid: "49ACB-BAD0-3E",
        panel: "scope",
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_Dates",
          column: "date",
          dim: "[Vw_Transaction_Dates.date (Calendar)]",
          datatype: "datetime",
          merged: true,
          level: "days",
          filter: {
            from: fromDateOnlyToString(fromDate),
            to: fromDateOnlyToString(toDate),
          },
          title: "Date",
          datasource: {
            address: "LocalHost",
            title: "Historical (2022)",
            id: "localhost_aHistoricalIAAaKAAa2022KQAa",
            database: "aHistoricalIAAaKAAa2022KQAa",
            fullname: "localhost/Historical (2022)",
            live: false,
          },
          collapsed: true,
        },
        instanceid: "E2CED-E391-D7",
        format: {
          mask: {
            years: "yyyy",
            quarters: "yyyy Q",
            months: "MM/yyyy",
            weeks: "ww yyyy",
            days: "shortDate",
            minutes: "HH:mm",
            seconds: "MM/dd/yyyy HH:mm:ss",
            dateAndTime: "MM/dd/yyyy HH:mm",
            isdefault: true,
          },
        },
        panel: "scope",
        handlers: [{}],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Platform",
          dim: "[Vw_Transaction_tbl.Platform]",
          datatype: "text",
          merged: true,
          collapsed: true,
          title: "Platform",
          isDashboardFilter: true,
          datasource: {
            fullname: "localhost/Historical (2022)",
            id: "localhost_aHistoricalIAAaKAAa2022KQAa",
            address: "LocalHost",
            database: "aHistoricalIAAaKAAa2022KQAa",
            live: false,
            title: "Historical (2022)",
          },
          filter: {
            explicit: false,
            multiSelection: true,
            all: true,
          },
        },
        disabled: false,
        isCascading: false,
        instanceid: "0EB07-C67B-9D",
        panel: "scope",
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Transaction_ID",
          dim: "[Vw_Transaction_tbl.Transaction_ID]",
          datatype: "text",
          merged: true,
          title: "Transaction_ID",
          collapsed: true,
          isDashboardFilter: true,
          datasource: {
            fullname: "localhost/Historical (2022)",
            id: "localhost_aHistoricalIAAaKAAa2022KQAa",
            address: "LocalHost",
            database: "aHistoricalIAAaKAAa2022KQAa",
            live: false,
            title: "Historical (2022)",
          },
          filter: {
            explicit: false,
            multiSelection: true,
            all: true,
          },
        },
        disabled: false,
        isCascading: false,
        instanceid: "4FDA8-1F04-A3",
        panel: "scope",
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "Integrator_Name",
          dim: "[Vw_Transaction_Campaigntbl.Integrator_Name]",
          datatype: "text",
          merged: true,
          title: "Integrator Name",
          datasource: {
            fullname: "localhost/Historical (2022)",
            id: "localhost_aHistoricalIAAaKAAa2022KQAa",
            address: "LocalHost",
            database: "aHistoricalIAAaKAAa2022KQAa",
            live: false,
            title: "Historical (2022)",
          },
          collapsed: true,
          isDashboardFilter: true,
          panel: "scope",
          filter: {
            explicit: false,
            multiSelection: true,
            all: true,
          },
        },
        disabled: false,
        isCascading: true,
        instanceid: "832C4-BFC2-A7",
        panel: "scope",
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "Billing_Name",
          dim: "[Vw_Transaction_Campaigntbl.Billing_Name]",
          datatype: "text",
          merged: true,
          title: "Billing Name",
          datasource: {
            fullname: "localhost/Historical (2022)",
            id: "localhost_aHistoricalIAAaKAAa2022KQAa",
            address: "LocalHost",
            database: "aHistoricalIAAaKAAa2022KQAa",
            live: false,
            title: "Historical (2022)",
          },
          collapsed: true,
          isDashboardFilter: true,
          panel: "scope",
          filter: {
            explicit: false,
            multiSelection: true,
            all: true,
          },
        },
        disabled: false,
        isCascading: true,
        instanceid: "256B0-B1FB-FD",
        rootInstanceId: "832C4-BFC2-A7",
        panel: "scope",
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "Program_Name",
          dim: "[Vw_Transaction_Campaigntbl.Program_Name]",
          datatype: "text",
          merged: true,
          title: "Program Name",
          datasource: {
            fullname: "localhost/Historical (2022)",
            id: "localhost_aHistoricalIAAaKAAa2022KQAa",
            address: "LocalHost",
            database: "aHistoricalIAAaKAAa2022KQAa",
            live: false,
            title: "Historical (2022)",
          },
          collapsed: true,
          isDashboardFilter: true,
          filter: {
            explicit: false,
            multiSelection: true,
            all: true,
          },
        },
        disabled: false,
        isCascading: true,
        instanceid: "6A989-CDE0-BD",
        rootInstanceId: "832C4-BFC2-A7",
        panel: "scope",
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "status",
          dim: "[Vw_Transaction_tbl.status]",
          datatype: "text",
          merged: true,
          collapsed: true,
          title: "status",
          datasource: {
            fullname: "localhost/Historical (2022)",
            id: "localhost_aHistoricalIAAaKAAa2022KQAa",
            address: "LocalHost",
            database: "aHistoricalIAAaKAAa2022KQAa",
            live: false,
            title: "Historical (2022)",
          },
          isDashboardFilter: true,
          filter: {
            explicit: false,
            multiSelection: true,
            all: true,
          },
        },
        isCascading: false,
        instanceid: "F4037-F8DA-B1",
        panel: "scope",
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "Service_Number",
          dim: "[Vw_Transaction_Campaigntbl.Service_Number]",
          datatype: "text",
          merged: true,
          title: "Service_Number",
          collapsed: true,
          isDashboardFilter: true,
          datasource: {
            fullname: "localhost/Historical (2022)",
            id: "localhost_aHistoricalIAAaKAAa2022KQAa",
            address: "LocalHost",
            database: "aHistoricalIAAaKAAa2022KQAa",
            live: false,
            title: "Historical (2022)",
          },
          filter: {
            explicit: true,
            multiSelection: true,
            members: [serviceNumber],
          },
        },
        disabled: false,
        isCascading: false,
        instanceid: "213E1-2647-65",
        panel: "scope",
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "USER_NAME",
          dim: "[Vw_Transaction_tbl.USER_NAME]",
          datatype: "text",
          merged: true,
          title: "USER_NAME",
          collapsed: true,
          isDashboardFilter: true,
          datasource: {
            fullname: "localhost/Historical (2022)",
            id: "localhost_aHistoricalIAAaKAAa2022KQAa",
            address: "LocalHost",
            database: "aHistoricalIAAaKAAa2022KQAa",
            live: false,
            title: "Historical (2022)",
          },
          filter: {
            explicit: false,
            multiSelection: true,
            all: true,
          },
        },
        disabled: false,
        isCascading: false,
        instanceid: "4682D-2C3B-90",
        panel: "scope",
      },
    ],
    ungroup: true,
    offset: 0,
    m2mThresholdFlag: 0,
    isMaskedResult: true,
    format: "json",
    widgetType: "tablewidget",
    by: "widget",
    dashboard: "677d5fb647691b0033b03d8d;Historical JAQLs",
  };

  return jaql;
};

export { getTransactionsJaql };
