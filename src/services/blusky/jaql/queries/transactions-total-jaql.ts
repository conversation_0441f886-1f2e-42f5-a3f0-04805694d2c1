import {
  fromDateOnlyToString,
  type DateOnly,
} from "../../../../utils/date-only";

const getTransactionTotalAmountJaql = (
  serviceNumbers: string[],
  fromDate: DateOnly,
  toDate: DateOnly
): Record<string, unknown> => {
  const jaql = {
    datasource: {
      fullname: "localhost/BluSkyProd",
      id: "localhost_aBluSkyProd",
      address: "LocalHost",
      database: "aBluSkyProd",
      live: false,
      title: "BluSkyProd",
    },
    metadata: [
      {
        jaql: {
          table: "platformPivot",
          column: "type",
          dim: "[platformPivot.type]",
          datatype: "text",
          merged: true,
          title: "type",
        },
        panel: "columns",
        field: {
          id: "[platformPivot.type]",
          index: 0,
        },
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Final_Amt",
          dim: "[Vw_Transaction_tbl.Final_Amt]",
          datatype: "numeric",
          agg: "sum",
          title: "Total Final_Amt",
        },
        format: {
          mask: {
            type: "number",
            t: true,
            b: true,
            separated: true,
            decimals: "auto",
            isdefault: true,
          },
          color: {
            type: "color",
            color: "transparent",
          },
        },
        panel: "measures",
        field: {
          id: "[Vw_Transaction_tbl.Final_Amt]_sum",
          index: 1,
        },
        handlers: [{}, {}],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "status",
          dim: "[Vw_Transaction_tbl.status]",
          datatype: "text",
          merged: true,
          filter: {
            explicit: true,
            multiSelection: true,
            members: ["STATUS_SUCCESS"],
          },
          collapsed: true,
          title: "status",
          datasource: {
            fullname: "localhost/BluSkyProd",
            id: "localhost_aBluSkyProd",
            address: "LocalHost",
            database: "aBluSkyProd",
            live: false,
            title: "BluSkyProd",
          },
        },
        panel: "scope",
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "sandbox",
          dim: "[Vw_Transaction_tbl.sandbox]",
          datatype: "text",
          merged: true,
          filter: {
            explicit: false,
            multiSelection: true,
            exclude: {
              members: ["true"],
            },
          },
          collapsed: true,
          title: "sandbox",
          datasource: {
            fullname: "localhost/BluSkyProd",
            id: "localhost_aBluSkyProd",
            address: "LocalHost",
            database: "aBluSkyProd",
            live: false,
            title: "BluSkyProd",
          },
        },
        panel: "scope",
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "updatedAt",
          dim: "[Vw_Transaction_tbl.updatedAt (Calendar)]",
          datatype: "datetime",
          merged: true,
          level: "days",
          filter: {
            from: fromDateOnlyToString(fromDate),
            to: fromDateOnlyToString(toDate),
          },
          title: "Days in updatedAt",
          datasource: {
            fullname: "localhost/BluSkyProd",
            id: "localhost_aBluSkyProd",
            address: "LocalHost",
            database: "aBluSkyProd",
            live: false,
            title: "BluSkyProd",
          },
          collapsed: true,
        },
        panel: "scope",
        format: {
          mask: {
            years: "yyyy",
            quarters: "yyyy Q",
            months: "MM/yyyy",
            weeks: "ww yyyy",
            days: "shortDate",
            minutes: "HH:mm",
            isdefault: true,
          },
        },
        handlers: [{}],
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "Service_Number",
          dim: "[Vw_Transaction_Campaigntbl.Service_Number]",
          datatype: "text",
          merged: true,
          filter: {
            explicit: true,
            multiSelection: true,
            members: serviceNumbers,
          },
          title: "Service_Number",
          collapsed: true,
          datasource: {
            fullname: "localhost/BluSkyProd",
            id: "localhost_aBluSkyProd",
            address: "LocalHost",
            database: "aBluSkyProd",
            live: false,
            title: "BluSkyProd",
          },
        },
        panel: "scope",
        handlers: [],
      },
      {
        jaql: {
          table: "platformPivot",
          column: "type",
          dim: "[platformPivot.type]",
          datatype: "text",
          merged: true,
          title: "type",
          filter: {
            explicit: false,
            multiSelection: true,
            all: true,
          },
          collapsed: true,
          datasource: {
            fullname: "localhost/BluSkyProd",
            id: "localhost_aBluSkyProd",
            address: "LocalHost",
            database: "aBluSkyProd",
            live: false,
            title: "BluSkyProd",
          },
        },
        panel: "scope",
        title: "type",
        handlers: [],
      },
    ],
    ungroup: true,
    count: 0,
    offset: 0,
    m2mThresholdFlag: 0,
    isMaskedResult: true,
    format: "json",
  };

  return jaql;
};

export { getTransactionTotalAmountJaql };
