const getFirstTransactionDateJaql = (serviceNumber: string) => {
  return {
    datasource: {
      fullname: "localhost/BluSkyProd",
      id: "localhost_aBluSkyProd",
      address: "LocalHost",
      database: "aBluSkyProd",
      live: false,
      title: "BluSkyProd",
    },
    metadata: [
      {
        jaql: {
          table: "FirstTransactionServiceNumber",
          column: "Column",
          dim: "[FirstTransactionServiceNumber.Column (Calendar)]",
          datatype: "datetime",
          merged: true,
          level: "days",
          title: "FirstTransaction",
        },
        field: {
          id: "[FirstTransactionServiceNumber.Column (Calendar)]_count_days",
          index: 0,
        },
        hierarchies: ["calendar", "calendar - weeks"],
        format: {
          mask: {
            years: "yyyy",
            quarters: "yyyy Q",
            months: "MM/yyyy",
            weeks: "ww yyyy",
            days: "yyyy-MM-dd",
            minutes: "HH:mm",
          },
        },
        handlers: [{}],
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "Service_Number",
          dim: "[Vw_Transaction_Campaigntbl.Service_Number]",
          datatype: "text",
          merged: true,
          title: "ServiceNumber",
        },
        field: {
          id: "[Vw_Transaction_Campaigntbl.Service_Number]",
          index: 2,
        },
        format: {
          mask: {
            type: "number",
            t: true,
            b: true,
            separated: true,
            decimals: "auto",
            isdefault: true,
          },
        },
        handlers: [],
      },
      {
        jaql: {
          table: "FirstTransactionServiceNumber",
          column: "Service_Number",
          dim: "[FirstTransactionServiceNumber.Service_Number]",
          datatype: "text",
          merged: true,
          filter: {
            explicit: true,
            multiSelection: true,
            members: [serviceNumber],
          },
          title: "Service_Number",
          datasource: {
            fullname: "localhost/BluSkyProd",
            id: "localhost_aBluSkyProd",
            address: "LocalHost",
            database: "aBluSkyProd",
            live: false,
            title: "BluSkyProd",
          },
          collapsed: true,
        },
        panel: "scope",
        handlers: [],
      },
    ],
    ungroup: true,
    count: 1,
    offset: 0,
    m2mThresholdFlag: 0,
    isMaskedResult: true,
    format: "json",
    widgetType: "tablewidget",
    by: "widget",
  };
};

export { getFirstTransactionDateJaql };
