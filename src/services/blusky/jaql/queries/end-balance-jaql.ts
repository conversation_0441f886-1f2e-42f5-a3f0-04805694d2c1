/* eslint-disable @typescript-eslint/naming-convention */
const getEndBalanceJaql = (
  serviceNumbers: string[]
): Record<string, unknown> => {
  return {
    datasource: {
      fullname: "localhost/FinanceModel",
      id: "localhost_aFinanceModel",
      address: "LocalHost",
      database: "aFinanceModel",
      live: false,
      title: "FinanceModel",
    },
    format: "pivot",
    grandTotals: {
      title: "Grand Total",
    },
    metadata: [
      {
        jaql: {
          table: "customer",
          column: "customerName",
          dim: "[customer.customerName]",
          datatype: "text",
          merged: true,
          title: "customerName",
        },
        instanceid: "F9DBE-BBD0-37",
        PanelName: "customerName",
        disabled: false,
        panel: "rows",
        field: {
          id: "[customer.customerName]",
          index: 0,
        },
        handlers: [],
      },
      {
        jaql: {
          table: "customer",
          column: "serviceNumber",
          dim: "[customer.serviceNumber]",
          datatype: "text",
          merged: true,
          title: "serviceNumber",
        },
        instanceid: "CB873-0B3F-11",
        panel: "rows",
        field: {
          id: "[customer.serviceNumber]",
          index: 1,
        },
        handlers: [],
      },
      {
        jaql: {
          type: "measure",
          formula: "([03984-6E3],[45539-6AE])",
          context: {
            "[03984-6E3]": {
              table: "vw_CustomerSettlementCurrentBalance",
              column: "EndBalance",
              dim: "[vw_CustomerSettlementCurrentBalance.EndBalance]",
              datatype: "numeric",
              agg: "sum",
              title: "Total EndBalance",
            },
            "[45539-6AE]": {
              table: "vw_CustomerSettlementCurrentBalance",
              column: "platformid",
              dim: "[vw_CustomerSettlementCurrentBalance.platformid]",
              datatype: "numeric",
              filter: {
                explicit: true,
                multiSelection: true,
                members: ["1"],
              },
              collapsed: true,
              title: "platformid",
              datasource: {
                fullname: "localhost/FinanceModel",
                id: "localhost_aFinanceModel",
                address: "LocalHost",
                database: "aFinanceModel",
                live: false,
                title: "FinanceModel",
              },
            },
          },
          title: "Previous Total",
          datatype: "numeric",
        },
        PanelName: "Previous Total",
        format: {
          mask: {
            abbreviations: {
              t: false,
              b: false,
              m: false,
              k: false,
            },
            decimals: 2,
            number: {
              separated: true,
            },
          },
          color: {
            type: "color",
            color: "transparent",
          },
        },
        disabled: false,
        instanceid: "59BD0-E119-EC",
        panel: "measures",
        field: {
          id: "([03984-6E3],[45539-6AE])",
          index: 2,
        },
        handlers: [{}, {}],
      },
      {
        jaql: {
          type: "measure",
          formula:
            "([FFFE9-1F2],[25A35-C16])+\n([FFFE9-1F2],[356AB-BC4])\n+(-([BCBE3-DE9]-([7AD3F-352])-([28E27-22C])))+\n([1C375-411],[7FD1B-4D8])+\n([1C375-411],[CF039-983])+\n([0378E-09B],[174DA-70E])",
          context: {
            "[1C375-411]": {
              table: "vw_CustomerSettlementCurrentBalance",
              column: "transactionamount",
              dim: "[vw_CustomerSettlementCurrentBalance.transactionamount]",
              datatype: "numeric",
              agg: "sum",
              title: "Total transactionamount",
            },
            "[7AD3F-352]": {
              table: "Totals",
              column: "transactionFees",
              dim: "[Totals.transactionFees]",
              datatype: "numeric",
              agg: "sum",
              title: "Total transactionFees",
            },
            "[28E27-22C]": {
              table: "Totals",
              column: "gatewayFees",
              dim: "[Totals.gatewayFees]",
              datatype: "numeric",
              agg: "sum",
              title: "Total gatewayFees",
            },
            "[0378E-09B]": {
              table: "vw_CustomerSettlementCurrentBalance",
              column: "EndBalance",
              dim: "[vw_CustomerSettlementCurrentBalance.EndBalance]",
              datatype: "numeric",
              agg: "sum",
              title: "Total EndBalance",
            },
            "[174DA-70E]": {
              table: "vw_CustomerSettlementCurrentBalance",
              column: "platformid",
              dim: "[vw_CustomerSettlementCurrentBalance.platformid]",
              datatype: "numeric",
              filter: {
                explicit: true,
                multiSelection: true,
                members: ["1"],
              },
              collapsed: true,
              title: "platformid",
              datasource: {
                fullname: "localhost/FinanceModel",
                id: "localhost_aFinanceModel",
                address: "LocalHost",
                database: "aFinanceModel",
                live: false,
                title: "FinanceModel",
              },
            },
            "[CF039-983]": {
              table: "vw_CustomerSettlementCurrentBalance",
              column: "platformid",
              dim: "[vw_CustomerSettlementCurrentBalance.platformid]",
              datatype: "numeric",
              filter: {
                explicit: true,
                multiSelection: true,
                members: ["9"],
              },
              collapsed: true,
              title: "platformid",
              datasource: {
                fullname: "localhost/FinanceModel",
                id: "localhost_aFinanceModel",
                address: "LocalHost",
                database: "aFinanceModel",
                live: false,
                title: "FinanceModel",
              },
            },
            "[FFFE9-1F2]": {
              table: "Totals",
              column: "total",
              dim: "[Totals.total]",
              datatype: "numeric",
              agg: "sum",
              title: "Total total",
            },
            "[7FD1B-4D8]": {
              table: "vw_CustomerSettlementCurrentBalance",
              column: "platformid",
              dim: "[vw_CustomerSettlementCurrentBalance.platformid]",
              datatype: "numeric",
              filter: {
                explicit: true,
                multiSelection: true,
                members: ["8"],
              },
              collapsed: true,
              title: "platformid",
              datasource: {
                fullname: "localhost/FinanceModel",
                id: "localhost_aFinanceModel",
                address: "LocalHost",
                database: "aFinanceModel",
                live: false,
                title: "FinanceModel",
              },
            },
            "[BCBE3-DE9]": {
              table: "Totals",
              column: "fees",
              dim: "[Totals.fees]",
              datatype: "numeric",
              title: "Total fees",
              agg: "sum",
            },
            "[356AB-BC4]": {
              table: "Totals",
              column: "Platform",
              dim: "[Totals.Platform]",
              datatype: "text",
              merged: true,
              filter: {
                explicit: true,
                multiSelection: true,
                members: ["ETO", "ACH", "RTO", "RTX", "ANR", "ANX"],
              },
              collapsed: true,
              title: "Platform",
              datasource: {
                fullname: "localhost/FinanceModel",
                id: "localhost_aFinanceModel",
                address: "LocalHost",
                database: "aFinanceModel",
                live: false,
                title: "FinanceModel",
              },
            },
            "[25A35-C16]": {
              table: "Totals",
              column: "Platform",
              dim: "[Totals.Platform]",
              datatype: "text",
              merged: true,
              filter: {
                explicit: true,
                multiSelection: true,
                members: ["ETI", "IDP", "RFM", "ETF"],
                rankingMessage: "",
                custom: true,
              },
              collapsed: true,
              title: "Platform",
              datasource: {
                fullname: "localhost/FinanceModel",
                id: "localhost_aFinanceModel",
                address: "LocalHost",
                database: "aFinanceModel",
                live: false,
                title: "FinanceModel",
              },
            },
          },
          title: "Current Balance",
          datatype: "numeric",
        },
        PanelName: "Current Balance",
        format: {
          mask: {
            abbreviations: {
              t: false,
              b: false,
              m: false,
              k: false,
            },
            decimals: 2,
            number: {
              separated: true,
            },
          },
          color: {
            type: "color",
            color: "transparent",
          },
        },
        disabled: false,
        instanceid: "26A38-426B-68",
        panel: "measures",
        field: {
          id: "([FFFE9-1F2],[25A35-C16])+\n([FFFE9-1F2],[356AB-BC4])\n+(-([BCBE3-DE9]-([7AD3F-352])-([28E27-22C])))+\n([1C375-411],[7FD1B-4D8])+\n([1C375-411],[CF039-983])+\n([0378E-09B],[174DA-70E])",
          index: 3,
        },
        handlers: [{}, {}],
      },
      {
        jaql: {
          type: "measure",
          formula:
            "([2F975-C3A],[918E8-DCE])+\n([2F975-C3A],[E5526-B07])+\n(-([CDFD3-896]-([F234F-2D7])-([34ADC-FF6])))+\n([A861A-4A0],[E2732-B32])+\n([A861A-4A0],[FAAE6-E36])+\n([9B6BA-327],[71B37-544])+\n([A861A-4A0],[7AD9D-F5D])",
          context: {
            "[A861A-4A0]": {
              table: "vw_CustomerSettlementCurrentBalance",
              column: "transactionamount",
              dim: "[vw_CustomerSettlementCurrentBalance.transactionamount]",
              datatype: "numeric",
              agg: "sum",
              title: "Total transactionamount",
            },
            "[F234F-2D7]": {
              table: "Totals",
              column: "transactionFees",
              dim: "[Totals.transactionFees]",
              datatype: "numeric",
              agg: "sum",
              title: "Total transactionFees",
            },
            "[34ADC-FF6]": {
              table: "Totals",
              column: "gatewayFees",
              dim: "[Totals.gatewayFees]",
              datatype: "numeric",
              agg: "sum",
              title: "Total gatewayFees",
            },
            "[9B6BA-327]": {
              table: "vw_CustomerSettlementCurrentBalance",
              column: "EndBalance",
              dim: "[vw_CustomerSettlementCurrentBalance.EndBalance]",
              datatype: "numeric",
              agg: "sum",
              title: "Total EndBalance",
            },
            "[7AD9D-F5D]": {
              table: "vw_CustomerSettlementCurrentBalance",
              column: "platformid",
              dim: "[vw_CustomerSettlementCurrentBalance.platformid]",
              datatype: "numeric",
              filter: {
                explicit: true,
                multiSelection: true,
                members: ["12"],
              },
              collapsed: true,
              title: "platformid",
              datasource: {
                fullname: "localhost/FinanceModel",
                id: "localhost_aFinanceModel",
                address: "LocalHost",
                database: "aFinanceModel",
                live: false,
                title: "FinanceModel",
              },
            },
            "[71B37-544]": {
              table: "vw_CustomerSettlementCurrentBalance",
              column: "platformid",
              dim: "[vw_CustomerSettlementCurrentBalance.platformid]",
              datatype: "numeric",
              filter: {
                explicit: true,
                multiSelection: true,
                members: ["1"],
              },
              collapsed: true,
              title: "platformid",
              datasource: {
                fullname: "localhost/FinanceModel",
                id: "localhost_aFinanceModel",
                address: "LocalHost",
                database: "aFinanceModel",
                live: false,
                title: "FinanceModel",
              },
            },
            "[FAAE6-E36]": {
              table: "vw_CustomerSettlementCurrentBalance",
              column: "platformid",
              dim: "[vw_CustomerSettlementCurrentBalance.platformid]",
              datatype: "numeric",
              filter: {
                explicit: true,
                multiSelection: true,
                members: ["9"],
              },
              collapsed: true,
              title: "platformid",
              datasource: {
                fullname: "localhost/FinanceModel",
                id: "localhost_aFinanceModel",
                address: "LocalHost",
                database: "aFinanceModel",
                live: false,
                title: "FinanceModel",
              },
            },
            "[2F975-C3A]": {
              table: "Totals",
              column: "total",
              dim: "[Totals.total]",
              datatype: "numeric",
              agg: "sum",
              title: "Total total",
            },
            "[E2732-B32]": {
              table: "vw_CustomerSettlementCurrentBalance",
              column: "platformid",
              dim: "[vw_CustomerSettlementCurrentBalance.platformid]",
              datatype: "numeric",
              filter: {
                explicit: true,
                multiSelection: true,
                members: ["8"],
              },
              collapsed: true,
              title: "platformid",
              datasource: {
                fullname: "localhost/FinanceModel",
                id: "localhost_aFinanceModel",
                address: "LocalHost",
                database: "aFinanceModel",
                live: false,
                title: "FinanceModel",
              },
            },
            "[CDFD3-896]": {
              table: "Totals",
              column: "fees",
              dim: "[Totals.fees]",
              datatype: "numeric",
              title: "Total fees",
              agg: "sum",
            },
            "[E5526-B07]": {
              table: "Totals",
              column: "Platform",
              dim: "[Totals.Platform]",
              datatype: "text",
              merged: true,
              filter: {
                explicit: true,
                multiSelection: true,
                members: ["ETO", "ACH", "RTO", "RTX", "ANR", "ANX"],
              },
              collapsed: true,
              title: "Platform",
              datasource: {
                fullname: "localhost/FinanceModel",
                id: "localhost_aFinanceModel",
                address: "LocalHost",
                database: "aFinanceModel",
                live: false,
                title: "FinanceModel",
              },
            },
            "[918E8-DCE]": {
              table: "Totals",
              column: "Platform",
              dim: "[Totals.Platform]",
              datatype: "text",
              merged: true,
              filter: {
                explicit: true,
                multiSelection: true,
                members: ["ETI", "IDP", "RFM", "ETF"],
                rankingMessage: "",
                custom: true,
              },
              collapsed: true,
              title: "Platform",
              datasource: {
                fullname: "localhost/FinanceModel",
                id: "localhost_aFinanceModel",
                address: "LocalHost",
                database: "aFinanceModel",
                live: false,
                title: "FinanceModel",
              },
            },
          },
          title: "Total Balance",
          datatype: "numeric",
        },
        PanelName: "Total Balance",
        format: {
          mask: {
            type: "number",
            t: true,
            b: true,
            separated: true,
            decimals: "auto",
            isdefault: true,
          },
          color: {
            type: "color",
            color: "transparent",
          },
        },
        instanceid: "F961C-2E9F-98",
        panel: "measures",
        field: {
          id: "([2F975-C3A],[918E8-DCE])+\n([2F975-C3A],[E5526-B07])+\n(-([CDFD3-896]-([F234F-2D7])-([34ADC-FF6])))+\n([A861A-4A0],[E2732-B32])+\n([A861A-4A0],[FAAE6-E36])+\n([9B6BA-327],[71B37-544])+\n([A861A-4A0],[7AD9D-F5D])",
          index: 4,
        },
        handlers: [{}, {}],
      },
      {
        jaql: {
          table: "customer",
          column: "serviceNumber",
          dim: "[customer.serviceNumber]",
          datatype: "text",
          merged: true,
          title: "serviceNumber",
          collapsed: true,
          isDashboardFilter: true,
          datasource: {
            fullname: "localhost/FinanceModel",
            id: "localhost_aFinanceModel",
            address: "LocalHost",
            database: "aFinanceModel",
            live: false,
            title: "FinanceModel",
          },
          filter: {
            explicit: true,
            multiSelection: true,
            members: serviceNumbers,
          },
        },
        disabled: false,
        isCascading: false,
        instanceid: "FE5B0-DA46-7C",
        panel: "scope",
      },
    ],
    m2mThresholdFlag: 0,
    isMaskedResult: true,
    widgetType: "pivot2",
    by: "widget",
    dashboard: "65b1653b2b6ef80033df550a;Current Breakdown (1)",
  };
};

export { getEndBalanceJaql };
