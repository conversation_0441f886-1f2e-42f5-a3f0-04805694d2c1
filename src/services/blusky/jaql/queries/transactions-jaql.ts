import {
  fromDateOnlyToString,
  type DateOnly,
} from "../../../../utils/date-only";

const getTransactionsJaql = (
  serviceNumber: string,
  fromDate: DateOnly,
  toDate: DateOnly
) => {
  const jaql = {
    datasource: {
      fullname: "localhost/BluSkyProd",
      id: "localhost_aBluSkyProd",
      address: "LocalHost",
      database: "aBluSkyProd",
      live: false,
      title: "BluSkyProd",
    },
    metadata: [
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "DispCreated",
          dim: "[Vw_Transaction_tbl.DispCreated]",
          datatype: "text",
          merged: true,
          title: "Created_Date",
        },
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "DispUpdated",
          dim: "[Vw_Transaction_tbl.DispUpdated]",
          datatype: "text",
          merged: true,
          title: "Updated_Date",
        },
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "Service_Number",
          dim: "[Vw_Transaction_Campaigntbl.Service_Number]",
          datatype: "text",
          title: "Service_Number",
        },
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Original_Amt",
          dim: "[Vw_Transaction_tbl.Original_Amt]",
          datatype: "numeric",
          title: "Original_Amt",
        },
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Refund_Amt",
          dim: "[Vw_Transaction_tbl.Refund_Amt]",
          datatype: "numeric",
          title: "Refund_Amt",
        },
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Final_Amt",
          dim: "[Vw_Transaction_tbl.Final_Amt]",
          datatype: "numeric",
          title: "Final_Amt",
        },
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Currency",
          dim: "[Vw_Transaction_tbl.Currency]",
          datatype: "text",
          merged: true,
          title: "Currency",
        },
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Country",
          dim: "[Vw_Transaction_tbl.Country]",
          datatype: "text",
          merged: true,
          title: "Country",
        },
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Billable",
          dim: "[Vw_Transaction_tbl.Billable]",
          datatype: "text",
          merged: true,
          title: "Billable",
        },
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Platform",
          dim: "[Vw_Transaction_tbl.Platform]",
          datatype: "text",
          merged: true,
          title: "Platform",
        },
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "Cust_Number",
          dim: "[Vw_Transaction_Campaigntbl.Cust_Number]",
          datatype: "text",
          title: "Cust_Number",
        },
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "r_code",
          dim: "[Vw_Transaction_tbl.r_code]",
          datatype: "numeric",
          title: "Rcode",
        },
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "Integrator_Name",
          dim: "[Vw_Transaction_Campaigntbl.Integrator_Name]",
          datatype: "text",
          merged: true,
          title: "Integrator_Name",
        },
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "Program_Name",
          dim: "[Vw_Transaction_Campaigntbl.Program_Name]",
          datatype: "text",
          merged: true,
          title: "Program_Name",
        },
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "Billing_Name",
          dim: "[Vw_Transaction_Campaigntbl.Billing_Name]",
          datatype: "text",
          merged: true,
          title: "Billing_Name",
        },
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Transaction_ID",
          dim: "[Vw_Transaction_tbl.Transaction_ID]",
          datatype: "text",
          merged: true,
          title: "Transaction_ID",
        },
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Receipt_ID",
          dim: "[Vw_Transaction_tbl.Receipt_ID]",
          datatype: "text",
          title: "Receipt_ID",
        },
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Interac_Ref",
          dim: "[Vw_Transaction_tbl.Interac_Ref]",
          datatype: "text",
          title: "Interac_Ref",
        },
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "FI_Name",
          dim: "[Vw_Transaction_tbl.FI_Name]",
          datatype: "text",
          merged: true,
          title: "FI_Name",
        },
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "status",
          dim: "[Vw_Transaction_tbl.status]",
          datatype: "text",
          merged: true,
          title: "Status",
        },
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "sandbox",
          dim: "[Vw_Transaction_tbl.sandbox]",
          datatype: "text",
          filter: {
            explicit: false,
            multiSelection: true,
            exclude: {
              members: ["true"],
            },
          },
          collapsed: true,
          title: "sandbox",
          datasource: {
            fullname: "localhost/BluSkyProd",
            id: "localhost_aBluSkyProd",
            address: "LocalHost",
            database: "aBluSkyProd",
            live: false,
            title: "BluSkyProd",
          },
        },
        panel: "scope",
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_Dates",
          column: "Date",
          dim: "[Vw_Transaction_Dates.Date (Calendar)]",
          datatype: "datetime",
          merged: true,
          level: "days",
          collapsed: true,
          title: "Date",
          datasource: {
            fullname: "localhost/BluSkyProd",
            id: "localhost_aBluSkyProd",
            address: "LocalHost",
            database: "aBluSkyProd",
            live: false,
            title: "BluSkyProd",
          },
          filter: {
            from: fromDateOnlyToString(fromDate),
            to: fromDateOnlyToString(toDate),
          },
        },
        disabled: false,
        isCascading: false,
        panel: "scope",
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "Platform",
          dim: "[Vw_Transaction_tbl.Platform]",
          datatype: "text",
          merged: true,
          collapsed: true,
          title: "Platform",
          datasource: {
            fullname: "localhost/BluSkyProd",
            id: "localhost_aBluSkyProd",
            address: "LocalHost",
            database: "aBluSkyProd",
            live: false,
            title: "BluSkyProd",
          },
          filter: {
            explicit: false,
            multiSelection: true,
            all: true,
          },
        },
        disabled: false,
        isCascading: false,
        panel: "scope",
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "Integrator_Name",
          dim: "[Vw_Transaction_Campaigntbl.Integrator_Name]",
          datatype: "text",
          merged: true,
          title: "Integrator_Name",
          collapsed: true,
          panel: "scope",
          datasource: {
            fullname: "localhost/BluSkyProd",
            id: "localhost_aBluSkyProd",
            address: "LocalHost",
            database: "aBluSkyProd",
            live: false,
            title: "BluSkyProd",
          },
          filter: {
            explicit: true,
            multiSelection: true,
            members: [],
          },
        },
        disabled: false,
        isCascading: true,
        panel: "scope",
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "Billing_Name",
          dim: "[Vw_Transaction_Campaigntbl.Billing_Name]",
          datatype: "text",
          merged: true,
          title: "Billing_Name",
          datasource: {
            fullname: "localhost/BluSkyProd",
            id: "localhost_aBluSkyProd",
            address: "LocalHost",
            database: "aBluSkyProd",
            live: false,
            title: "BluSkyProd",
          },
          filter: {
            explicit: false,
            multiSelection: true,
            all: true,
          },
        },
        isCascading: true,
        panel: "scope",
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "Program_Name",
          dim: "[Vw_Transaction_Campaigntbl.Program_Name]",
          datatype: "text",
          merged: true,
          title: "Program_Name",
          collapsed: true,
          datasource: {
            fullname: "localhost/BluSkyProd",
            id: "localhost_aBluSkyProd",
            address: "LocalHost",
            database: "aBluSkyProd",
            live: false,
            title: "BluSkyProd",
          },
          filter: {
            explicit: false,
            multiSelection: true,
            all: true,
          },
        },
        disabled: false,
        isCascading: true,
        panel: "scope",
      },
      {
        jaql: {
          table: "Vw_Transaction_tbl",
          column: "status",
          dim: "[Vw_Transaction_tbl.status]",
          datatype: "text",
          merged: true,
          collapsed: true,
          title: "Status",
          datasource: {
            fullname: "localhost/BluSkyProd",
            id: "localhost_aBluSkyProd",
            address: "LocalHost",
            database: "aBluSkyProd",
            live: false,
            title: "BluSkyProd",
          },
          filter: {
            explicit: false,
            multiSelection: true,
            exclude: {
              members: ["STATUS_PENDING1"],
            },
          },
        },
        disabled: false,
        isCascading: false,
        panel: "scope",
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "Service_Number",
          dim: "[Vw_Transaction_Campaigntbl.Service_Number]",
          datatype: "text",
          merged: true,
          filter: {
            members: [serviceNumber],
          },
          title: "Service_Number",
          datasource: {
            fullname: "localhost/BluSkyProd",
            id: "localhost_aBluSkyProd",
            address: "LocalHost",
            database: "aBluSkyProd",
            live: false,
            title: "BluSkyProd",
          },
          collapsed: true,
          isDashboardFilter: true,
        },
        isCascading: false,
        panel: "scope",
      },
    ],
    ungroup: true,
    count: 0,
    offset: 0,
    m2mThresholdFlag: 0,
    isMaskedResult: true,
    format: "json",
  };

  return jaql;
};

export { getTransactionsJaql };
