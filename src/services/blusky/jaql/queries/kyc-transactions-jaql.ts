export const getKycTxnJaql = (
  serviceNumber: string,
  fromDate: string,
  toDate: string
) => {
  const jaql = {
    datasource: {
      fullname: "localhost/FinanceModel",
      id: "localhost_aFinanceModel",
      address: "LocalHost",
      database: "aFinanceModel",
      live: false,
      title: "FinanceModel",
    },
    metadata: [
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "SERVICE_NUMBER",
          dim: "[Vw_Transaction_Campaigntbl.SERVICE_NUMBER]",
          datatype: "text",
          merged: true,
          title: "ServiceNumber",
        },
        instanceid: "26702-F954-70",
        field: {
          id: "[Vw_Transaction_Campaigntbl.SERVICE_NUMBER]",
          index: 0,
        },
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_VerifyKycTracking (1)",
          column: "Name",
          dim: "[Vw_VerifyKycTracking (1).Name]",
          datatype: "text",
          title: "Name",
        },
        instanceid: "3EB57-560D-CB",
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_VerifyKycTracking (1)",
          column: "Email",
          dim: "[Vw_VerifyKycTracking (1).Email]",
          datatype: "text",
          title: "Email",
        },
        instanceid: "BC746-115B-0F",
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_VerifyKycTracking (1)",
          column: "Platform",
          dim: "[Vw_VerifyKycTracking (1).Platform]",
          datatype: "text",
          merged: true,
          title: "Type",
        },
        instanceid: "0ED6E-8CA6-7A",
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_VerifyKycTracking (1)",
          column: "clientUserId",
          dim: "[Vw_VerifyKycTracking (1).clientUserId]",
          datatype: "text",
          merged: true,
          title: "clientUserId",
        },
        instanceid: "7E398-17C2-A6",
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_VerifyKycTracking (1)",
          column: "status",
          dim: "[Vw_VerifyKycTracking (1).status]",
          datatype: "text",
          merged: true,
          title: "status",
        },
        instanceid: "C9AE9-C892-E0",
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_VerifyKycTracking (1)",
          column: "DispCreated",
          dim: "[Vw_VerifyKycTracking (1).DispCreated]",
          datatype: "text",
          merged: true,
          title: "DispCreated",
        },
        instanceid: "C49F1-902E-93",
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_VerifyKycTracking (1)",
          column: "DispUpdated",
          dim: "[Vw_VerifyKycTracking (1).DispUpdated]",
          datatype: "text",
          merged: true,
          title: "DispUpdated",
        },
        instanceid: "E6524-8DBE-A3",
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_Transaction_Campaigntbl",
          column: "SERVICE_NUMBER",
          dim: "[Vw_Transaction_Campaigntbl.SERVICE_NUMBER]",
          datatype: "text",
          merged: true,
          filter: {
            explicit: true,
            multiSelection: true,
            members: [serviceNumber],
          },
          title: "SERVICE_NUMBER",
          datasource: {
            fullname: "localhost/FinanceModel",
            id: "localhost_aFinanceModel",
            address: "LocalHost",
            database: "aFinanceModel",
            live: false,
            title: "FinanceModel",
          },
          collapsed: true,
        },
        instanceid: "4D5D9-BFE4-90",
        panel: "scope",
        handlers: [],
      },
      {
        jaql: {
          table: "Vw_VerifyKycTracking (1)",
          column: "createdAt",
          dim: "[Vw_VerifyKycTracking (1).createdAt (Calendar)]",
          datatype: "datetime",
          merged: true,
          level: "days",
          filter: {
            from: fromDate,
            to: toDate,
          },
          collapsed: true,
          title: "Days in createdAt",
          datasource: {
            fullname: "localhost/FinanceModel",
            id: "localhost_aFinanceModel",
            address: "LocalHost",
            database: "aFinanceModel",
            live: false,
            title: "FinanceModel",
          },
        },
        instanceid: "6BBC9-1C23-2A",
        format: {
          mask: {
            years: "yyyy",
            quarters: "yyyy Q",
            months: "MM/yyyy",
            weeks: "ww yyyy",
            days: "shortDate",
            minutes: "HH:mm",
            dateAndTime: "MM/dd/y HH:mm",
            isdefault: true,
          },
        },
        panel: "scope",
        handlers: [{}],
      },
      {
        jaql: {
          table: "Vw_VerifyKycTracking (1)",
          column: "billableMerchant",
          dim: "[Vw_VerifyKycTracking (1).billableMerchant]",
          datatype: "numeric",
          filter: {
            explicit: true,
            multiSelection: true,
            members: ["1"],
          },
          collapsed: true,
          title: "billableMerchant",
          datasource: {
            fullname: "localhost/FinanceModel",
            id: "localhost_aFinanceModel",
            address: "LocalHost",
            database: "aFinanceModel",
            live: false,
            title: "FinanceModel",
          },
        },
        instanceid: "08DDB-D077-A1",
        panel: "scope",
        handlers: [],
      },
    ],
    ungroup: true,
    count: 0,
    offset: 0,
    m2mThresholdFlag: 0,
    isMaskedResult: true,
    format: "json",
    widgetType: "tablewidget",
    by: "widget",
    dashboard: "638fbff9c928e4003655b973;FinanceModel",
  };

  return jaql;
};
