import { getEndBalance } from "./end-balance";
import * as fetchSqlData from "./sql/fetch-sql-data";

describe("end-balance", () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it("should get the end balance in the proper format", async () => {
    const spyFetchSqlData = vi
      .spyOn(fetchSqlData, "fetchSqlData")
      .mockResolvedValue([
        {
          customerName: "John Doe",
          serviceNumber: "12345",
          previousTotal: 1000,
          currentBalance: 200,
          totalBalance: 1200,
        },
        {
          customerName: "<PERSON>",
          serviceNumber: "67890",
          previousTotal: 1500,
          currentBalance: 300,
          totalBalance: 1800,
        },
        {
          customerName: "Alice Johnson",
          serviceNumber: "54321",
          previousTotal: 800,
          currentBalance: "abc",
          totalBalance: 900,
        },
      ]);

    const result = await getEndBalance(
      ["12345", "67890", "54321"],
      "test-token"
    );

    expect(spyFetchSqlData).toHaveBeenCalledWith(
      `
  SELECT 
    A.[CUSTOMERNAME] AS customerName,
    A.[SERVICE_NUMBER] AS serviceNumber,
    SUM(CASE WHEN A.[PLATFORM] = 'SUMMARY' THEN A.[LAST_SETTLEMENT_BALANCE] ELSE 0 END) AS [Previous Total],
    SUM(CASE WHEN A.[PLATFORM] = 'SUMMARY' THEN A.[TOTAL_BALANCE] ELSE 0 END) AS [Current Balance],
    SUM(CASE WHEN A.[PLATFORM] = 'SUMMARY' THEN A.[TOTAL_BALANCE] ELSE 0 END) +
    SUM(CASE WHEN A.[PLATFORM] = 'RESERVE' THEN A.[RESERVE] ELSE 0 END) AS [Total Balance]
  FROM [VW_MERCHANT_CURRENT_BALANCE] A
  WHERE 
    A.[SERVICE_NUMBER] IN ('12345', '67890', '54321') AND 
    A.[PLATFORM] IN ('SUMMARY')
  GROUP BY A.[CUSTOMERNAME], A.[SERVICE_NUMBER]
`,
      "test-token"
    );

    expect(result).toEqual({
      // eslint-disable-next-line @typescript-eslint/naming-convention
      "12345": {
        customerName: "John Doe",
        endBalance: 1000,
        currentBalance: 200,
        totalBalance: 1200,
      },
      // eslint-disable-next-line @typescript-eslint/naming-convention
      "67890": {
        customerName: "Jane Smith",
        endBalance: 1500,
        currentBalance: 300,
        totalBalance: 1800,
      },
      // eslint-disable-next-line @typescript-eslint/naming-convention
      "54321": {
        customerName: "Alice Johnson",
        endBalance: 800,
        currentBalance: Number.NaN,
        totalBalance: 900,
      },
    });
  });
});
