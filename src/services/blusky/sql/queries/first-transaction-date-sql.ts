const getFirstTransactionDateSql = (serviceNumber: string) => {
  return `
    SELECT
        LEFT(ToString(ToDatetime(MIN(f.[CREATED_DATE]))),10) as FirstTransaction,
        f.[SERVICE_NUMBER] as ServiceNumber
    FROM[transactions] f
    WHERE f.[SERVICE_NUMBER] in ('${serviceNumber}')
    AND STATUS = 'STATUS_SUCCESS'
    GROUP BY F.[SERVICE_NUMBER]
   `;
};

export { getFirstTransactionDateSql };
