import { formatDateOnlyWithCommas, type DateOnly } from "@utils/date-only";

const getTransactionsTotalSql = (
  serviceNumbers: string[],
  fromDate: DateOnly,
  toDate: DateOnly
) => {
  const formattedServiceNumbers = serviceNumbers
    .map((serviceNumber) => `'${serviceNumber}'`)
    .join(", ");

  return `
  SELECT 
    f.platform as type,
    SUM(f.[FINAL_AMT]) AS totalFinalAmount
    FROM [transactions] f
    WHERE STATUS IN ('STATUS_SUCCESS')
    AND f.[UPDATEDAT] BETWEEN createdate(${formatDateOnlyWithCommas(
      fromDate
    )}) AND createdate(${formatDateOnlyWithCommas(toDate)})
    AND f.[SERVICE_NUMBER] IN (${formattedServiceNumbers})
    GROUP BY f.[PLATFORM]
  `;
};

export { getTransactionsTotalSql };
