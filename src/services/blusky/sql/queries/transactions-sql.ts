import { type DateOnly, formatDateOnlyWithCommas } from "@utils/date-only";

const getTransactionsSql = (
  serviceNumber: string,
  fromDate: DateOnly,
  toDate: DateOnly
) => {
  return `
      SELECT
       A.[CREATED_DATE]    AS Created_Date,
       A.[UPDATED_DATE]    AS Updated_Date,
       A.[SERVICE_NUMBER]  AS Service_Number,
       A.[ORIGINAL_AMT]    AS Original_Amt,
       A.[REFUND_AMT]      AS Refund_Amt,
       A.[FINAL_AMT]       AS Final_Amt,
       A.[CURRENCY]        AS Currency,
       A.[COUNTRY]         AS Country,
       A.[BILLABLE]        AS Billable,
       A.[PLATFORM]        AS Platform,
       A.[CUST_NUMBER]     AS Cust_Number,
       A.[RCODE]           AS Rcode,
       A.[INTEGRATOR_NAME] AS Integrator_Name,
       A.[PROGRAM_NAME]    AS Program_Name,
       A.[BILLING_NAME]    AS Billing_Name,
       A.[TRANSACTION_ID]  AS Transaction_ID,
       A.[RECEIPT_ID]      AS Receipt_ID,
       A.[INTERAC_REF]     AS Interac_Ref,
       A.[FI_NAME]         AS FI_Name,
       A.[STATUS]          AS Status
        FROM [transactions] A
        WHERE A.[SERVICE_NUMBER] IN ('${serviceNumber}')
        AND 
        (updatedat BETWEEN  
        CreateDate(${formatDateOnlyWithCommas(fromDate)}) AND CreateDate(${formatDateOnlyWithCommas(toDate)})
        OR createdat BETWEEN  
        CreateDate(${formatDateOnlyWithCommas(fromDate)}) AND CreateDate(${formatDateOnlyWithCommas(toDate)})
      )
   `;
};

export { getTransactionsSql };
