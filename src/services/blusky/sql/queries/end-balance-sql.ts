const getEndBalanceSql = (serviceNumbers: string[]) => {
  const formattedServiceNumbers = serviceNumbers
    .map((serviceNumber) => `'${serviceNumber}'`)
    .join(", ");

  return `
  SELECT 
    A.[CUSTOMERNAME] AS customerName,
    A.[SERVICE_NUMBER] AS serviceNumber,
    SUM(CASE WHEN A.[PLATFORM] = 'SUMMARY' THEN A.[LAST_SETTLEMENT_BALANCE] ELSE 0 END) AS [Previous Total],
    SUM(CASE WHEN A.[PLATFORM] = 'SUMMARY' THEN A.[TOTAL_BALANCE] ELSE 0 END) AS [Current Balance],
    SUM(CASE WHEN A.[PLATFORM] = 'SUMMARY' THEN A.[TOTAL_BALANCE] ELSE 0 END) +
    SUM(CASE WHEN A.[PLATFORM] = 'RESERVE' THEN A.[RESERVE] ELSE 0 END) AS [Total Balance]
  FROM [VW_MERCHANT_CURRENT_BALANCE] A
  WHERE 
    A.[SERVICE_NUMBER] IN (${formattedServiceNumbers}) AND 
    A.[PLATFORM] IN ('SUMMARY')
  GROUP BY A.[CUSTOMERNAME], A.[SERVICE_NUMBER]
`;
};

export { getEndBalanceSql };
