import { fetchSqlData } from "./fetch-sql-data";
import { request } from "../../api-caller/request";

describe("fetch-sql-data", () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it("should fetch SQL data successfully", async () => {
    const rawSql = "SELECT \n * FROM test_table";
    const spyRequest = vi.spyOn(request, "get").mockResolvedValue({
      headers: [
        "header with spaces",
        "headerAlreadyInCamelCase",
        "\nHeader\nwith\nnewlines",
      ],
      values: [
        ["value1", "value2", "value3"],
        ["value4", "value5", "value6"],
      ],
    });

    const result = await fetchSqlData(rawSql, "test-token");

    expect(result).toEqual([
      {
        headerWithSpaces: "value1",
        headerAlreadyInCamelCase: "value2",
        headerWithNewlines: "value3",
      },
      {
        headerWithSpaces: "value4",
        headerAlreadyInCamelCase: "value5",
        headerWithNewlines: "value6",
      },
    ]);

    expect(spyRequest).toHaveBeenCalledWith(
      "baseurl/datasources/test-datasource/sql?query=SELECT%20*%20FROM%20test_table",
      "json",
      {
        headers: {
          accept: "application/json",
          "Content-Type": "application/json",
          // eslint-disable-next-line @typescript-eslint/naming-convention
          Authorization: "Bearer test-token",
        },
      }
    );
  });

  it("should gracefully handle errors", async () => {
    const rawSql = "SELECT * FROM test_table";
    const errorMessage = "Network error";
    const testError = new Error(errorMessage);
    const spyRequest = vi.spyOn(request, "get").mockRejectedValue(testError);

    await expect(fetchSqlData(rawSql, "test-token")).rejects.toThrow(
      `Error fetching SQL data from BluSky: Network error`
    );

    expect(spyRequest).toHaveBeenCalledWith(
      "baseurl/datasources/test-datasource/sql?query=SELECT%20*%20FROM%20test_table",
      "json",
      {
        headers: {
          accept: "application/json",
          "Content-Type": "application/json",
          // eslint-disable-next-line @typescript-eslint/naming-convention
          Authorization: "Bearer test-token",
        },
      }
    );
  });
});
