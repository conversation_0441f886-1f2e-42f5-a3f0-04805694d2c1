import { options as createOptions } from "@lib/access/role/create/options";
import { options as deleteOptions } from "@lib/access/role/delete/options";
import { options as readOptions } from "@lib/access/role/read/options";
import { options as updateOptions } from "@lib/access/role/update/options";

import type { FastifyInstance } from "fastify";

export default async function (server: FastifyInstance) {
  server.post("/", createOptions);
  server.get("/", readOptions);
  server.put("/", updateOptions);
  server.delete("/", deleteOptions);
}
