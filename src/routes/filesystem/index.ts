import { options as createOptions } from "@lib/filesystem/create/options";
import { options as deleteOptions } from "@lib/filesystem/delete/options";
import { options as downloadOptions } from "@lib/filesystem/download/options";
import { options as moveOptions } from "@lib/filesystem/move/options";
import { options as readOptions } from "@lib/filesystem/read/options";
import { options as renameOptions } from "@lib/filesystem/rename/options";

import type { FastifyInstance } from "fastify";

export default async function (server: FastifyInstance) {
  server.get("/", readOptions);
  server.get("/download", downloadOptions);
  server.post("/folder", createOptions);
  server.delete("/", deleteOptions);
  server.post("/rename", renameOptions);
  server.post("/move", moveOptions);
}
