import { options as authCheckOptions } from "../../lib/session/auth-check/options.js";
import { options as legacyAuthCheckOptions } from "../../lib/session/legacy-auth-check/options.js";
import { options as loginOptions } from "../../lib/session/login/options.js";
import { options as logoutOptions } from "../../lib/session/logout/options.js";

import type { FastifyInstance } from "fastify";

export default async function (server: FastifyInstance) {
  server.get("/", authCheckOptions);
  server.post("/", loginOptions);
  server.delete("/", logoutOptions);
  server.post("/legacy", legacyAuthCheckOptions);
}
