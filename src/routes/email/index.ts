import { options as readByFiltersOptions } from "@lib/email/read-by-filters/options";
import { options as readByIdOptions } from "@lib/email/read-by-id/options";
import { options as readBySettlementIdOptions } from "@lib/email/read-by-settlement-id/options";
import { options as readFilterOptionsOptions } from "@lib/email/read-filter-options/options";
import { options as readPreferenceOptions } from "@lib/email/read-preference/options";
import { options as updateOptions } from "@lib/email/update/options";

import type { FastifyInstance } from "fastify";

export default async function (server: FastifyInstance) {
  server.post("/", readByFiltersOptions);
  server.get("/:id", readByIdOptions);
  server.get("/filter-options", readFilterOptionsOptions);
  server.get("/preference", readPreferenceOptions);
  server.put("/", updateOptions);
  server.get("/settlement/:settlementId", readBySettlementIdOptions);
}
