import { options as firstTransactionDateOptions } from "@lib/test/first-transaction-date/options";
import { options as kycTransactionsOptions } from "@lib/test/kyc-transactions/options";
import { options as allTransactionsOptions } from "@lib/test/transactions/options";
import { options as totalTransactionsOptions } from "@lib/test/transactions-total/options";

import type { FastifyInstance } from "fastify";

export default async function (server: FastifyInstance) {
  server.post("/GetTotalTransactionsAmountByDate", totalTransactionsOptions);
  server.post("/GetFirstTransactionDate", firstTransactionDateOptions);
  server.post("/GetAllTransactionsByDate", allTransactionsOptions);
  server.post("/GetKycTransactionsByDate", kycTransactionsOptions);
}
