import { Decimal } from "@prisma/client/runtime/library";

/**
 * This function takes in any object and an array of keys.
 * Returns true if all values for the specified keys are not null, zero, or undefined.
 */
export const objectValuesAreNotNullOrZeroOrUndefined = (
  object: Record<string, unknown>,
  keys: string[]
) => {
  return keys.some((key) => {
    const value = object[key];

    if (value === null || value === undefined) {
      return false;
    }

    if (value instanceof Decimal) {
      return !value.isZero(); // False if Decimal(0)
    }

    return value !== 0;
  });
};
