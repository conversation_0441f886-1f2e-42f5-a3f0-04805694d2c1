import {
  toDateOnly,
  toDateOnlyFromString,
  fromDateOnly,
  fromDateOnlyToString,
  isSameDay,
  isDayBefore,
  isDateBeforeOrEqual,
  getHigherDate,
  getLowerDate,
  isDateInRange,
  startOfDayOnly,
  endOfDayOnly,
  startOfTheMonthOnly,
  endOfTheMonthOnly,
  startOfPreviousMonthOnly,
  endOfPreviousMonthOnly,
  subMonthsOnly,
  type DateOnly,
  subDaysOnly,
  dateToString,
  fromDateOnlyToStringLong,
} from "./date-only";

describe("toDateOnly", () => {
  it("should convert Date to DateOnly", () => {
    const date = new Date("2023-10-05");
    const dateOnly = toDateOnly(date);
    expect(dateOnly).toEqual({ year: 2023, month: 10, day: 5 });
  });
});

describe("toDateOnlyFromString", () => {
  it("should convert string to DateOnly", () => {
    const dateString = "2023-10-05";
    const dateOnly = toDateOnlyFromString(dateString);
    expect(dateOnly).toEqual({ year: 2023, month: 10, day: 5 });
  });
});

describe("fromDateOnly", () => {
  it("should convert DateOnly to Date", () => {
    const dateOnly: DateOnly = { year: 2023, month: 10, day: 5 };
    const date = fromDateOnly(dateOnly);
    expect(date.toISOString().startsWith("2023-10-05")).toBe(true);
  });
});

describe("fromDateOnlyToString", () => {
  it("should convert DateOnly to string", () => {
    const dateOnly: DateOnly = { year: 2023, month: 10, day: 5 };
    const dateString = fromDateOnlyToString(dateOnly);
    expect(dateString).toBe("2023-10-05");
  });
});

describe("fromDateOnlyToStringLong", () => {
  it("should convert DateOnly to string in long format", () => {
    const dateOnly: DateOnly = { year: 2023, month: 10, day: 5 };
    expect(fromDateOnlyToStringLong(dateOnly)).toBe("October 05 2023");
  });

  it("should convert DateOnly to string in long format with ordinal", () => {
    const dateOnly: DateOnly = { year: 2023, month: 10, day: 5 };
    expect(fromDateOnlyToStringLong(dateOnly, true)).toBe("October 5th 2023");
  });
});

describe("isSameDay", () => {
  it("should return true if two DateOnly are the same", () => {
    const date1: DateOnly = { year: 2023, month: 10, day: 5 };
    const date2: DateOnly = { year: 2023, month: 10, day: 5 };
    expect(isSameDay(date1, date2)).toBe(true);
  });

  it("should return false if two DateOnly are not the same day", () => {
    const date1: DateOnly = { year: 2023, month: 10, day: 1 };
    const date2: DateOnly = { year: 2023, month: 10, day: 5 };
    expect(isSameDay(date1, date2)).toBe(false);
  });

  it("should return false if two DateOnly are not the same year", () => {
    const date1: DateOnly = { year: 2023, month: 10, day: 5 };
    const date2: DateOnly = { year: 2024, month: 10, day: 5 };
    expect(isSameDay(date1, date2)).toBe(false);
  });

  it("should return false if two DateOnly are not the same month", () => {
    const date1: DateOnly = { year: 2023, month: 10, day: 5 };
    const date2: DateOnly = { year: 2023, month: 12, day: 5 };
    expect(isSameDay(date1, date2)).toBe(false);
  });
});

describe("isDayBefore", () => {
  it("should return true if the first DateOnly is a day before the other", () => {
    const date1: DateOnly = { year: 2023, month: 10, day: 4 };
    const date2: DateOnly = { year: 2023, month: 10, day: 5 };
    expect(isDayBefore(date1, date2)).toBe(true);
  });

  it("should return false if the first DateOnly is more than a day before the other", () => {
    const date1: DateOnly = { year: 2023, month: 10, day: 3 };
    const date2: DateOnly = { year: 2023, month: 10, day: 5 };
    expect(isDayBefore(date1, date2)).toBe(false);
  });

  it("should return false if the first DateOnly is a day after the other", () => {
    const date1: DateOnly = { year: 2023, month: 10, day: 6 };
    const date2: DateOnly = { year: 2023, month: 10, day: 5 };
    expect(isDayBefore(date1, date2)).toBe(false);
  });

  it("should return false if the dates are the same", () => {
    const date1: DateOnly = { year: 2023, month: 10, day: 5 };
    const date2: DateOnly = { year: 2023, month: 10, day: 5 };
    expect(isDayBefore(date1, date2)).toBe(false);
  });
});

describe("isDateBeforeOrEqual", () => {
  it("should return true if the first DateOnly is before the other", () => {
    const date1: DateOnly = { year: 2023, month: 10, day: 4 };
    const date2: DateOnly = { year: 2023, month: 10, day: 5 };
    expect(isDateBeforeOrEqual(date1, date2)).toBe(true);
  });

  it("should return true if the dates are the same", () => {
    const date1: DateOnly = { year: 2023, month: 10, day: 5 };
    const date2: DateOnly = { year: 2023, month: 10, day: 5 };
    expect(isDateBeforeOrEqual(date1, date2)).toBe(true);
  });

  it("should return false if the first DateOnly is after the other", () => {
    const date1: DateOnly = { year: 2023, month: 10, day: 6 };
    const date2: DateOnly = { year: 2023, month: 10, day: 5 };
    expect(isDateBeforeOrEqual(date1, date2)).toBe(false);
  });
});

describe("getHigherDate", () => {
  it("should return the first DateOnly if it is greater than the second", () => {
    const date1: DateOnly = { year: 2023, month: 11, day: 5 };
    const date2: DateOnly = { year: 2023, month: 10, day: 5 };
    expect(getHigherDate(date1, date2)).toEqual(date1);
  });

  it("should return the second DateOnly if it is greater than the first", () => {
    const date1: DateOnly = { year: 2023, month: 10, day: 4 };
    const date2: DateOnly = { year: 2023, month: 10, day: 5 };
    expect(getHigherDate(date1, date2)).toEqual(date2);
  });

  it("should return the DateOnly if they are the same", () => {
    const date1: DateOnly = { year: 2023, month: 10, day: 5 };
    const date2: DateOnly = { year: 2023, month: 10, day: 5 };
    expect(getHigherDate(date1, date2)).toEqual(date1);
  });
});

describe("getLowerDate", () => {
  it("should return the first DateOnly if it is lower than the second", () => {
    const date1: DateOnly = { year: 2022, month: 10, day: 4 };
    const date2: DateOnly = { year: 2023, month: 10, day: 4 };
    expect(getLowerDate(date1, date2)).toEqual(date1);
  });

  it("should return the second DateOnly if it is lower than the first", () => {
    const date1: DateOnly = { year: 2023, month: 10, day: 5 };
    const date2: DateOnly = { year: 2023, month: 10, day: 4 };
    expect(getLowerDate(date1, date2)).toEqual(date2);
  });

  it("should return the DateOnly if they are the same", () => {
    const date1: DateOnly = { year: 2023, month: 10, day: 5 };
    const date2: DateOnly = { year: 2023, month: 10, day: 5 };
    expect(getLowerDate(date1, date2)).toEqual(date1);
  });
});

describe("isDateInRange", () => {
  it("should return true if the DateOnly is in the middle of the range", () => {
    const date: DateOnly = { year: 2023, month: 10, day: 5 };
    const startDate: DateOnly = { year: 2023, month: 10, day: 1 };
    const endDate: DateOnly = { year: 2023, month: 10, day: 10 };
    expect(isDateInRange(date, startDate, endDate)).toBe(true);
  });

  it("should return true if the DateOnly is the same as the lower range", () => {
    const date: DateOnly = { year: 2023, month: 10, day: 6 };
    const startDate: DateOnly = { year: 2023, month: 10, day: 6 };
    const endDate: DateOnly = { year: 2023, month: 10, day: 10 };
    expect(isDateInRange(date, startDate, endDate)).toBe(true);
  });

  it("should return true if the DateOnly is the same as the upper range", () => {
    const date: DateOnly = { year: 2023, month: 10, day: 10 };
    const startDate: DateOnly = { year: 2023, month: 10, day: 6 };
    const endDate: DateOnly = { year: 2023, month: 10, day: 10 };
    expect(isDateInRange(date, startDate, endDate)).toBe(true);
  });

  it("should return false if the DateOnly is before the range", () => {
    const date: DateOnly = { year: 2023, month: 10, day: 5 };
    const startDate: DateOnly = { year: 2023, month: 10, day: 6 };
    const endDate: DateOnly = { year: 2023, month: 10, day: 10 };
    expect(isDateInRange(date, startDate, endDate)).toBe(false);
  });

  it("should return false if the DateOnly is after the range", () => {
    const date: DateOnly = { year: 2023, month: 10, day: 11 };
    const startDate: DateOnly = { year: 2023, month: 10, day: 6 };
    const endDate: DateOnly = { year: 2023, month: 10, day: 10 };
    expect(isDateInRange(date, startDate, endDate)).toBe(false);
  });
});

describe("startOfDayOnly", () => {
  it("should return a Date where the time is start of day", () => {
    const date: DateOnly = { year: 2023, month: 10, day: 5 };
    const startOfDay = startOfDayOnly(date);
    expect(startOfDay.toISOString().endsWith("00:00:00.000Z")).toBe(true);
  });
});

describe("endOfDayOnly", () => {
  it("should return a Date where the time is end of day", () => {
    const date: DateOnly = { year: 2023, month: 10, day: 5 };
    const endOfDay = endOfDayOnly(date);
    expect(endOfDay.toISOString().endsWith("23:59:59.999Z")).toBe(true);
  });
});

describe("startOfTheMonthOnly", () => {
  it("should return a DateOnly that is the start of the month", () => {
    const date: DateOnly = { year: 2023, month: 10, day: 16 };
    const startOfMonth = startOfTheMonthOnly(date);
    expect(startOfMonth).toEqual({ year: 2023, month: 10, day: 1 });
  });
});

describe("endOfTheMonthOnly", () => {
  it("should return a DateOnly that is the end of the month that has 31 days", () => {
    const date: DateOnly = { year: 2023, month: 10, day: 16 };
    const endOfMonth = endOfTheMonthOnly(date);
    expect(endOfMonth).toEqual({ year: 2023, month: 10, day: 31 });
  });

  it("should return a DateOnly that is the end of the month that has 30 days", () => {
    const date: DateOnly = { year: 2023, month: 11, day: 16 };
    const endOfMonth = endOfTheMonthOnly(date);
    expect(endOfMonth).toEqual({ year: 2023, month: 11, day: 30 });
  });

  it("should return a DateOnly that is the end of the month that has 28 days", () => {
    const date: DateOnly = { year: 2023, month: 2, day: 16 };
    const endOfMonth = endOfTheMonthOnly(date);
    expect(endOfMonth).toEqual({ year: 2023, month: 2, day: 28 });
  });

  it("should return a DateOnly that is the end of the month that has 29 days", () => {
    const date: DateOnly = { year: 2024, month: 2, day: 16 };
    const endOfMonth = endOfTheMonthOnly(date);
    expect(endOfMonth).toEqual({ year: 2024, month: 2, day: 29 });
  });
});

describe("startOfPreviousMonthOnly", () => {
  it("should return a DateOnly that is the start of the previous month and same year", () => {
    const date: DateOnly = { year: 2023, month: 10, day: 5 };
    const startOfPreviousMonth = startOfPreviousMonthOnly(date);
    expect(startOfPreviousMonth).toEqual({ year: 2023, month: 9, day: 1 });
  });

  it("should return a DateOnly that is the start of the previous month and different year", () => {
    const date: DateOnly = { year: 2024, month: 1, day: 5 };
    const startOfPreviousMonth = startOfPreviousMonthOnly(date);
    expect(startOfPreviousMonth).toEqual({ year: 2023, month: 12, day: 1 });
  });
});

describe("endOfPreviousMonthOnly", () => {
  it("should return a DateOnly that is the end of the previous month that has 30 days", () => {
    const date: DateOnly = { year: 2023, month: 10, day: 5 };
    const endOfPreviousMonth = endOfPreviousMonthOnly(date);
    expect(endOfPreviousMonth).toEqual({ year: 2023, month: 9, day: 30 });
  });

  it("should return a DateOnly that is the end of the previous month that has 31 days", () => {
    const date: DateOnly = { year: 2023, month: 11, day: 5 };
    const endOfPreviousMonth = endOfPreviousMonthOnly(date);
    expect(endOfPreviousMonth).toEqual({ year: 2023, month: 10, day: 31 });
  });

  it("should return a DateOnly that is the end of the previous month that has 28 days", () => {
    const date: DateOnly = { year: 2023, month: 3, day: 5 };
    const endOfPreviousMonth = endOfPreviousMonthOnly(date);
    expect(endOfPreviousMonth).toEqual({ year: 2023, month: 2, day: 28 });
  });

  it("should return a DateOnly that is the end of the previous month that has 29 days", () => {
    const date: DateOnly = { year: 2024, month: 3, day: 5 };
    const endOfPreviousMonth = endOfPreviousMonthOnly(date);
    expect(endOfPreviousMonth).toEqual({ year: 2024, month: 2, day: 29 });
  });

  it("should return a DateOnly that is the end of the previous month and different year", () => {
    const date: DateOnly = { year: 2024, month: 1, day: 5 };
    const endOfPreviousMonth = endOfPreviousMonthOnly(date);
    expect(endOfPreviousMonth).toEqual({ year: 2023, month: 12, day: 31 });
  });
});

describe("subMonthsOnly", () => {
  it("should return a DateOnly with subtracted months and same year", () => {
    const date: DateOnly = { year: 2023, month: 10, day: 5 };
    const subtractedDate = subMonthsOnly(date, 2);
    expect(subtractedDate).toEqual({ year: 2023, month: 8, day: 5 });
  });

  it("should return a DateOnly with subtracted months and different year", () => {
    const date: DateOnly = { year: 2023, month: 12, day: 5 };
    const subtractedDate = subMonthsOnly(date, 15);
    expect(subtractedDate).toEqual({ year: 2022, month: 9, day: 5 });
  });
});

describe("subDaysOnly", () => {
  it("should return a DateOnly with subtracted days and same month", () => {
    const date: DateOnly = { year: 2023, month: 10, day: 5 };
    const subtractedDate = subDaysOnly(date, 2);
    expect(subtractedDate).toEqual({ year: 2023, month: 10, day: 3 });
  });

  it("should return a DateOnly with subtracted days and different month", () => {
    const date: DateOnly = { year: 2023, month: 10, day: 1 };
    const subtractedDate = subDaysOnly(date, 2);
    expect(subtractedDate).toEqual({ year: 2023, month: 9, day: 29 });
  });

  it("should return a DateOnly with subtracted days and different year", () => {
    const date: DateOnly = { year: 2023, month: 1, day: 1 };
    const subtractedDate = subDaysOnly(date, 3);
    expect(subtractedDate).toEqual({ year: 2022, month: 12, day: 29 });
  });
});

describe("dateToString", () => {
  it("should return the date in yyyy-MM-dd format", () => {
    const date = new Date("2023-10-05T12:34:56Z");
    expect(dateToString(date)).toBe("2023-10-05");
  });

  it("should handle dates at the start of the month", () => {
    const date = new Date("2023-10-01T00:00:00Z");
    expect(dateToString(date)).toBe("2023-10-01");
  });

  it("should handle dates at the end of the month", () => {
    const date = new Date("2023-10-31T23:59:59Z");
    expect(dateToString(date)).toBe("2023-10-31");
  });
});
