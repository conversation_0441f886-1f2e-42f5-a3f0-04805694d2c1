import { chunkArray } from "./chunk-array";

describe("chunkArray", () => {
  it("should chunk an array into smaller arrays of the specified size", () => {
    const array = [1, 2, 3, 4, 5, 6, 7, 8, 9];
    const size = 3;
    const result = chunkArray(array, size);
    expect(result).toEqual([
      [1, 2, 3],
      [4, 5, 6],
      [7, 8, 9],
    ]);
  });

  it("should handle arrays that do not divide evenly", () => {
    const array = [1, 2, 3, 4, 5];
    const size = 2;
    const result = chunkArray(array, size);
    expect(result).toEqual([[1, 2], [3, 4], [5]]);
  });

  it("should return an empty array when input array is empty", () => {
    const array: number[] = [];
    const size = 3;
    const result = chunkArray(array, size);
    expect(result).toEqual([]);
  });

  it("should return the original array when size is greater than array length", () => {
    const array = [1, 2, 3];
    const size = 5;
    const result = chunkArray(array, size);
    expect(result).toEqual([[1, 2, 3]]);
  });

  it("should handle size of 1 correctly", () => {
    const array = [1, 2, 3, 4];
    const size = 1;
    const result = chunkArray(array, size);
    expect(result).toEqual([[1], [2], [3], [4]]);
  });

  it("should handle size of 0 correctly", () => {
    const array = [1, 2, 3, 4];
    const size = 0;
    const result = chunkArray(array, size);
    expect(result).toEqual([]);
  });

  it("should handle negative size correctly", () => {
    const array = [1, 2, 3, 4];
    const size = -1;
    const result = chunkArray(array, size);
    expect(result).toEqual([]);
  });
});
