import type { Prisma } from "@prisma/client";

export function safeParse<PERSON>son(jsonValue: Prisma.JsonValue): string[] {
  try {
    const parsed: unknown =
      typeof jsonValue === "string" ? JSON.parse(jsonValue) : jsonValue;

    if (
      Array.isArray(parsed) &&
      parsed.every((item): item is string => typeof item === "string")
    ) {
      return parsed;
    }
  } catch {
    // Ignore JSON parsing errors
  }

  return [];
}
