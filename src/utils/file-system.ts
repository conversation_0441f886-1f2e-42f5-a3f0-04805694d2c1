import { constants } from "node:fs";
import { access } from "node:fs/promises";
import { resolve } from "node:path";

import { baseDirectoryName } from "@constants/filesystem";

const resolveFilePath = (filePath: string): string => {
  const baseDirectory: string = resolve(baseDirectoryName);

  if (filePath.startsWith("/")) {
    filePath = `.${filePath}`;
  }

  const resolvedPath: string = resolve(baseDirectory, filePath);

  // Security check: Ensure that the resolved path starts with the base directory.
  // Although `resolve` gives us an absolute path, it does not automatically guarantee
  // that the final path stays within the `baseDirectory`. Without this check,
  // a malicious user could provide a path like "../../../some/other/folder",
  // which would allow access to files or directories outside the intended root directory.
  //
  // By enforcing that the `resolvedPath` starts with `baseDirectory`, we ensure that
  // any path provided stays confined within the allowed root filesystem, protecting against
  // unauthorized access or modification of other parts of the file system.
  if (!resolvedPath.startsWith(baseDirectory)) {
    throw new Error(
      "Invalid path. Files can only be accessed within the base directory."
    );
  }

  return resolvedPath;
};

/**
 * Checks if the current process has the specified permission on the given file or directory.
 *
 * @param filePath - The path to the file or directory.
 * @param permission - The permission to check ('read' or 'write').
 * @returns A promise that resolves to an object containing the result and the error if any.
 */
async function hasPermission(
  filePath: string,
  permission: "read" | "write"
): Promise<{ isSuccess: boolean; error?: NodeJS.ErrnoException }> {
  let mode: number;

  if (permission === "read") {
    mode = constants.R_OK;
  } else if (permission === "write") {
    mode = constants.W_OK;
  } else {
    throw new Error('Invalid permission type. Use "read" or "write".');
  }

  try {
    await access(filePath, mode);

    return { isSuccess: true };
  } catch (error) {
    if (error instanceof Error && "code" in error) {
      return { isSuccess: false, error: error as NodeJS.ErrnoException };
    }

    // In case the error is not of type NodeJS.ErrnoException
    return { isSuccess: false, error: error as NodeJS.ErrnoException };
  }
}

export { resolveFilePath, hasPermission };
