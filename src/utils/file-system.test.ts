import fs from "node:fs";
import os from "node:os";
import path, { resolve } from "node:path";

import {
  describe,
  it,
  expect,
  beforeEach,
  vi,
  beforeAll,
  afterAll,
} from "vitest";

import { resolveFilePath, hasPermission } from "./file-system";

// Mocking baseDirectoryName
vi.mock("@constants/filesystem", () => ({
  baseDirectoryName: "/base/directory",
}));

describe("resolveFilePath", () => {
  let baseDirectory: string;

  beforeEach(() => {
    baseDirectory = resolve("/base/directory");
  });

  it("should resolve a valid file path inside the base directory", () => {
    const filePath = "subfolder/file.txt";
    const result = resolveFilePath(filePath);

    expect(result).toBe(resolve(baseDirectory, filePath));
  });

  it("should throw an error for paths outside the base directory", () => {
    // Attempt to access outside the base directory
    const filePath = "../../outside-directory/file.txt";

    expect(() => resolveFilePath(filePath)).toThrow();
  });

  it("should resolve to base directory when the file path is an empty string", () => {
    const filePath = "";
    const result = resolveFilePath(filePath);

    expect(result).toBe(baseDirectory);
  });

  it("should correctly handle paths that try to backtrack but stay within the base directory", () => {
    const filePath = "subfolder/../file.txt";
    const result = resolveFilePath(filePath);

    expect(result).toBe(resolve(baseDirectory, "file.txt"));
  });

  it("should resolve the file path when the path is the same as the base directory", () => {
    const filePath = "./"; // Path points to base directory
    const result = resolveFilePath(filePath);

    expect(result).toBe(baseDirectory); // Should resolve to base directory
  });
});

describe("hasPermission", () => {
  const testDirectory = path.join(os.tmpdir(), "hasPermissionTest");
  const readableFile = path.join(testDirectory, "readableFile.txt");
  const nonReadableFile = path.join(testDirectory, "nonReadableFile.txt");
  const writableFile = path.join(testDirectory, "writableFile.txt");
  const nonWritableFile = path.join(testDirectory, "nonWritableFile.txt");
  const nonExistentFile = path.join(testDirectory, "nonExistentFile.txt");
  const readableDirectory = path.join(testDirectory, "readableDir");
  const nonWritableDirectory = path.join(testDirectory, "nonWritableDir");

  beforeAll(() => {
    // Create test directory
    fs.mkdirSync(testDirectory, { recursive: true });

    // Create a readable file
    fs.writeFileSync(readableFile, "This is a readable file.");
    fs.chmodSync(readableFile, 0o644); // Owner read/write, others read

    // Create a non-readable file
    fs.writeFileSync(nonReadableFile, "This file is not readable.");
    fs.chmodSync(nonReadableFile, 0o000); // No permissions

    // Create a writable file
    fs.writeFileSync(writableFile, "This is a writable file.");
    fs.chmodSync(writableFile, 0o200); // Owner write only

    // Create a non-writable file
    fs.writeFileSync(nonWritableFile, "This file is not writable.");
    fs.chmodSync(nonWritableFile, 0o444); // Owner read only

    // Create a readable directory
    fs.mkdirSync(readableDirectory, { recursive: true });
    fs.chmodSync(readableDirectory, 0o755); // Owner read/write/execute, others read/execute

    // Create a non-writable directory
    fs.mkdirSync(nonWritableDirectory, { recursive: true });
    fs.chmodSync(nonWritableDirectory, 0o555); // Owner read/execute, no write permissions
  });

  afterAll(() => {
    // Reset permissions to allow deletion
    fs.chmodSync(testDirectory, 0o755);
    fs.chmodSync(readableFile, 0o644);
    fs.chmodSync(nonReadableFile, 0o644);
    fs.chmodSync(writableFile, 0o644);
    fs.chmodSync(nonWritableFile, 0o644);
    fs.chmodSync(readableDirectory, 0o755);
    fs.chmodSync(nonWritableDirectory, 0o755);

    // Remove test directory and all contents
    fs.rmSync(testDirectory, { recursive: true, force: true });
  });

  it("should return true for a file with read permission", async () => {
    const { isSuccess, error } = await hasPermission(readableFile, "read");
    expect(isSuccess).toBe(true);
    expect(error).toBeUndefined();
  });

  it("should return false for a file without read permission", async () => {
    const { isSuccess, error } = await hasPermission(nonReadableFile, "read");
    expect(isSuccess).toBe(false);
    expect(error).toBeDefined();
    expect(error?.code).toBe("EACCES");
  });

  it("should return true for a file with write permission", async () => {
    const { isSuccess, error } = await hasPermission(writableFile, "write");
    expect(isSuccess).toBe(true);
    expect(error).toBeUndefined();
  });

  it("should return false for a file without write permission", async () => {
    const { isSuccess, error } = await hasPermission(nonWritableFile, "write");
    expect(isSuccess).toBe(false);
    expect(error).toBeDefined();
    expect(error?.code).toBe("EACCES");
  });

  it("should return true for a directory with write permission", async () => {
    const { isSuccess, error } = await hasPermission(
      readableDirectory,
      "write"
    );
    expect(isSuccess).toBe(true);
    expect(error).toBeUndefined();
  });

  it("should return false for a directory without write permission", async () => {
    const { isSuccess, error } = await hasPermission(
      nonWritableDirectory,
      "write"
    );
    expect(isSuccess).toBe(false);
    expect(error).toBeDefined();
    expect(error?.code).toBe("EACCES");
  });

  it("should return false for a non-existent file", async () => {
    const { isSuccess, error } = await hasPermission(nonExistentFile, "read");
    expect(isSuccess).toBe(false);
    expect(error).toBeDefined();
    expect(error?.code).toBe("ENOENT");
  });

  it("should throw an error for an invalid permission type", async () => {
    await expect(
      hasPermission(readableFile, "execute" as unknown as "read")
    ).rejects.toThrow();
  });
});
