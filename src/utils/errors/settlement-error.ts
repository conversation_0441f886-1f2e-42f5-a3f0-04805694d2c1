class SettlementError extends Error {
  public isSkipped: boolean;

  constructor(message: string, isSkipped = false) {
    super(message);
    this.isSkipped = isSkipped;
    this.name = "SettlementError";
    /**
     * This line is needed because of how TypeScript transpiles classes. Without this line,
     * instanceof checks would fail for instances of SettlementError.
     */
    Object.setPrototypeOf(this, new.target.prototype);

    // Maintains proper stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, SettlementError);
    }
  }
}

export { SettlementError };
