class RequestError extends Error {
  public httpCode: number | undefined;
  public originalError: unknown;
  public url: string;

  constructor(
    message: string,
    url: string,
    httpCode?: number,
    originalError?: unknown
  ) {
    super(message);
    this.name = "RequestError";
    this.url = url;
    this.httpCode = httpCode;
    this.originalError = originalError;

    /**
     * This line is needed because of how TypeScript transpiles classes. Without this line,
     * instanceof checks would fail for instances of RequestError.
     */
    Object.setPrototypeOf(this, new.target.prototype);

    // Maintains proper stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, RequestError);
    }
  }
}

export { RequestError };
