import { startOfDay, isSameDay } from "date-fns";

const isDateInRange = (dateToCheck: Date, fromDate: Date, toDate: Date) => {
  const startOfDayToCheck = startOfDay(dateToCheck);
  const startOfDayFromDate = startOfDay(fromDate);
  const startOfDayToDate = startOfDay(toDate);

  return (
    isSameDay(startOfDayToCheck, startOfDayFromDate) ||
    isSameDay(startOfDayToCheck, startOfDayToDate) ||
    (startOfDayToCheck > startOfDayFromDate &&
      startOfDayToCheck < startOfDayToDate)
  );
};

export { isDateInRange };
