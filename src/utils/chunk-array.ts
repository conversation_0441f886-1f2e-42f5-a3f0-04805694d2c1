/**
 * Splits an array into an array of chunks of a specified size.
 *
 * @param array - The array to split.
 * @param size - The size of each chunk.
 *               Must be greater than 0 - otherwise an empty array is returned.
 * @returns An array of chunks with the specified size.
 *          If the size is 0 or negative, an empty array is returned.
 */
const chunkArray = <T>(array: T[], size: number): T[][] => {
  const result: T[][] = [];

  if (size <= 0) {
    return result;
  }

  for (let index = 0; index < array.length; index += size) {
    result.push(array.slice(index, index + size));
  }

  return result;
};

export { chunkArray };
