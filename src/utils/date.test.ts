import { isDateInRange } from "./date";

describe("isDateInRange", () => {
  it("should return true if the date is the same as the fromDate", () => {
    const dateToCheck = new Date("2023-10-01");
    const fromDate = new Date("2023-10-01");
    const toDate = new Date("2023-10-10");
    expect(isDateInRange(dateToCheck, fromDate, toDate)).toBe(true);
  });

  it("should return true if the date is the same as the toDate", () => {
    const dateToCheck = new Date("2023-10-10");
    const fromDate = new Date("2023-10-01");
    const toDate = new Date("2023-10-10");
    expect(isDateInRange(dateToCheck, fromDate, toDate)).toBe(true);
  });

  it("should return true if the date is within the range", () => {
    const dateToCheck = new Date("2023-10-05");
    const fromDate = new Date("2023-10-01");
    const toDate = new Date("2023-10-10");
    expect(isDateInRange(dateToCheck, fromDate, toDate)).toBe(true);
  });

  it("should return false if the date is before the fromDate", () => {
    const dateToCheck = new Date("2023-09-30");
    const fromDate = new Date("2023-10-01");
    const toDate = new Date("2023-10-10");
    expect(isDateInRange(dateToCheck, fromDate, toDate)).toBe(false);
  });

  it("should return false if the date is after the toDate", () => {
    const dateToCheck = new Date("2023-10-11");
    const fromDate = new Date("2023-10-01");
    const toDate = new Date("2023-10-10");
    expect(isDateInRange(dateToCheck, fromDate, toDate)).toBe(false);
  });
});
