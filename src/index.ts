import { join } from "node:path";

import { environment } from "@constants/environment";
import { logger } from "@constants/logger";
import { fastifyAutoload } from "@fastify/autoload";
import { type FastifyJwtNamespace, type JWT } from "@fastify/jwt";
import { type PrismaClient } from "@prisma/client";
import { fastify } from "fastify";

import { authenticate } from "./decorators/authenticate";
import { type UserProfile } from "./types/user-profile";

declare module "fastify" {
  // eslint-disable-next-line @typescript-eslint/consistent-type-definitions
  interface FastifyRequest
    extends FastifyJwtNamespace<{
        jwtDecode: "accessJwtDecode";
        jwtVerify: "accessJwtVerify";
      }>,
      FastifyJwtNamespace<{
        jwtDecode: "refreshJwtDecode";
        jwtVerify: "refreshJwtVerify";
      }> {
    jwt: JWT;
    userProfile: UserProfile;
  }
  // eslint-disable-next-line @typescript-eslint/consistent-type-definitions
  interface FastifyReply
    extends FastifyJwtNamespace<{ jwtSign: "accessJwtSign" }>,
      FastifyJwtNamespace<{ jwtSign: "refreshJwtSign" }> {}

  // eslint-disable-next-line @typescript-eslint/consistent-type-definitions
  interface FastifyInstance {
    authenticate: (
      request: FastifyRequest,
      reply: FastifyReply
    ) => Promise<void>;
    prisma: PrismaClient;
    getEmailBody(
      emailId: number
    ): Promise<{ emailBodyHtml: string; error?: string }>;
  }
}
const server = fastify({
  logger,
});

// This loads all plugins defined in plugins
await server.register(fastifyAutoload, {
  dir: join(
    process.cwd(),
    `${environment.isCompiled ? "dist" : "src"}/plugins`
  ),
});
// Enables file-based routing. Scans "src/routes" directory and registers all found routes.
await server.register(fastifyAutoload, {
  dir: join(process.cwd(), `${environment.isCompiled ? "dist" : "src"}/routes`),
});

server.addHook("onRequest", (request, _, done) => {
  request.jwt = server.jwt;
  done();
});

server.decorate("authenticate", authenticate);
server.listen(
  {
    // This host is required for Docker
    host: "0.0.0.0",
    port: 8081,
  },
  (error) => {
    if (error) {
      server.log.error(error);
    }
  }
);

export { server };
