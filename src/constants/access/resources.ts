import { type Permission } from "./permissions";

// List of all available resources in the application
const resources = {
  users: "Users",
  filesystem: "Filesystem",
  iam: "IAM",
  settlement: "Settlement",
  allMonthlySettlement: "All-Monthly Settlement",
  documentation: "Documentation",
  tierset: "Tierset",
  wire: "Wire",
  email: "Email",
  merchantConfiguration: "Merchant Configuration",
  reserveFund: "Reserve Fund",
  seedDataMaintenance: "Seed Data Maintenance",
} as const;

type Resource = keyof typeof resources;

// We are using constant files (like the 'permissions' and 'resources' defined here) rather
// than saving them in a database for several reasons:
// 1. **Simplicity and Performance**: Constant files are much faster to access as they are
//    loaded into memory at runtime.
// 2. **No Dynamic Usage**: Even if permissions and resources are stored in a database, adding
//    a new one there is not very helpful. The application doesn't inherently know what to do
//    with a new resource or permission added dynamically. It would still require a code change
//    to properly utilize the new permission or resource in the business logic, as new behavior
//    must be explicitly handled in code. Thus, storing them in a database doesn’t save effort
//    in this case.
// 3. **Maintainability**: Storing permissions and resources as constants simplifies the
//    architecture and ensures that adding new ones is deliberate and controlled. It avoids
//    unnecessary database complexity, especially when these elements are mostly
//    static or infrequently changed.

// ResourcesRule defines the default set of permissions that can be applied to each resource.
// These rules specify which permissions are available for a resource when creating a role.
// If a permission is not present in the array for a resource,it means that permission
// is not applicable to that resource.
const resourcesRule: Record<Resource, Permission[]> = {
  users: ["create", "read", "update", "delete"],
  filesystem: ["create", "read", "update", "delete"],
  iam: ["create", "read", "update", "delete"],
  settlement: ["create", "read", "update", "delete", "approve", "reset"],
  allMonthlySettlement: [
    "create",
    "read",
    "update",
    "delete",
    "reset",
    "approve",
  ],
  documentation: ["read"],
  tierset: ["create", "read", "update", "delete"],
  wire: ["create", "read", "update", "delete", "reset", "approve"],
  email: ["create", "read", "update", "delete", "send", "reset", "approve"],
  merchantConfiguration: ["create", "read", "update", "delete"],
  reserveFund: ["create", "read", "update", "delete"],
  seedDataMaintenance: ["create", "read", "update", "delete"],
};

export { resources, type Resource, resourcesRule };
