// List of all available permissions in the application.

// The keys of this object should be used by the frontend
// when communicating with the backend or updating roles.
// These keys are the internal identifiers for permissions,
// while the values are human-readable labels used for display purposes.
const permissions = {
  read: "Read",
  update: "Update",
  create: "Create",
  delete: "Delete",
  approve: "Approve",
  reset: "Reset",
  send: "Send",
} as const;

type Permission = keyof typeof permissions;

export { type Permission, permissions };
