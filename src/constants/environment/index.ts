import env from "env-var";

const nodeEnvironment = env
  .get("NODE_ENV")
  .default("development")
  .asEnum(["production", "development", "test", "ci"]);

// Returns environment variables that have been loaded in a safe and predictable way
export const environment = {
  // Node.js environment
  isProduction: nodeEnvironment === "production",
  isDevelopment: nodeEnvironment === "development",
  isTest: nodeEnvironment === "test",
  isCi: nodeEnvironment === "ci",
  isCompiled: env.get("COMPILED").asBoolStrict(),
  isDebug: env.get("DEBUG").asBoolStrict(),
  databaseUrl: env.get("DATABASE_URL").default("").asString(),
  // JWT token settings
  tokenSecret: env
    .get("JWT_SECRET")
    .default((Math.random() + 1).toString(36))
    .asString(),
  accessTokenCookieName: env
    .get("JWT_ACCESS_TOKEN_COOKIE_NAME")
    .default("token")
    .asString(),
  refreshTokenCookieName: env
    .get("JWT_REFRESH_TOKEN_COOKIE_NAME")
    .default("refresh_token")
    .asString(),
  refreshTokenDuration: env
    .get("JWT_REFRESH_TOKEN_DURATION")
    .default("7d")
    .asString(),
  accessTokenDuration: env
    .get("JWT_ACCESS_TOKEN_DURATION")
    .default("30m")
    .asString(),
  allowedCorsDomains: env.get("ALLOWED_CORS_DOMAINS").default(["*"]).asArray(),
  bluskyBaseUrl: env
    .get("BLUSKY_BASE_URL")
    .example("https://www.bluskyreporting.com/api")
    .asString(),
  bluskyUsername: env.get("BLUSKY_USERNAME").asString(),
  bluskyPassword: env.get("BLUSKY_PASSWORD").asString(),
  bluskyCubeId: env.get("BLUSKY_CUBE_ID").asString(),
  bluskyDatasource: env.get("BLUSKY_DATASOURCE").asString(),
  senderEmailUsername: env.get("SENDER_EMAIL_USERNAME").asString(),
  githubSha: env.get("GITHUB_SHA").default("unavailable").asString(),
  monthsToHistorical: env.get("MONTHS_TO_HISTORICAL").asInt(),
  emailBodyLambdaRegion: env.get("EMAIL_BODY_REGION").asString(),
  emailBodyLambdaAccessKey: env.get("EMAIL_BODY_AWS_ACCESS_KEY_ID").asString(),
  emailBodyLambdaSecret: env.get("EMAIL_BODY_AWS_SECRET_ACCESS_KEY").asString(),
  emailBodyLambdaFunctionName: env
    .get("EMAIL_BODY_AWS_FUNCTION_NAME")
    .asString(),
  baseFileLocation: env.get("BASE_FILE_LOCATION").asString(),
};
