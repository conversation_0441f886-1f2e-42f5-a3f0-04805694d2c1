import { environment } from "@constants/environment";

// Returns pino logger configuration to be used in Fastify server
export const logger = {
  // Pretty-printing is used only in development (to maximize the performance elsewhere)
  ...(environment.isDevelopment && {
    transport: {
      target: "pino-pretty",
    },
  }),
  // Enables showing 'trace' level logs (default levels are 'info' and above)
  ...(environment.isDebug && {
    level: "trace",
  }),
};
