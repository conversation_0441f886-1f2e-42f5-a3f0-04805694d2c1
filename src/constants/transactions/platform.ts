const platforms = [
  "ACH",
  "ANR",
  "ANX",
  "ETO",
  "RTO",
  "RTX",
  "ETI",
  "RFM",
  "IDP",
  "ETF",
] as const;

// Platform type as a union of all the values in the platforms array
type Platform = (typeof platforms)[number];

// Group type that enforces its members to be one of the platforms
type Group = {
  name: string;
  members: Platform[];
  coreMembers?: Platform[];
};

const achGroup: Group = {
  name: "ACH GROUP",
  members: ["ACH", "ANR", "ANX"],
};

const rtoGroup: Group = {
  name: "RTO GROUP",
  members: ["ETO", "RTO", "RTX"],
};

const idpGroup: Group = {
  name: "IDP GROUP",
  members: ["ETI", "RFM", "IDP", "ETF"],
  coreMembers: ["ETI", "RFM", "ETF"],
};

const payInGroup: Platform[] = ["ETI", "RFM", "IDP", "ETF"];
const payOutGroup: Platform[] = ["ETO", "ACH", "RTO", "RTX", "ANR", "ANX"];

export {
  platforms,
  achGroup,
  rtoGroup,
  idpGroup,
  payInGroup,
  payOutGroup,
  type Platform,
};
