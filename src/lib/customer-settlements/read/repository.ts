import { type PrismaClient } from "@prisma/client";

import type {
  ApprovalCustomerSettlement,
  PreviousSettlement,
} from "@lib/customer-settlements/read/type";

export const fetchSettlementDetailsForApproval = async (
  prisma: PrismaClient,
  customerSettlementsId: number
): Promise<ApprovalCustomerSettlement | undefined> => {
  const settlement = await prisma.customerSettlements.findUnique({
    where: {
      customerSettlementsId,
    },
    include: {
      platform: true,
      customer: {
        include: {
          wireGroup: {
            include: {
              wireConfiguration: true,
              beneficiary: {
                include: {
                  bankAccount: true,
                  cryptoAccount: true,
                },
              },
            },
          },
          emailConfiguration: true,
        },
      },
      customerCustomerType: {
        include: {
          statementFrequency: true,
          customerType: true,
        },
      },
    },
  });

  if (!settlement) {
    return;
  }

  return {
    customerSettlementsId: settlement.customerSettlementsId,
    customerId: settlement.customerId,
    fromDate: settlement.fromDate,
    toDate: settlement.toDate,
    endBalance: settlement.endBalance ? Number(settlement.endBalance) : 0,
    platform: {
      platformId: settlement.platform.platformId,
      platformCode: settlement.platform.platformCode,
    },
    customer: {
      customerName: settlement.customer.customerName,
      serviceNumber: settlement.customer.serviceNumber,
      customerTradingName: settlement.customer.customerTradingName ?? "",
      wireGroup: {
        wireConfiguration: {
          isHold: false,
          thresholdAmount: settlement.customer.wireGroup?.wireConfiguration
            ?.thresholdAmount
            ? Number(
                settlement.customer.wireGroup.wireConfiguration.thresholdAmount
              )
            : 0,
        },
        beneficiary: {
          reference:
            settlement.customer.wireGroup?.beneficiary?.reference ?? "",
          bankAccount: {
            bankPaymentRailId:
              settlement.customer.wireGroup?.beneficiary?.bankAccount
                ?.bankPaymentRailId ?? 0,
          },
          ...(settlement.customer.wireGroup?.beneficiary?.cryptoAccount && {
            cryptoAccount: {
              cryptoPaymentRailId:
                settlement.customer.wireGroup.beneficiary.cryptoAccount
                  .cryptoPaymentRailId,
            },
          }),
        },
      },
      emailConfiguration: {
        isEmailEnabled: true,
      },
    },
    customerCustomerType: {
      ...(settlement.customerCustomerType?.statementFolderLocation && {
        statementFolderLocation:
          settlement.customerCustomerType.statementFolderLocation,
      }),
      statementFrequency: {
        statementFrequencyId:
          settlement.customerCustomerType?.statementFrequency
            ?.statementFrequencyId ?? 0,
        ...(settlement.customerCustomerType?.statementFrequency
          ?.statementFrequencyCode && {
          statementFrequencyCode:
            settlement.customerCustomerType.statementFrequency
              .statementFrequencyCode,
        }),
      },
      customerType: {
        customerTypeName:
          settlement.customerCustomerType?.customerType?.customerTypeName ?? "",
      },
    },
  };
};

// Find the most recent settlement that ended on the previous date
export const fetchPreviousSettlementForEndBalance = async (
  prisma: PrismaClient,
  customerId: number,
  platformId: number,
  previousDate: Date
): Promise<PreviousSettlement | undefined> => {
  const settlement = await prisma.customerSettlements.findFirst({
    where: {
      customerId,
      platformId,
      toDate: previousDate,
      deletedAt: null,
    },
    orderBy: {
      toDate: "desc",
    },
    select: {
      customerSettlementsId: true,
      endBalance: true,
      toDate: true,
      customerId: true,
      platformId: true,
      status: true,
    },
  });

  if (!settlement) {
    return;
  }

  return {
    customerSettlementsId: settlement.customerSettlementsId,
    endBalance: settlement.endBalance?.toString() ?? "0",
    toDate: settlement.toDate,
    customerId: settlement.customerId,
    platformId: settlement.platformId,
    status: settlement.status ?? "",
  };
};
