export type ApprovalCustomerSettlement = {
  customerSettlementsId: number;
  customerId: number;
  fromDate: Date | undefined;
  toDate: Date | undefined;
  endBalance: number;
  platform: {
    platformId: number;
    platformCode: string;
  };
  customer: {
    customerName: string;
    serviceNumber: string;
    customerTradingName: string;
    wireGroup: {
      wireConfiguration: {
        isHold: boolean;
        thresholdAmount: number;
      };
      beneficiary: {
        reference: string;
        bankAccount: {
          bankPaymentRailId: number;
        };
        cryptoAccount?: {
          cryptoPaymentRailId: number;
        };
      };
    };
    emailConfiguration: {
      isEmailEnabled: true;
    };
  };
  customerCustomerType: {
    statementFolderLocation?: string;
    statementFrequency: {
      statementFrequencyId: number;
      statementFrequencyCode?: string;
    };
    customerType: {
      customerTypeName: string;
    };
  };
};

export type PreviousSettlement = {
  customerSettlementsId: number;
  endBalance: string;
  toDate: Date;
  customerId: number;
  platformId: number;
  status: string;
};
