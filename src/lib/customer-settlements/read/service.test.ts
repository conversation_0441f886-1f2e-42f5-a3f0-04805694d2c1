import { type PrismaClient } from "@prisma/client";
import { describe, it, expect, vi, beforeEach } from "vitest";

import { fetchSettlementDetailsForApproval } from "./repository";
import { getSettlementForApproval } from "./service";

import type { ApprovalCustomerSettlement } from "./type";

vi.mock("./repository", () => ({
  fetchSettlementDetailsForApproval: vi.fn(),
}));

const mockFetchSettlementDetailsForApproval = vi.mocked(
  fetchSettlementDetailsForApproval
);

describe("getSettlementForApproval", () => {
  let mockPrisma: PrismaClient;
  const settlementId = 123;

  beforeEach(() => {
    mockPrisma = {} as unknown as PrismaClient;
    vi.clearAllMocks();
  });

  it("should return settlement when valid settlement with SUMMARY platform is found", async () => {
    const mockSettlement: ApprovalCustomerSettlement = {
      customerSettlementsId: settlementId,
      customerId: 1,
      fromDate: new Date("2025-01-01"),
      toDate: new Date("2025-01-31"),
      endBalance: 1000,
      platform: {
        platformId: 1,
        platformCode: "SUMMARY",
      },
      customer: {
        customerName: "Test Customer",
        serviceNumber: "SN123",
        customerTradingName: "Test Trading",
        wireGroup: {
          wireConfiguration: {
            isHold: false,
            thresholdAmount: 500,
          },
          beneficiary: {
            reference: "REF123",
            bankAccount: {
              bankPaymentRailId: 1,
            },
          },
        },
        emailConfiguration: {
          isEmailEnabled: true,
        },
      },
      customerCustomerType: {
        statementFolderLocation: "/test/folder",
        statementFrequency: {
          statementFrequencyId: 1,
          statementFrequencyCode: "M",
        },
        customerType: {
          customerTypeName: "Test Type",
        },
      },
    };

    mockFetchSettlementDetailsForApproval.mockResolvedValue(mockSettlement);

    const result = await getSettlementForApproval(mockPrisma, settlementId);

    expect(result).toEqual(mockSettlement);
    expect(mockFetchSettlementDetailsForApproval).toHaveBeenCalledWith(
      mockPrisma,
      settlementId
    );
    expect(mockFetchSettlementDetailsForApproval).toHaveBeenCalledTimes(1);
  });

  it("should throw error when settlement is not found", async () => {
    // eslint-disable-next-line unicorn/no-useless-undefined
    mockFetchSettlementDetailsForApproval.mockResolvedValue(undefined);

    await expect(
      getSettlementForApproval(mockPrisma, settlementId)
    ).rejects.toThrow(
      "Unable to create wire record because the specified settlement was not found."
    );

    expect(mockFetchSettlementDetailsForApproval).toHaveBeenCalledWith(
      mockPrisma,
      settlementId
    );
    expect(mockFetchSettlementDetailsForApproval).toHaveBeenCalledTimes(1);
  });

  it("should throw error when settlement platform is not SUMMARY", async () => {
    const mockSettlement: ApprovalCustomerSettlement = {
      customerSettlementsId: settlementId,
      customerId: 1,
      fromDate: new Date("2025-01-01"),
      toDate: new Date("2025-01-31"),
      endBalance: 1000,
      platform: {
        platformId: 1,
        platformCode: "OTHER_PLATFORM",
      },
      customer: {
        customerName: "Test Customer",
        serviceNumber: "SN123",
        customerTradingName: "Test Trading",
        wireGroup: {
          wireConfiguration: {
            isHold: false,
            thresholdAmount: 500,
          },
          beneficiary: {
            reference: "REF123",
            bankAccount: {
              bankPaymentRailId: 1,
            },
          },
        },
        emailConfiguration: {
          isEmailEnabled: true,
        },
      },
      customerCustomerType: {
        statementFolderLocation: "/test/folder",
        statementFrequency: {
          statementFrequencyId: 1,
          statementFrequencyCode: "M",
        },
        customerType: {
          customerTypeName: "Test Type",
        },
      },
    };

    mockFetchSettlementDetailsForApproval.mockResolvedValue(mockSettlement);

    await expect(
      getSettlementForApproval(mockPrisma, settlementId)
    ).rejects.toThrow(
      "Unable to create wire record because the settlement is not a summary."
    );

    expect(mockFetchSettlementDetailsForApproval).toHaveBeenCalledWith(
      mockPrisma,
      settlementId
    );
    expect(mockFetchSettlementDetailsForApproval).toHaveBeenCalledTimes(1);
  });
});
