import { fetchSettlementDetailsForApproval } from "@lib/customer-settlements/read/repository";

import type { ApprovalCustomerSettlement } from "@lib/customer-settlements/read/type";
import type { PrismaClient } from "@prisma/client";

export const getSettlementForApproval = async (
  prisma: PrismaClient,
  settlementId: number
): Promise<ApprovalCustomerSettlement> => {
  const settlement = await fetchSettlementDetailsForApproval(
    prisma,
    settlementId
  );

  if (!settlement) {
    throw new Error(
      "Unable to create wire record because the specified settlement was not found."
    );
  }

  if (settlement.platform.platformCode !== "SUMMARY") {
    throw new Error(
      "Unable to create wire record because the settlement is not a summary."
    );
  }

  return settlement;
};
