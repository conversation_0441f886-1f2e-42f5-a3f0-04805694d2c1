import { type UpdateApprovalCustomerSettlementsParameter } from "@lib/customer-settlements/update/type";

import type { PrismaClient } from "@prisma/client";

/**
 * Updates multiple customer settlements to approved status
 */
export const updateCustomerSettlementsToApproved = async (
  prisma: PrismaClient,
  {
    customerId,
    fromDate,
    toDate,
    approvedBy,
  }: UpdateApprovalCustomerSettlementsParameter
) => {
  return prisma.customerSettlements.updateMany({
    where: {
      customerId,
      fromDate: new Date(fromDate),
      toDate: new Date(toDate),
      deletedAt: null,
    },
    data: {
      status: "APPROVED",
      approvedBy,
    },
  });
};

/**
 * Updates a specific customer settlement with approved status and end balance
 */
export const updateCustomerSettlementWithEndBalance = async (
  prisma: PrismaClient,
  customerSettlementsId: number,
  endBalance: number,
  approvedBy: number
) => {
  return prisma.customerSettlements.update({
    where: {
      customerSettlementsId,
    },
    data: {
      status: "APPROVED",
      endBalance,
      approvedBy,
    },
  });
};
