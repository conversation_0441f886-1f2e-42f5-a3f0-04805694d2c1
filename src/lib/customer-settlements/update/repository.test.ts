import { describe, it, expect, vi, beforeEach } from "vitest";

import { updateCustomerSettlementsToApproved } from "./repository";

import type { UpdateApprovalCustomerSettlementsParameter } from "./type";
import type { PrismaClient } from "@prisma/client";

describe("updateCustomerSettlementsToApproved", () => {
  let mockPrisma: PrismaClient;
  let mockUpdateMany: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    mockUpdateMany = vi.fn();
    mockPrisma = {
      customerSettlements: {
        updateMany: mockUpdateMany,
      },
    } as unknown as PrismaClient;
  });

  it("should update customer settlements to approved status with correct parameters", async () => {
    const mockResult = { count: 3 };
    mockUpdateMany.mockResolvedValue(mockResult);

    const parameters: UpdateApprovalCustomerSettlementsParameter = {
      customerId: 123,
      fromDate: "2024-01-01",
      toDate: "2024-01-31",
      approvedBy: 123,
    };

    const result = await updateCustomerSettlementsToApproved(
      mockPrisma,
      parameters
    );

    expect(mockUpdateMany).toHaveBeenCalledWith({
      where: {
        customerId: 123,
        fromDate: new Date("2024-01-01"),
        toDate: new Date("2024-01-31"),
        deletedAt: null,
      },
      data: {
        status: "APPROVED",
        approvedBy: 123,
      },
    });
    expect(result).toEqual(mockResult);
  });

  it("should handle different date formats correctly", async () => {
    const mockResult = { count: 1 };
    mockUpdateMany.mockResolvedValue(mockResult);

    const parameters: UpdateApprovalCustomerSettlementsParameter = {
      customerId: 456,
      fromDate: "2024-12-01T00:00:00.000Z",
      toDate: "2024-12-31T23:59:59.999Z",
      approvedBy: 456,
    };

    const result = await updateCustomerSettlementsToApproved(
      mockPrisma,
      parameters
    );

    expect(mockUpdateMany).toHaveBeenCalledWith({
      where: {
        customerId: 456,
        fromDate: new Date("2024-12-01T00:00:00.000Z"),
        toDate: new Date("2024-12-31T23:59:59.999Z"),
        deletedAt: null,
      },
      data: {
        status: "APPROVED",
        approvedBy: 456,
      },
    });
    expect(result).toEqual(mockResult);
  });

  it("should handle zero updates correctly", async () => {
    const mockResult = { count: 0 };
    mockUpdateMany.mockResolvedValue(mockResult);

    const parameters: UpdateApprovalCustomerSettlementsParameter = {
      customerId: 789,
      fromDate: "2024-02-01",
      toDate: "2024-02-28",
      approvedBy: 789,
    };

    const result = await updateCustomerSettlementsToApproved(
      mockPrisma,
      parameters
    );

    expect(mockUpdateMany).toHaveBeenCalledWith({
      where: {
        customerId: 789,
        fromDate: new Date("2024-02-01"),
        toDate: new Date("2024-02-28"),
        deletedAt: null,
      },
      data: {
        status: "APPROVED",
        approvedBy: 789,
      },
    });
    expect(result).toEqual(mockResult);
  });

  it("should handle database errors correctly", async () => {
    const mockError = new Error("Database connection failed");
    mockUpdateMany.mockRejectedValue(mockError);

    const parameters: UpdateApprovalCustomerSettlementsParameter = {
      customerId: 999,
      fromDate: "2024-03-01",
      toDate: "2024-03-31",
      approvedBy: 999,
    };

    await expect(
      updateCustomerSettlementsToApproved(mockPrisma, parameters)
    ).rejects.toThrow("Database connection failed");

    expect(mockUpdateMany).toHaveBeenCalledWith({
      where: {
        customerId: 999,
        fromDate: new Date("2024-03-01"),
        toDate: new Date("2024-03-31"),
        deletedAt: null,
      },
      data: {
        status: "APPROVED",
        approvedBy: 999,
      },
    });
  });

  it("should always set status to APPROVED", async () => {
    const mockResult = { count: 5 };
    mockUpdateMany.mockResolvedValue(mockResult);

    const parameters: UpdateApprovalCustomerSettlementsParameter = {
      customerId: 222,
      fromDate: "2024-05-01",
      toDate: "2024-05-31",
      approvedBy: 222,
    };

    await updateCustomerSettlementsToApproved(mockPrisma, parameters);

    expect(mockUpdateMany).toHaveBeenCalledWith({
      where: {
        customerId: 222,
        fromDate: new Date("2024-05-01"),
        toDate: new Date("2024-05-31"),
        deletedAt: null,
      },
      data: {
        status: "APPROVED",
        approvedBy: 222,
      },
    });
  });

  it("should handle invalid date strings gracefully", async () => {
    const mockResult = { count: 1 };
    mockUpdateMany.mockResolvedValue(mockResult);

    const parameters: UpdateApprovalCustomerSettlementsParameter = {
      customerId: 333,
      fromDate: "invalid-date",
      toDate: "another-invalid-date",
      approvedBy: 333,
    };

    const result = await updateCustomerSettlementsToApproved(
      mockPrisma,
      parameters
    );

    expect(mockUpdateMany).toHaveBeenCalledWith({
      where: {
        customerId: 333,
        fromDate: new Date("invalid-date"),
        toDate: new Date("another-invalid-date"),
        deletedAt: null,
      },
      data: {
        status: "APPROVED",
        approvedBy: 333,
      },
    });
    expect(result).toEqual(mockResult);
  });

  it("should handle empty approvedBy string", async () => {
    const mockResult = { count: 1 };
    mockUpdateMany.mockResolvedValue(mockResult);

    const parameters: UpdateApprovalCustomerSettlementsParameter = {
      customerId: 444,
      fromDate: "2024-06-01",
      toDate: "2024-06-30",
      approvedBy: 123,
    };

    const result = await updateCustomerSettlementsToApproved(
      mockPrisma,
      parameters
    );

    expect(mockUpdateMany).toHaveBeenCalledWith({
      where: {
        customerId: 444,
        fromDate: new Date("2024-06-01"),
        toDate: new Date("2024-06-30"),
        deletedAt: null,
      },
      data: {
        status: "APPROVED",
        approvedBy: 123,
      },
    });
    expect(result).toEqual(mockResult);
  });
});
