import { resolveFilePath, hasPermission } from "../../../utils/file-system";

import type { FastifyReply, FastifyRequest } from "fastify";

export const handler = async (
  request: FastifyRequest<{
    Querystring: { filepath: string };
  }>,
  reply: FastifyReply
) => {
  try {
    const { filepath } = request.query;

    const resolvedPath: string = resolveFilePath(filepath);

    const { isSuccess, error: permissionError } = await hasPermission(
      resolvedPath,
      "read"
    );

    if (isSuccess) {
      return await reply.type("application/octet-stream").sendFile(filepath);
    }

    const { code } = permissionError!;

    if (code === "EACCES" || code === "EPERM") {
      return await reply.code(403).send({
        message: "File cannot be downloaded due to insufficient permissions.",
      });
    }

    return await reply.code(404).send({
      message: "File not found.",
    });
  } catch (error) {
    request.log.error(error);

    return reply.code(500).send({ message: (error as Error)?.message });
  }
};
