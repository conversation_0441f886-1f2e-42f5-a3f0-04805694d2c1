import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Download a File",
  description:
    "Allows users to download a file if they have sufficient permissions.",
  tags: [tags.filesystem],
  querystring: {
    type: "object",
    properties: {
      filepath: {
        type: "string",
        description: "The relative path to the file to be downloaded.",
        minLength: 1,
      },
    },
    required: ["filepath"],
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful response with file download",
      type: "string",
      contentMediaType: "application/octet-stream",
      example: "File content",
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    403: {
      description: "Insufficient permissions.",
      type: "object",
      properties: {
        message: {
          type: "string",
          example: "File cannot be downloaded due to insufficient permissions.",
        },
      },
      required: ["message"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    404: {
      description: "File not found or insufficient permissions",
      type: "object",
      properties: {
        message: {
          type: "string",
          example: "File not found or insufficient permissions.",
        },
      },
      required: ["message"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Internal server error",
      type: "object",
      properties: {
        message: {
          type: "string",
          example: "An error occurred while processing the request.",
        },
      },
      required: ["message"],
    },
  },
};
