import { promises as fs } from "node:fs";
import { join } from "node:path";

import { type FileItem } from "./types";
import { resolveFilePath } from "../../../utils/file-system";

const getFiles = async (directoryPath: string): Promise<FileItem[]> => {
  const resolvedPath: string = resolveFilePath(directoryPath);

  const items = await fs.readdir(resolvedPath, { withFileTypes: true });

  const promises = items.map(async (item) => {
    const fullPath = join(resolvedPath, item.name);

    const stats = await fs.stat(fullPath);
    const modified = stats.mtime.toISOString();
    const sizeInBytes = stats.size;
    const sizeInKb = sizeInBytes / 1024;

    return {
      name: item.name,
      type: item.isDirectory() ? "folder" : "file",
      modified,
      size: item.isDirectory() ? undefined : Math.round(sizeInKb * 100) / 100,
    } satisfies FileItem;
  });

  const unsortedItems = await Promise.all(promises);

  return unsortedItems.sort((a, b) => {
    if (a.type === b.type) {
      return a.name.localeCompare(b.name);
    }

    return b.type.localeCompare(a.type);
  });
};

export { getFiles };
