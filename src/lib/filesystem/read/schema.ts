import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Fetch files from a specified filepath",
  description:
    "Retrieve a list of files or folders from the provided filepath.",
  tags: [tags.filesystem],
  querystring: {
    type: "object",
    properties: {
      filepath: {
        type: "string",
        description: "The path to retrieve files from",
      },
    },
    required: ["filepath"],
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful response with a list of files",
      type: "array",
      items: {
        type: "object",
        properties: {
          name: {
            type: "string",
            description: "The name of the file or folder",
          },
          type: {
            type: "string",
            enum: ["file", "folder"],
            description: "The type of item",
          },
          modified: { type: "string", description: "Last modified date" },
          size: {
            type: "number",
            description: "Size of the file in kilo bytes",
            nullable: true,
          },
        },
        required: ["name", "type", "modified"],
      },
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Internal server error",
      type: "object",
      properties: {
        message: { type: "string", description: "Error message" },
      },
    },
  },
};
