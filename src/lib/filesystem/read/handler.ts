import { getFiles } from "./functions";

import type { FastifyReply, FastifyRequest } from "fastify";

export const handler = async (
  request: FastifyRequest<{
    Querystring: { filepath: string };
  }>,
  reply: FastifyReply
) => {
  const { filepath } = request.query;

  try {
    const files = await getFiles(filepath);

    return await reply.send(files);
  } catch (error) {
    request.log.error(error);

    return reply.code(500).send({ message: (error as Error)?.message });
  }
};
