import { constants } from "node:fs";
import { mkdir, access } from "node:fs/promises";
import path from "node:path";

import { resolveFilePath, hasPermission } from "../../../utils/file-system";

async function createFolder(parentPath: string, name: string): Promise<void> {
  const resolvedParentPath: string = resolveFilePath(parentPath);
  const resolvedPath: string = resolveFilePath(`${parentPath}/${name}`);

  try {
    const { isSuccess: hasWritePermission, error: permissionError } =
      await hasPermission(resolvedParentPath, "write");

    if (!hasWritePermission) {
      const { code } = permissionError!;

      if (code === "ENOENT") {
        throw new Error(
          `Cannot create folder "${name}" because the parent directory "${path.basename(parentPath)}" does not exist.`
        );
      } else if (code === "EACCES" || code === "EPERM") {
        throw new Error(
          `Cannot create folder "${name}" due to insufficient permissions in the parent directory "${path.basename(parentPath)}".`
        );
      } else {
        throw new Error(permissionError?.message ?? "Unknown error occurred");
      }
    }

    // Check if the folder already exists
    await access(resolvedPath, constants.F_OK);
    throw new Error(`Folder "${path.basename(resolvedPath)}" already exists.`);
  } catch (error) {
    // The folder does not exist, and we can create it
    if (error instanceof Error && "code" in error && error?.code === "ENOENT") {
      await mkdir(resolvedPath, { recursive: true });
    } else {
      throw new Error(
        `${(error as Error).message ?? "Unknown error occurred"}`
      );
    }
  }
}

export { createFolder };
