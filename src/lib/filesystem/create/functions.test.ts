import { constants } from "node:fs";
import { mkdir, access } from "node:fs/promises";
import path from "node:path";

import { describe, it, expect, vi, type MockedFunction } from "vitest";

import { createFolder } from "./functions";
import { resolveFilePath, hasPermission } from "../../../utils/file-system";

vi.mock("node:fs/promises", () => ({
  access: vi.fn(),
  mkdir: vi.fn(),
}));

vi.mock("../../../utils/file-system", () => ({
  resolveFilePath: vi.fn((p: string) => p),
  hasPermission: vi.fn(),
}));

describe("createFolder", () => {
  const parentPath = "/test-parent";
  const folderName = "test-folder";
  const resolvedParentPath = parentPath;
  const resolvedPath = path.join(parentPath, folderName);

  const accessMock = access as MockedFunction<typeof access>;
  const mkdirMock = mkdir as MockedFunction<typeof mkdir>;
  const hasPermissionMock = hasPermission as MockedFunction<
    typeof hasPermission
  >;
  const resolveFilePathMock = resolveFilePath as MockedFunction<
    typeof resolveFilePath
  >;

  beforeEach(() => {
    vi.resetAllMocks();

    resolveFilePathMock.mockImplementation((p: string) => p);
  });

  it("should throw an error if the parent directory does not exist", async () => {
    const enoentError = new Error("ENOENT") as NodeJS.ErrnoException;
    enoentError.code = "ENOENT";
    hasPermissionMock.mockResolvedValue({
      isSuccess: false,
      error: enoentError,
    });

    await expect(createFolder(parentPath, folderName)).rejects.toThrow(
      `Cannot create folder "${folderName}" because the parent directory "${path.basename(parentPath)}" does not exist.`
    );
    expect(hasPermissionMock).toHaveBeenCalledWith(resolvedParentPath, "write");
    expect(accessMock).not.toHaveBeenCalled();
    expect(mkdirMock).not.toHaveBeenCalled();
  });

  it("should throw an error if there are insufficient permissions in the parent directory", async () => {
    const eaccesError = new Error("EACCES") as NodeJS.ErrnoException;
    eaccesError.code = "EACCES";
    hasPermissionMock.mockResolvedValue({
      isSuccess: false,
      error: eaccesError,
    });

    await expect(createFolder(parentPath, folderName)).rejects.toThrow(
      `Cannot create folder "${folderName}" due to insufficient permissions in the parent directory "${path.basename(parentPath)}".`
    );
    expect(hasPermissionMock).toHaveBeenCalledWith(resolvedParentPath, "write");
    expect(accessMock).not.toHaveBeenCalled();
    expect(mkdirMock).not.toHaveBeenCalled();
  });

  it("should throw an error if the folder already exists", async () => {
    hasPermissionMock.mockResolvedValue({ isSuccess: true });
    accessMock.mockResolvedValue();

    await expect(createFolder(parentPath, folderName)).rejects.toThrow();
    expect(hasPermissionMock).toHaveBeenCalledWith(resolvedParentPath, "write");
    expect(accessMock).toHaveBeenCalledWith(resolvedPath, constants.F_OK);
    expect(mkdirMock).not.toHaveBeenCalled();
  });

  it("should create the folder if it does not exist and permissions are sufficient", async () => {
    hasPermissionMock.mockResolvedValue({ isSuccess: true });
    const enoentError = new Error("ENOENT") as NodeJS.ErrnoException;
    enoentError.code = "ENOENT";
    accessMock.mockRejectedValue(enoentError);

    await createFolder(parentPath, folderName);

    expect(hasPermissionMock).toHaveBeenCalledWith(resolvedParentPath, "write");
    expect(accessMock).toHaveBeenCalledWith(resolvedPath, constants.F_OK);
    expect(mkdirMock).toHaveBeenCalledWith(resolvedPath, { recursive: true });
  });

  it("should throw an error if access throws an unexpected error", async () => {
    hasPermissionMock.mockResolvedValue({ isSuccess: true });
    const unexpectedError = new Error(
      "Unexpected error"
    ) as NodeJS.ErrnoException;
    unexpectedError.code = "EUNKNOWN";
    accessMock.mockRejectedValue(unexpectedError);

    await expect(createFolder(parentPath, folderName)).rejects.toThrow();

    expect(hasPermissionMock).toHaveBeenCalledWith(resolvedParentPath, "write");
    expect(accessMock).toHaveBeenCalledWith(resolvedPath, constants.F_OK);
    expect(mkdirMock).not.toHaveBeenCalled();
  });
});
