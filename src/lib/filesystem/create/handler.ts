import { createFolder } from "./functions";

import type { FastifyReply, FastifyRequest } from "fastify";

type RequestBody = {
  parentPath: string;
  name: string;
};

export const handler = async (
  request: FastifyRequest<{ Body: RequestBody }>,
  reply: FastifyReply
) => {
  const { parentPath, name } = request.body;

  try {
    await createFolder(parentPath, name);

    return await reply.send({});
  } catch (error) {
    request.log.error(error);

    return reply.code(500).send({ message: (error as Error)?.message });
  }
};
