import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Create a folder in the filesystem",
  description: `
    This endpoint creates a folder at the specified path within the base directory
    and any path outside this directory is considered invalid.
  `,
  tags: [tags.filesystem],
  body: {
    type: "object",
    properties: {
      parentPath: {
        type: "string",
        description: "The relative path where the folder will be created",
        minLength: 1,
      },
      name: {
        type: "string",
        description: "The name of the new folder be created",
        minLength: 1,
      },
    },
    required: ["parentPath", "name"],
    additionalProperties: false,
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Folder successfully created",
      type: "object",
      properties: {},
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    400: {
      description: "Invalid folder path",
      type: "object",
      properties: {
        message: { type: "string" },
      },
      required: ["message"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Server error",
      type: "object",
      properties: {
        message: { type: "string" },
      },
      required: ["message"],
    },
  },
};
