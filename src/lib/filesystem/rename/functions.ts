import { promises as fs } from "node:fs";
import path from "node:path";

import {
  getCustomersByFolderPath,
  updateFolderPathForAllCustomers,
} from "@lib/common/customer-by-folder-path/functions";
import { type PrismaClient } from "@prisma/client";

import { resolveFilePath } from "../../../utils/file-system";

const renameFile = async (
  prisma: PrismaClient,
  filePath: string,
  newName: string
): Promise<void> => {
  const resolvedPath: string = resolveFilePath(filePath);
  const resolvedNewPath = path.join(path.dirname(resolvedPath), newName);
  const fileStats = await fs.lstat(resolvedPath);
  const newPath = path.join(path.dirname(filePath), newName);

  //  Files are created by the application hence renamed manually is not allowed.
  // Only folders can be renamed.
  if (!fileStats.isDirectory()) {
    throw new Error("Only folder names can be renamed.");
  }

  if (resolvedPath === resolvedNewPath) {
    throw new Error(
      "The file name remains the same. Please choose a different name to rename the file."
    );
  }

  // Perform the file rename operation first
  await fs.rename(resolvedPath, resolvedNewPath);

  try {
    const customers = await getCustomersByFolderPath(prisma, filePath);

    if (fileStats.isDirectory() && customers.length > 0) {
      await prisma.$transaction(
        async (prismaTransaction) => {
          // This will only perform updates if the folder path exists in the database
          const updateSuccess = await updateFolderPathForAllCustomers(
            prismaTransaction,
            filePath,
            newPath
          );

          if (!updateSuccess) {
            // Roll back the file rename
            await fs.rename(resolvedNewPath, resolvedPath);
            throw new Error("Failed to update folder path for all customers.");
          }
        },
        { timeout: 500_000, maxWait: 100_000 }
      );
    }
  } catch (error) {
    // Roll back the file rename
    await fs.rename(resolvedNewPath, resolvedPath);
    throw error;
  }
};

export { renameFile };
