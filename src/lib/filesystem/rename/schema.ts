import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Rename a file or folder in the filesystem",
  description: `
    This endpoint renames a file or folder at the specified path within the base directory.
    The new name must not contain invalid characters, and the path should remain within the base directory.
    Any path outside this directory is considered invalid.
  `,
  tags: [tags.filesystem],
  body: {
    type: "object",
    required: ["filePath", "newName"],
    properties: {
      filePath: {
        type: "string",
        description: "The current path of the file or folder to be renamed.",
        minLength: 1,
      },
      newName: {
        type: "string",
        description: "The new name for the file or folder.",
        minLength: 1,
      },
    },
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Success response after renaming the file or folder",
      type: "object",
      properties: {},
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    400: {
      description: "Bad request due to invalid file path or name",
      type: "object",
      properties: {
        message: { type: "string" },
      },
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Internal server error during the renaming process",
      type: "object",
      required: ["message"],
      properties: {
        message: { type: "string" },
      },
    },
  },
};
