import { renameFile } from "./functions";

import type { FastifyReply, FastifyRequest } from "fastify";

type RequestBody = {
  filePath: string;
  newName: string;
};

export const handler = async (
  request: FastifyRequest<{ Body: RequestBody }>,
  reply: FastifyReply
) => {
  const { filePath, newName } = request.body;

  try {
    await renameFile(request.server.prisma, filePath, newName);

    return await reply.send({});
  } catch (error) {
    request.log.error(error);

    return reply.code(500).send({ message: (error as Error)?.message });
  }
};
