import { promises as fs } from "node:fs";
import path from "node:path";

import { getPaymentEnabledPlatformCodes } from "@lib/platform/read/repositories";
import { beforeEach, describe, expect, it, vi } from "vitest";

import { getAttachments } from "./service";

import type { PrismaClient } from "@prisma/client";
import type { Dirent } from "node:fs";

type MockPlatform = {
  platformCode: string;
};

type MockCustomer = {
  customerTradingName: string;
};

type MockCustomerType = {
  customerTypeName: string;
};

type MockCustomerCustomerType = {
  statementFolderLocation?: string;
  customerType?: MockCustomerType;
};

type MockSettlement = {
  fromDate: string;
  toDate: string;
  customer?: MockCustomer;
  customerCustomerType?: MockCustomerCustomerType;
};

vi.mock("node:fs", () => ({
  promises: {
    readdir: vi.fn(),
  },
}));

vi.mock("@constants/environment", () => ({
  environment: {
    baseFileLocation: "/base/path",
  },
}));

vi.mock("@lib/platform/read/repositories", () => ({
  getPaymentEnabledPlatformCodes: vi.fn(),
}));

const mockFs = vi.mocked(fs);

describe("getAttachments", () => {
  let mockPrisma: PrismaClient;
  let mockSettlement: MockSettlement;

  beforeEach(() => {
    vi.clearAllMocks();
    mockPrisma = {} as unknown as PrismaClient;

    const mockGetPaymentEnabledPlatformCodes = vi.mocked(
      getPaymentEnabledPlatformCodes
    );

    mockSettlement = {
      fromDate: "2023-01-01",
      toDate: "2023-01-31",
      customer: {
        customerTradingName: "TestCustomer",
      },
      customerCustomerType: {
        statementFolderLocation: "statements/folder",
        customerType: {
          customerTypeName: "Merchant",
        },
      },
    };

    mockGetPaymentEnabledPlatformCodes.mockResolvedValue([
      { platformCode: "PLATFORM1" },
      { platformCode: "PLATFORM2" },
    ] as MockPlatform[]);
  });

  describe("when statementFolderLocation is missing", () => {
    it("should return empty array", async () => {
      const settlementWithoutFolder = {
        ...mockSettlement,
        customerCustomerType: {
          ...mockSettlement.customerCustomerType,
          statementFolderLocation: undefined,
        },
      };

      const result = await getAttachments(
        mockPrisma,
        settlementWithoutFolder as never
      );

      expect(result).toEqual([]);
    });
  });

  describe("when customerTypeName is Merchant", () => {
    it("should return attachments for all platforms including Summary", async () => {
      mockFs.readdir
        .mockResolvedValueOnce([
          "TestCustomer-2023-01-01-2023-01-31-PLATFORM1 rev1.xlsx",
          "TestCustomer-2023-01-01-2023-01-31-PLATFORM1 rev2.xlsx",
        ] as unknown as Dirent[])
        .mockResolvedValueOnce([
          "TestCustomer-2023-01-01-2023-01-31-PLATFORM2 rev1.xlsx",
        ] as unknown as Dirent[])
        .mockResolvedValueOnce([
          "TestCustomer-2023-01-01-2023-01-31-Summary.xlsx",
        ] as unknown as Dirent[]);

      const result = await getAttachments(mockPrisma, mockSettlement as never);

      expect(result).toEqual([
        path.join(
          "statements/folder",
          "PLATFORM1",
          "TestCustomer-2023-01-01-2023-01-31-PLATFORM1 rev2.xlsx"
        ),
        path.join(
          "statements/folder",
          "PLATFORM2",
          "TestCustomer-2023-01-01-2023-01-31-PLATFORM2 rev1.xlsx"
        ),
        path.join(
          "statements/folder",
          "Summary",
          "TestCustomer-2023-01-01-2023-01-31-Summary.xlsx"
        ),
      ]);

      expect(mockFs.readdir).toHaveBeenCalledTimes(3);
      expect(mockFs.readdir).toHaveBeenCalledWith(
        "/base/path/statements/folder/PLATFORM1"
      );
      expect(mockFs.readdir).toHaveBeenCalledWith(
        "/base/path/statements/folder/PLATFORM2"
      );
      expect(mockFs.readdir).toHaveBeenCalledWith(
        "/base/path/statements/folder/Summary"
      );
    });

    it("should handle missing directories gracefully", async () => {
      mockFs.readdir
        .mockRejectedValueOnce(new Error("Directory not found"))
        .mockResolvedValueOnce([
          "TestCustomer-2023-01-01-2023-01-31-PLATFORM2 rev1.xlsx",
        ] as unknown as Dirent[])
        .mockRejectedValueOnce(new Error("Directory not found"));

      const result = await getAttachments(mockPrisma, mockSettlement as never);

      expect(result).toEqual([
        path.join(
          "statements/folder",
          "PLATFORM2",
          "TestCustomer-2023-01-01-2023-01-31-PLATFORM2 rev1.xlsx"
        ),
      ]);
    });

    it("should handle empty directories", async () => {
      mockFs.readdir
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([]);

      const result = await getAttachments(mockPrisma, mockSettlement as never);

      expect(result).toEqual([]);
    });

    it("should handle directories with no matching files", async () => {
      mockFs.readdir
        .mockResolvedValueOnce(["other-file.xlsx"] as unknown as Dirent[])
        .mockResolvedValueOnce(["another-file.xlsx"] as unknown as Dirent[])
        .mockResolvedValueOnce(["different-file.xlsx"] as unknown as Dirent[]);

      const result = await getAttachments(mockPrisma, mockSettlement as never);

      expect(result).toEqual([]);
    });
  });

  describe("date formatting", () => {
    it("should format dates correctly", async () => {
      const settlementWithDifferentDates = {
        ...mockSettlement,
        fromDate: "2023-12-05",
        toDate: "2023-12-31",
        customerCustomerType: {
          ...mockSettlement.customerCustomerType,
          customerType: {
            customerTypeName: "NonMerchant",
          },
        },
      };

      mockFs.readdir.mockResolvedValueOnce([
        "TestCustomer-2023-12-05-2023-12-31-final.xlsx",
      ] as unknown as Dirent[]);

      const result = await getAttachments(
        mockPrisma,
        settlementWithDifferentDates as never
      );

      expect(result).toEqual([
        path.join(
          "statements/folder",
          "TestCustomer-2023-12-05-2023-12-31-final.xlsx"
        ),
      ]);
    });
  });
});

describe("getHighestRevFile functionality", () => {
  let mockPrisma: PrismaClient;
  let mockSettlement: MockSettlement;

  beforeEach(async () => {
    vi.clearAllMocks();
    mockPrisma = {} as unknown as PrismaClient;

    const { getPaymentEnabledPlatformCodes } = await import(
      "@lib/platform/read/repositories"
    );
    const mockGetPaymentEnabledPlatformCodes = vi.mocked(
      getPaymentEnabledPlatformCodes
    );

    mockSettlement = {
      fromDate: "2023-01-01",
      toDate: "2023-01-31",
      customer: {
        customerTradingName: "TestCustomer",
      },
      customerCustomerType: {
        statementFolderLocation: "statements/folder",
        customerType: {
          customerTypeName: "Merchant",
        },
      },
    };

    mockGetPaymentEnabledPlatformCodes.mockResolvedValue([
      { platformCode: "PLATFORM1" },
    ] as MockPlatform[]);
  });

  it("should select file with highest revision number", async () => {
    mockFs.readdir.mockResolvedValueOnce([
      "TestCustomer-2023-01-01-2023-01-31-PLATFORM1 rev1.xlsx",
      "TestCustomer-2023-01-01-2023-01-31-PLATFORM1 rev5.xlsx",
      "TestCustomer-2023-01-01-2023-01-31-PLATFORM1 rev3.xlsx",
    ] as unknown as Dirent[]);

    const result = await getAttachments(mockPrisma, mockSettlement as never);

    expect(result).toEqual([
      path.join(
        "statements/folder",
        "PLATFORM1",
        "TestCustomer-2023-01-01-2023-01-31-PLATFORM1 rev5.xlsx"
      ),
    ]);
  });

  it("should handle files without revision numbers", async () => {
    mockFs.readdir.mockResolvedValueOnce([
      "TestCustomer-2023-01-01-2023-01-31-PLATFORM1.xlsx",
      "TestCustomer-2023-01-01-2023-01-31-PLATFORM1 rev2.xlsx",
    ] as unknown as Dirent[]);

    const result = await getAttachments(mockPrisma, mockSettlement as never);

    expect(result).toEqual([
      path.join(
        "statements/folder",
        "PLATFORM1",
        "TestCustomer-2023-01-01-2023-01-31-PLATFORM1 rev2.xlsx"
      ),
    ]);
  });

  it("should handle case insensitive revision matching", async () => {
    mockFs.readdir.mockResolvedValueOnce([
      "TestCustomer-2023-01-01-2023-01-31-PLATFORM1 REV1.xlsx",
      "TestCustomer-2023-01-01-2023-01-31-PLATFORM1 rev3.xlsx",
    ] as unknown as Dirent[]);

    const result = await getAttachments(mockPrisma, mockSettlement as never);

    expect(result).toEqual([
      path.join(
        "statements/folder",
        "PLATFORM1",
        "TestCustomer-2023-01-01-2023-01-31-PLATFORM1 rev3.xlsx"
      ),
    ]);
  });

  it("should return last file when all have revision 0 (no revision)", async () => {
    mockFs.readdir.mockResolvedValueOnce([
      "TestCustomer-2023-01-01-2023-01-31-PLATFORM1.xlsx",
      "TestCustomer-2023-01-01-2023-01-31-PLATFORM1-other.xlsx",
    ] as unknown as Dirent[]);

    const result = await getAttachments(mockPrisma, mockSettlement as never);

    expect(result).toEqual([
      path.join(
        "statements/folder",
        "PLATFORM1",
        "TestCustomer-2023-01-01-2023-01-31-PLATFORM1-other.xlsx"
      ),
    ]);
  });
});
