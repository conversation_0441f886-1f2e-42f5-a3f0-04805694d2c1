import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Delete a file or an empty folder in the filesystem",
  description: `
    This endpoint deletes a file or an empty folder at the specified path within the base directory.
    If the folder is not empty, the operation will fail. Additionally, any folder associated with a customer for 
    saving their settlement files cannot be deleted. Paths outside the base directory are considered invalid.
  `,
  tags: [tags.filesystem],
  body: {
    type: "object",
    required: ["filePath"],
    properties: {
      filePath: {
        type: "string",
        description:
          "The path of the file or folder to delete, relative to the base directory.",
        minLength: 1,
      },
    },
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "File or folder deleted successfully",
      type: "object",
      properties: {},
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    400: {
      description: "Invalid or missing filePath in the request",
      type: "object",
      properties: {
        message: {
          type: "string",
          description: "Explanation of the bad request",
        },
      },
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Internal server error",
      type: "object",
      required: ["message"],
      properties: {
        message: {
          type: "string",
          description: "Error message explaining the failure",
        },
      },
    },
  },
};
