import { deleteFile } from "./functions";

import type { FastifyReply, FastifyRequest } from "fastify";

type RequestBody = {
  filePath: string;
};

export const handler = async (
  request: FastifyRequest<{ Body: RequestBody }>,
  reply: FastifyReply
) => {
  const { filePath } = request.body;

  try {
    await deleteFile(request.server.prisma, filePath);

    return await reply.send({});
  } catch (error) {
    request.log.error(error);

    return reply.code(500).send({ message: (error as Error)?.message });
  }
};
