import { promises as fs } from "node:fs";
import path from "node:path";

import { getCustomersByFolderPath } from "@lib/common/customer-by-folder-path/functions";
import { type PrismaClient } from "@prisma/client";

import { resolveFilePath } from "../../../utils/file-system";

async function deleteFile(
  prisma: PrismaClient,
  filePath: string
): Promise<void> {
  const resolvedPath: string = resolveFilePath(filePath);

  // Check if the path is a file or a directory
  const stats = await fs.lstat(resolvedPath);

  if (stats.isDirectory()) {
    const files = await fs.readdir(resolvedPath);

    if (files.length === 0) {
      const folderUsedBy = await getCustomersByFolderPath(prisma, filePath);

      if (folderUsedBy && folderUsedBy.length > 0) {
        throw new Error(
          `Cannot delete folder "${path.basename(filePath)}" because it is 
           used by customer "${folderUsedBy[0]!.name}".`
        );
      }

      await fs.rmdir(resolvedPath);
    } else {
      throw new Error(
        `Cannot delete folder "${path.basename(filePath)}" because it is not empty.`
      );
    }
  } else {
    await fs.unlink(resolvedPath);
  }
}

export { deleteFile };
