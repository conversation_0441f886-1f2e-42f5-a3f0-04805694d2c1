import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Move Files",
  description:
    "Endpoint to move files from specified source paths to a destination path. Ensures files are not moved into themselves.",
  tags: [tags.filesystem],
  body: {
    type: "object",
    required: ["sourcePaths", "destinationPath"],
    properties: {
      sourcePaths: {
        type: "array",
        items: { type: "string", minLength: 1 },
        minItems: 1,
        description:
          "An array of file paths to be moved. Each path should be a non-empty string.",
      },
      destinationPath: {
        type: "string",
        minLength: 1,
        description:
          "The target destination path where the files should be moved. This should be a unique path that does not overlap with source paths.",
      },
    },
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      type: "object",
      properties: {
        message: { type: "string", default: "Files moved successfully" },
      },
      description: "Indicates a successful file move operation.",
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    400: {
      type: "object",
      properties: {
        message: { type: "string", default: "Cannot move a file into itself." },
      },
      description: "Error response when attempting to move a file into itself.",
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      type: "object",
      properties: {
        message: {
          type: "string",
          default: "An error occurred while moving files",
        },
      },
      description:
        "Indicates an internal server error during file move operation.",
    },
  },
};
