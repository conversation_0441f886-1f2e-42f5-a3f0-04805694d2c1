import { moveFiles } from "./functions";

import type { FastifyReply, FastifyRequest } from "fastify";

type RequestBody = {
  sourcePaths: string[];
  destinationPath: string;
};

export const handler = async (
  request: FastifyRequest<{ Body: RequestBody }>,
  reply: FastifyReply
) => {
  const { sourcePaths, destinationPath } = request.body;

  try {
    await moveFiles(request.server.prisma, sourcePaths, destinationPath);

    return await reply.send({});
  } catch (error) {
    request.log.error(error);

    return reply.code(500).send({ message: (error as Error)?.message });
  }
};
