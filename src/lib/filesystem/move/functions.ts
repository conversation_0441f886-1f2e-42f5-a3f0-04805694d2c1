import { promises as fs } from "node:fs";
import path from "node:path";

import {
  getCustomersByFolderPath,
  updateFolderPathForAllCustomers,
} from "@lib/common/customer-by-folder-path/functions";
import { type PrismaClient } from "@prisma/client";

import { hasPermission, resolveFilePath } from "../../../utils/file-system";

const moveFiles = async (
  prisma: PrismaClient,
  sourcePaths: string[],
  destinationFolderPath: string
): Promise<void> => {
  // Resolve the destination path
  const resolvedDestinationPath = resolveFilePath(destinationFolderPath);
  await _validatePathsOrThrow(sourcePaths, destinationFolderPath);

  // Track successful moves for potential rollback
  const successfulMoves: Array<{ originalPath: string; newPath: string }> = [];

  try {
    await prisma.$transaction(
      async (transactionPrisma) => {
        // Process each source path
        for (const sourcePath of sourcePaths) {
          const resolvedSourcePath = resolveFilePath(sourcePath);
          // eslint-disable-next-line no-await-in-loop
          const sourceStats = await fs.lstat(resolvedSourcePath);

          const sourceBaseName = path.basename(resolvedSourcePath);
          const resolvedNewPath = path.join(
            resolvedDestinationPath,
            sourceBaseName
          );

          // Move the file or folder
          // eslint-disable-next-line no-await-in-loop
          await fs.rename(resolvedSourcePath, resolvedNewPath);
          // Track the successful move for potential rollback
          successfulMoves.push({
            originalPath: resolvedSourcePath,
            newPath: resolvedNewPath,
          });

          // eslint-disable-next-line no-await-in-loop
          const customers = await getCustomersByFolderPath(prisma, sourcePath);

          // If it's a directory and has associated customers, update the database paths
          if (sourceStats.isDirectory() && customers.length > 0) {
            const newPath = path.join(destinationFolderPath, sourceBaseName);
            // eslint-disable-next-line no-await-in-loop
            const updateSuccess = await updateFolderPathForAllCustomers(
              transactionPrisma,
              sourcePath,
              newPath
            );

            if (!updateSuccess) {
              throw new Error(
                "Failed to update folder path for all customers."
              );
            }
          }
        }
      },
      { timeout: 500_000, maxWait: 100_000 }
    );
  } catch (error) {
    // Roll back all successful moves in case of an error
    for (const { originalPath, newPath } of successfulMoves.reverse()) {
      try {
        // eslint-disable-next-line no-await-in-loop
        await fs.rename(newPath, originalPath);
      } catch {
        // Do nothing
      }
    }

    throw error;
  }
};

function _isSubPath(parent: string, child: string): boolean {
  const relative = path.relative(parent, child);

  return Boolean(
    relative && !relative.startsWith("..") && !path.isAbsolute(relative)
  );
}

async function _validatePathsOrThrow(
  sourcePaths: string[],
  destinationFolderPath: string
) {
  const resolvedDestinationPath = resolveFilePath(destinationFolderPath);
  const destinationStats = await fs.lstat(resolvedDestinationPath);

  if (!destinationStats.isDirectory()) {
    throw new Error("Destination path must be an existing directory.");
  }

  const { isSuccess: hasWritePermission } = await hasPermission(
    resolvedDestinationPath,
    "write"
  );

  if (!hasWritePermission) {
    throw new Error(
      `Destination path ${destinationFolderPath} does not have sufficient permissions`
    );
  }

  // Validate each source path
  for (const sourcePath of sourcePaths) {
    const resolvedSourcePath = resolveFilePath(sourcePath);
    const resolvedSourceDirectory = path.dirname(resolvedSourcePath);

    // Prevent moving a folder into itself, its subdirectories, or the same directory
    if (
      _isSubPath(resolvedSourcePath, resolvedDestinationPath) ||
      resolvedSourcePath === resolvedDestinationPath ||
      resolvedSourceDirectory === resolvedDestinationPath
    ) {
      throw new Error(
        `Cannot move '${sourcePath}' into itself, its subdirectories, or the same directory.`
      );
    }

    // Check if source exists and has sufficient permissions
    // eslint-disable-next-line no-await-in-loop
    const { isSuccess: hasWritePermission } = await hasPermission(
      resolvedSourcePath,
      "write"
    );

    if (!hasWritePermission) {
      throw new Error(
        `Source path ${sourcePath} path does not have sufficient permissions`
      );
    }
  }
}

export { moveFiles };
