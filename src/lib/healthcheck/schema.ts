import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Health check",
  description: "Server health check.",
  tags: [tags.healthcheck],
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful response",
      type: "object",
      properties: {
        status: { type: "string", example: "OK" },
      },
      required: ["status"],
    },
  },
};
