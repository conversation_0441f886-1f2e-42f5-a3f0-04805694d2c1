import { type Frequency } from "../../types/frequency/frequency";

import type { PrismaClient } from "@prisma/client";

export async function getAllFrequencies(
  prisma: PrismaClient
): Promise<Frequency[]> {
  const frequencies = await prisma.statementFrequency.findMany({
    select: {
      statementFrequencyId: true,
      statementFrequencyCode: true,
      statementFrequencyName: true,
    },
  });

  return frequencies.map((frequency) => ({
    frequencyId: frequency.statementFrequencyId,
    frequencyCode: frequency.statementFrequencyCode,
    frequencyName: frequency.statementFrequencyName,
  }));
}

export async function getFrequency(
  frequencyId: number,
  prisma: PrismaClient
): Promise<Frequency | undefined> {
  const frequency = await prisma.statementFrequency.findFirst({
    where: {
      statementFrequencyId: frequencyId,
    },
    select: {
      statementFrequencyId: true,
      statementFrequencyCode: true,
      statementFrequencyName: true,
    },
  });

  if (!frequency) {
    return;
  }

  return {
    frequencyId: frequency.statementFrequencyId,
    frequencyCode: frequency.statementFrequencyCode,
    frequencyName: frequency.statementFrequencyName,
  };
}
