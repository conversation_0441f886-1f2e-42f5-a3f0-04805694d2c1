import { type PrismaClient } from "@prisma/client";

type CustomerCustomerType = {
  customerId: number;
  customerTypeId: number;
};

const getCustomerIdByCustomerCustomerType = async (
  customerCustomerTypeId: number,
  prisma: PrismaClient
): Promise<CustomerCustomerType> => {
  return (await prisma.customerCustomerType.findFirst({
    where: {
      customerCustomerTypeId,
    },
    select: {
      customerId: true,
      customerTypeId: true,
    },
  })) as CustomerCustomerType;
};

export { getCustomerIdByCustomerCustomerType };
