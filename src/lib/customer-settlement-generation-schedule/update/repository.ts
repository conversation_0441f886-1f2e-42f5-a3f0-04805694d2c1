import { type CreateScheduleData } from "@lib/customer-settlement-generation-schedule/update/type";

import type { PrismaClient } from "@prisma/client";

export const createCustomerSettlementGenerationSchedule = async (
  prisma: PrismaClient,
  {
    serviceNumber,
    fromDate,
    toDate,
    customerCustomerTypeId,
    generationType = "INITIAL",
    generationStatus = "PENDING",
  }: CreateScheduleData
) => {
  const currentDate = new Date();

  return prisma.customerSettlementGenerationSchedule.create({
    data: {
      serviceNumber,
      fromDate: new Date(fromDate),
      toDate: new Date(toDate),
      customerCustomerTypeId,
      generationType,
      generationStatus,
      generationDate: currentDate,
      startDate: currentDate,
    },
  });
};
