import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Update a user",
  description: "Updates user roles by adding or removing role IDs.",
  tags: [tags.users],
  body: {
    type: "object",
    required: ["userId"],
    properties: {
      userId: { type: "integer", minimum: 1 },
      addRoleIds: {
        type: "array",
        items: { type: "integer" },
        default: [],
      },
      removeRoleIds: {
        type: "array",
        items: { type: "integer" },
        default: [],
      },
    },
    additionalProperties: false,
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful response",
      type: "object",
      properties: {
        message: { type: "string", example: "User was updated successfully" },
      },
      required: ["message"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Internal server error",
      type: "object",
      properties: {
        error: {
          type: "object",
          properties: {
            message: { type: "string", example: "Error message" },
          },
          required: ["message"],
        },
      },
      required: ["error"],
    },
  },
};
