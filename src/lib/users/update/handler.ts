import { updateUser } from "./functions";

import type { FastifyReply, FastifyRequest } from "fastify";

type RequestBody = {
  userId: number;
  addRoleIds?: number[];
  removeRoleIds?: number[];
};

export const handler = async (
  request: FastifyRequest<{ Body: RequestBody }>,
  reply: FastifyReply
) => {
  const { userId, addRoleIds, removeRoleIds } = request.body;

  try {
    await updateUser(request.server.prisma, userId, {
      addRoleIds: addRoleIds ?? [],
      removeRoleIds: removeRoleIds ?? [],
    });

    return await reply.send({ message: "User was updated successfully" });
  } catch (error) {
    request.log.error(error);
    await reply.code(500).send({
      error: { message: (error as Error).message },
    });
  }
};
