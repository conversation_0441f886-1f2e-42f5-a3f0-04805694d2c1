import { type Prisma, PrismaClient } from "@prisma/client";

const updateUserInDatabase = async (
  prisma: PrismaClient | Prisma.TransactionClient,
  userId: number,
  properties: {
    addRoleIds: number[];
    removeRoleIds: number[];
  }
): Promise<void> => {
  const { addRoleIds, removeRoleIds } = properties;

  await (prisma instanceof PrismaClient
    ? prisma.$transaction(async (tx) =>
        _updateUserInDatabase(tx, userId, addRoleIds, removeRoleIds)
      )
    : _updateUserInDatabase(prisma, userId, addRoleIds, removeRoleIds));
};

const _updateUserInDatabase = async (
  prisma: Prisma.TransactionClient,
  userId: number,
  addRoleIds: number[] = [],
  removeRoleIds: number[] = []
) => {
  if (removeRoleIds.length > 0) {
    await prisma.userRole.deleteMany({
      where: {
        userId,
        roleId: { in: removeRoleIds },
      },
    });
  }

  if (addRoleIds.length > 0) {
    await prisma.userRole.createMany({
      data: addRoleIds.map((roleId) => ({ userId, roleId })),
      skipDuplicates: true,
    });
  }
};

export { updateUserInDatabase };
