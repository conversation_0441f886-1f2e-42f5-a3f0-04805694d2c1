import { type PrismaClient, type Prisma } from "@prisma/client";

import { type UsersFilter } from "./types";
import { type UserProfile, type Rule } from "../../../types/user-profile";

const getUsersFromDatabase = async (
  prisma: PrismaClient,
  offset = 0,
  limit = 20,
  filter?: UsersFilter
): Promise<{ users: UserProfile[]; totalCount: number }> => {
  const [users, totalCount] = await prisma.$transaction(async (tx) => {
    const users = await tx.user.findMany({
      where: buildWhereClause(filter),
      select: {
        userId: true,
        userName: true,
        userEmail: true,
        userRole: {
          select: {
            role: {
              select: {
                roleId: true,
                name: true,
                isEditable: true,
                rule: true,
              },
            },
          },
        },
      },
      skip: offset,
      take: limit,
      orderBy: [{ userName: "asc" }],
    });

    const count = await tx.user.count({
      where: buildWhereClause(filter),
    });

    return [users, count];
  });

  const userProfiles: UserProfile[] = users.map((user) => ({
    id: user.userId,
    fullName: user.userName,
    email: user.userEmail,
    roles: user.userRole.map((userRole) => ({
      id: userRole.role.roleId,
      name: userRole.role.name,
      isEditable: userRole.role.isEditable,
      rule: userRole.role.rule as Rule,
    })),
  }));

  return {
    users: userProfiles,
    totalCount,
  };
};

const buildWhereClause = (filter?: UsersFilter): Prisma.userWhereInput => {
  const whereClause: Prisma.userWhereInput = { deletedAt: null };

  if (filter) {
    if (filter.name) {
      whereClause.userName = { contains: filter.name };
    }

    if (filter.email) {
      whereClause.userEmail = { contains: filter.email };
    }
  }

  return whereClause;
};

export { getUsersFromDatabase };
