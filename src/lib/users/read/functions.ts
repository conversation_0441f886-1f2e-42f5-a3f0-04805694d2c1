import { type PrismaClient } from "@prisma/client";

import { getUsersFromDatabase } from "./repository";
import { type UsersFilter } from "./types";
import { type UserProfile } from "../../../types/user-profile";

const getUsers = async (
  prisma: PrismaClient,
  offset: number,
  limit: number,
  filter?: UsersFilter
): Promise<{ users: UserProfile[]; totalCount: number }> => {
  return getUsersFromDatabase(prisma, offset, limit, filter);
};

export { getUsers };
