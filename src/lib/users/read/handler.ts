import { getUsers } from "./functions";
import { type UsersFilter } from "./types";

import type { FastifyReply, FastifyRequest } from "fastify";

type Query = {
  offset: number;
  limit: number;
  name?: string;
  email?: string;
};

export const handler = async (
  request: FastifyRequest<{ Querystring: Query }>,
  reply: FastifyReply
) => {
  const { offset, limit, name, email } = request.query;

  const filter = {
    ...(name ? { name } : {}),
    ...(email ? { email } : {}),
  } satisfies UsersFilter;

  try {
    const result = await getUsers(request.server.prisma, offset, limit, filter);

    return await reply.send(result);
  } catch (error) {
    request.log.error(error);
    await reply.code(500).send({
      error: { message: (error as Error).message },
    });
  }
};
