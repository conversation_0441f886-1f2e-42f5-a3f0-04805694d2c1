import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Retrieve a list of users with filtering and pagination",
  description: `Fetches a paginated list of users based on provided filters (name, email) and pagination
   parameters (offset, limit). Returns user details including roles and permissions.`,
  tags: [tags.users],
  querystring: {
    type: "object",
    required: ["offset", "limit"],
    properties: {
      offset: { type: "integer", minimum: 0, description: "Pagination offset" },
      limit: {
        type: "integer",
        minimum: 1,
        description: "Number of records to retrieve",
      },
      name: { type: "string", description: "Filter by user name" },
      email: {
        type: "string",
        format: "email",
        description: "Filter by user email",
      },
    },
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful response with a list of users and total count",
      type: "object",
      properties: {
        users: {
          type: "array",
          items: {
            type: "object",
            properties: {
              id: { type: "integer", description: "Unique user ID" },
              fullName: {
                type: "string",
                description: "Full name of the user",
              },
              email: {
                type: "string",
                format: "email",
                description: "User's email address",
              },
              roles: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    id: { type: "integer", description: "Role ID" },
                    name: { type: "string", description: "Role name" },
                    isEditable: {
                      type: "boolean",
                      description: "If the role can be edited",
                    },
                    rule: {
                      type: "object",
                      description: "Permissions associated with the role",
                    },
                  },
                },
                description: "Array of user roles",
              },
            },
          },
        },
        totalCount: {
          type: "integer",
          description: "Total count of users matching the filters",
        },
      },
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Internal server error",
      type: "object",
      properties: {
        error: {
          type: "object",
          properties: {
            message: { type: "string", description: "Error message" },
          },
        },
      },
    },
  },
};
