import { getBySettlementId } from "./service";

import type { FastifyRequest, FastifyReply } from "fastify";

export const handler = async (request: FastifyRequest, reply: FastifyReply) => {
  const { settlementId } = request.params as { settlementId: number };

  // Fetch the emailId and deletedAt associated with the settlementId and send it back to UI.
  try {
    const email = await getBySettlementId(request.server.prisma, settlementId);

    // Return the emailId and deletedAt in the resoponse.
    return await reply.send({
      emailId: email?.emailId,
      deletedAt: email?.deletedAt,
    });
  } catch (error) {
    request.log.error(error);

    return reply.code(500).send({ message: "Internal server error" });
  }
};
