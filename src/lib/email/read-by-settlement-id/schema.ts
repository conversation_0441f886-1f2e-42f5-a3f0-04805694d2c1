import type { FastifySchema } from "fastify";

// The schema for the route that fetches the email ID by settlement ID.
export const schema: FastifySchema = {
  summary: "Fetch Email id by Settlement ID",
  description: "Fetches the email ID associated with a specific settlement ID.",
  tags: ["email"],
  params: {
    type: "object",
    properties: {
      settlementId: { type: "number" },
    },
    required: ["settlementId"],
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful fetch of email ID",
      type: "object",
      properties: {
        emailId: {
          type: "number",
        },
        deletedAt: {
          type: "string",
        },
      },
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Internal server error",
      type: "object",
      required: ["message"],
      properties: {
        message: {
          type: "string",
          description:
            "Error in fetching email Id and redirecting to email page.",
        },
      },
    },
  },
};
