import { type PrismaClient } from "@prisma/client";

// Get the emailId and deletedAt associated with the settlementId through the wire table.
export const getEmailRecordBySettlementId = async (
  prisma: PrismaClient,
  settlementId: number
) => {
  return prisma.email.findFirst({
    where: {
      wire: {
        settlementId,
        isCancelled: false,
      },
    },
    select: { emailId: true, deletedAt: true },
  });
};
