import type { PrismaClient, Prisma } from "@prisma/client";

async function updateEmails(
  prisma: PrismaClient,
  emailIds: number[],
  comment?: string,
  isEndBalance?: boolean
) {
  const dataToUpdate: Prisma.emailUpdateInput = {};

  if (typeof comment === "string") {
    dataToUpdate.comment = comment;
  }

  if (typeof isEndBalance === "boolean") {
    dataToUpdate.isEndBalance = isEndBalance;
  }

  if (Object.keys(dataToUpdate).length === 0) {
    return;
  }

  // Only update emails that are not sent yet
  await prisma.email.updateMany({
    where: {
      emailId: { in: emailIds },
      sentById: null,
      sentAt: null,
    },
    data: dataToUpdate,
  });
}

export { updateEmails };
