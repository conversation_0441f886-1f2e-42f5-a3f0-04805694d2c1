import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

const errorResponse = {
  description: "Error response",
  type: "object",
  properties: {
    success: { type: "boolean", example: false },
    message: { type: "string" },
    error: {
      type: "object",
      properties: {
        message: { type: "string" },
      },
      required: ["message"],
    },
  },
  required: ["message", "error"],
};

export const schema: FastifySchema = {
  summary: "Fetch Email by ID",
  description: "Fetches an email by its ID.",
  tags: [tags.email],
  body: {
    type: "object",
    properties: {
      emailIds: { type: "array", items: { type: "number" } },
      comment: { type: "string" },
      isEndBalance: { type: "boolean" },
    },
    required: ["emailIds"],
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful response",
      type: "object",
      properties: {
        success: { type: "boolean", example: true },
        message: { type: "string" },
      },
      required: ["message"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: errorResponse,
  },
};
