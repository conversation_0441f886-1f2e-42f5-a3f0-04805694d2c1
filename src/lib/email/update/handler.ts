import { updateBatchEmails } from "@lib/email/update/functions";
import { type EmailRequestBody } from "@lib/email/update/type";

import type { FastifyReply, FastifyRequest } from "fastify";

export const handler = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const { prisma } = request.server;
    const { emailIds, comment, isEndBalance } =
      request.body as EmailRequestBody;

    await updateBatchEmails(prisma, emailIds, comment, isEndBalance);

    return await reply.send({
      success: true,
      message: "Email updated successfully.",
    });
  } catch (error) {
    request.log.error(error);
    await reply.code(500).send({
      success: false,
      message: "Error updating email.",
      error: { message: (error as Error).message },
    });
  }
};
