import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Fetch Emails",
  description: "Fetches emails with related customer and wire information.",
  tags: [tags.email],
  body: {
    type: "object",
    properties: {
      offset: {
        type: "integer",
        minimum: 0,
        default: 0,
      },
      limit: {
        type: "integer",
        minimum: 1,
        maximum: 200,
        default: 20,
      },
      status: { type: "string" },
      subject: { type: "string" },
      toEmail: { type: "string" },
      startSendDate: { type: "string" },
      endSendDate: { type: "string" },
      entity: { type: "string" },
      frequency: { type: "string" },
      merchantType: { type: "string" },
      sortColumnName: { type: "string" },
      sortDirection: { type: "string" },
    },
    required: ["offset", "limit"],
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful response",
      type: "object",
      properties: {
        data: {
          type: "object",
          properties: {
            data: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  clientName: { type: "string" },
                  serviceNumber: { type: "string" },
                  to: { type: "array", items: { type: "string" } },
                  cc: { type: "array", items: { type: "string" } },
                  subject: { type: "string" },
                  wireAmount: { type: "number" },
                  sendDate: { type: "string", format: "date" },
                  endBalance: { type: "number" },
                  attachments: { type: "array", items: { type: "string" } },
                  comment: { type: "string" },
                  entityName: { type: "string" },
                },
                required: [
                  "id",
                  "clientName",
                  "serviceNumber",
                  "to",
                  "cc",
                  "subject",
                  "wireAmount",
                  "sendDate",
                ],
              },
            },
            totalCount: { type: "number" },
          },
          required: ["data", "totalCount"],
        },
      },
      required: ["data"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Error response",
      type: "object",
      properties: {
        success: { type: "boolean", example: false },
        message: { type: "string" },
        error: {
          type: "object",
          properties: {
            message: { type: "string" },
          },
          required: ["message"],
        },
      },
      required: ["message", "error"],
    },
  },
};
