import { type Prisma, type PrismaClient } from "@prisma/client";

import { getOrder } from "./service";
import { type EmailFilterParameters } from "../../../types/email/email";

type EmailFilters = {
  offset: number;
  limit: number;
} & EmailFilterParameters;

export const saveEmailPreference = async (
  userId: number,
  emailFilters: EmailFilters,
  prisma: PrismaClient
) => {
  const { offset, limit, ...filters } = emailFilters;

  await prisma.userPreference.upsert({
    where: { userId },
    update: {
      emailPreference: {
        filters,
        pageNumber: offset / limit + 1,
        recordsPerPage: limit,
      },
    },
    create: {
      userId,
      emailPreference: {
        filters,
        pageNumber: offset / limit + 1,
        recordsPerPage: limit,
      },
    },
  });
};

export const fetchFilteredEmails = async ({
  prisma,
  where,
  offset,
  limit,
  sortColumnName,
  sortDirection,
}: {
  prisma: PrismaClient;
  where: Prisma.emailWhereInput;
  offset: number;
  limit: number;
  sortColumnName: string | undefined;
  sortDirection: string | undefined;
}) => {
  const orderBy = getOrder(sortColumnName, sortDirection);
  const [emails, totalCount] = await prisma.$transaction([
    prisma.email.findMany({
      orderBy,
      where,
      skip: offset,
      take: limit,
      select: {
        emailId: true,
        toMeta: true,
        ccMeta: true,
        subject: true,
        sentAt: true,
        scheduledSendDate: true,
        transferInId: true,
        wireId: true,
        wire: {
          select: {
            finalWireAmount: true,
            finalWireDate: true,
            settlementId: true,
            customer: {
              select: {
                customerName: true,
                serviceNumber: true,
                emailConfiguration: {
                  select: { toMeta: true, ccMeta: true },
                },
                entity: { select: { entityName: true } },
              },
            },
          },
        },
        customerWireInOuts: {
          select: {
            transactionAmount: true,
            transactionDate: true,
            customer: {
              select: {
                customerName: true,
                serviceNumber: true,
                emailConfiguration: {
                  select: { toMeta: true, ccMeta: true },
                },
                entity: { select: { entityName: true } },
              },
            },
          },
        },
      },
    }),
    prisma.email.count({ where }),
  ]);

  return { emails, totalCount };
};
