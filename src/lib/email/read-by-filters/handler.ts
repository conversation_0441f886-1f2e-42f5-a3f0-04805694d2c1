import { fetchFilteredEmails, saveEmailPreference } from "./repository";
import { formatEmailData, getEmailFilterConditions } from "./service";
import { getAuthToken } from "../../../services/blusky/authentication";
import { getEndBalance } from "../../../services/blusky/end-balance";
import {
  type EmailFilterParameters,
  type EmailRecord,
} from "../../../types/email/email";

import type { FastifyReply, FastifyRequest } from "fastify";

type EmailRequest = {
  offset: number;
  limit: number;
} & EmailFilterParameters;

export const handler = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const { prisma } = request.server;
    const filters = request.body as EmailRequest;
    const {
      offset = 0,
      limit = 20,
      status,
      subject,
      startSendDate,
      endSendDate,
      toEmail,
      entity,
      frequency,
      merchantType,
      sortColumnName,
      sortDirection,
    } = filters;

    await saveEmailPreference(request.userProfile.id, filters, prisma);

    const where = getEmailFilterConditions({
      subject: subject?.trim(),
      toEmail: toEmail?.trim().toLowerCase(),
      status,
      startSendDate,
      endSendDate,
      entity,
      frequency,
      merchantType,
    } as EmailFilterParameters);

    const { emails, totalCount } = await fetchFilteredEmails({
      prisma,
      where,
      offset,
      limit,
      sortColumnName,
      sortDirection,
    });

    const serviceNumbers = emails.map(
      (email) =>
        email.wire?.customer?.serviceNumber ??
        email.customerWireInOuts?.customer?.serviceNumber
    ) as string[];

    let endBalanceMap = {};

    if (serviceNumbers.length > 1) {
      const bluskyToken = await getAuthToken();
      request.log.info(
        `Emails: Getting end balance for service numbers: ${serviceNumbers.join(", ")}`
      );
      endBalanceMap = await getEndBalance(serviceNumbers, bluskyToken);
    }

    const data = formatEmailData(
      emails as unknown as EmailRecord[],
      endBalanceMap
    );

    return await reply.send({ data: { data, totalCount } });
  } catch (error) {
    request.log.error(error);
    await reply.code(500).send({
      success: false,
      message: "Error fetching emails, please try again.",
      error: { message: (error as Error).message },
    });
  }
};
