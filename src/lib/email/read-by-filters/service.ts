/* eslint-disable @typescript-eslint/naming-convention */

import { getEmailSubject } from "@lib/email/common/functions/get-email-subject";
import { Prisma } from "@prisma/client";
import { isValid, parseISO } from "date-fns";

import {
  type EmailFilterParameters,
  type EmailRecord,
  type EndBalanceMap,
  type FormattedEmailData,
} from "../../../types/email/email";
import { safeParseJson } from "../../../utils/safe-parse-json";

export const getStatusWhereCondition = (
  status: string
): Prisma.emailWhereInput => {
  switch (status) {
    case "Email: Pending": {
      return {
        sentAt: null,
      };
    }

    case "Email: Sent": {
      return {
        sentAt: { not: null },
      };
    }

    default: {
      return {};
    }
  }
};

export const getEmailFilterConditions = ({
  status,
  subject,
  startSendDate,
  endSendDate,
  toEmail,
  entity,
  frequency,
  merchantType,
}: EmailFilterParameters): Prisma.emailWhereInput => {
  const where: Prisma.emailWhereInput = {
    deletedAt: null,
    ...getStatusWhereCondition(status),
  };

  if (subject) {
    where.subject = { contains: subject };
  }

  if (startSendDate ?? endSendDate) {
    where.scheduledSendDate = {};

    if (startSendDate) {
      where.scheduledSendDate.gte = getDateAtMidnightUtc(startSendDate);
    }

    if (endSendDate) {
      where.scheduledSendDate.lte = getDateAtEndOfDayUtc(endSendDate);
    }
  }

  const orConditions: Prisma.emailWhereInput[] = [];

  if (toEmail) {
    toEmail = toEmail.toLowerCase();
    orConditions.push({
      OR: [
        {
          toMeta: {
            string_contains: toEmail,
          },
        },
        {
          wire: {
            customer: {
              emailConfiguration: {
                toMeta: {
                  string_contains: toEmail,
                },
              },
            },
          },
        },
        {
          customerWireInOuts: {
            customer: {
              emailConfiguration: {
                toMeta: {
                  string_contains: toEmail,
                },
              },
            },
          },
        },
      ],
    });
  }

  if (entity) {
    orConditions.push({
      OR: [
        {
          wire: {
            customer: {
              entityId: Number(entity),
            },
          },
        },
        {
          customerWireInOuts: {
            customer: {
              entityId: Number(entity),
            },
          },
        },
      ],
    });
  }

  if (frequency) {
    orConditions.push({
      OR: [
        {
          wire: {
            statementFrequency: { statementFrequencyId: Number(frequency) },
          },
        },
        {
          customerWireInOuts: {
            customer: {
              customerCustomerType: {
                some: {
                  statementFrequencyId: Number(frequency),
                },
              },
            },
          },
        },
      ],
    });
  }

  if (merchantType) {
    orConditions.push({
      OR: [
        {
          wire: {
            customer: {
              customerCustomerType: {
                some: {
                  customerType: { customerTypeId: Number(merchantType) },
                },
              },
            },
          },
        },
        {
          customerWireInOuts: {
            customer: {
              customerCustomerType: {
                some: {
                  customerType: { customerTypeId: Number(merchantType) },
                },
              },
            },
          },
        },
      ],
    });
  }

  if (orConditions.length > 0) {
    where.AND = orConditions;
  }

  return where;
};

export const formatEmailData = (
  emails: EmailRecord[],
  endBalanceMap: EndBalanceMap
): Array<FormattedEmailData | undefined> => {
  return emails
    .map((email) => {
      const record = email.wire ?? email.customerWireInOuts;
      const customer = record?.customer;

      if (!customer) {
        return;
      }

      const subjectMessage = getEmailSubject({
        serviceNumber: customer.serviceNumber ?? "",
        customerName: customer.customerName ?? "",
        transferInId: email.transferInId,
        wireId: email.wireId,
        settlementId: email.wire?.settlementId,
      });

      const endBalance = endBalanceMap[customer.serviceNumber]?.endBalance ?? 0;

      const toEmails = parseEmailMeta(
        email.toMeta,
        customer.emailConfiguration?.toMeta
      );
      const ccEmails = parseEmailMeta(
        email.ccMeta,
        customer.emailConfiguration?.ccMeta
      );

      const sendDateISO =
        email.scheduledSendDate ??
        email.wire?.finalWireDate ??
        email.customerWireInOuts?.transactionDate;
      const sendDate = sendDateISO
        ? new Date(sendDateISO).toISOString().split("T")[0]
        : undefined;

      return {
        id: email.emailId,
        clientName: customer.customerName ?? "",
        serviceNumber: customer.serviceNumber ?? "",
        to: toEmails,
        cc: ccEmails,
        subject: email.subject ?? subjectMessage.message,
        wireAmount:
          email.wire?.finalWireAmount ??
          email.customerWireInOuts?.transactionAmount ??
          0,
        sendDate,
        endBalance,
        entityName: customer.entity?.entityName ?? "",
      };
    })
    .filter(Boolean);
};

const parseEmailMeta = (meta: unknown, fallbackMeta?: unknown) => {
  if (typeof meta === "string") {
    return safeParseJson(meta);
  }

  if (typeof fallbackMeta === "string") {
    return safeParseJson(fallbackMeta);
  }

  return [];
};

export const getOrder = (sortColumnName?: string, sortDirection?: string) => {
  const sortType =
    sortDirection === "asc" ? Prisma.SortOrder.asc : Prisma.SortOrder.desc;

  if (sortDirection === "none" || !sortColumnName) {
    return { updatedAt: sortType };
  }

  if (sortColumnName === "wireAmount") {
    return { wire: { finalWireAmount: sortType } };
  }

  if (sortColumnName === "client") {
    return { wire: { customer: { customerName: sortType } } };
  }

  return { [sortColumnName]: sortType };
};

const getDateAtMidnightUtc = (dateString: string): Date => {
  const date = parseDateStringToUtcDate(dateString);

  return new Date(
    Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate())
  );
};

const getDateAtEndOfDayUtc = (dateString: string): Date => {
  const date = parseDateStringToUtcDate(dateString);

  return new Date(
    Date.UTC(
      date.getUTCFullYear(),
      date.getUTCMonth(),
      date.getUTCDate(),
      23,
      59,
      59,
      999
    )
  );
};

const parseDateStringToUtcDate = (dateString: string): Date => {
  const date = parseISO(dateString);

  if (!isValid(date)) {
    throw new TypeError(`Invalid date string: ${dateString}`);
  }

  return date;
};
