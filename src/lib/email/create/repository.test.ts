import { type PrismaClient } from "@prisma/client";
import { describe, it, expect, beforeEach, vi, type Mock } from "vitest";

import { createEmailWithWire } from "./repository";
import { type CreateEmailWithWireProperties } from "./type";

const mockPrismaClient = {
  email: {
    create: vi.fn(),
    findFirst: vi.fn(),
    findMany: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
  },
} as unknown as PrismaClient;

describe("Email Create Repository", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("createEmailWithWire", () => {
    it("should create an email with wire successfully", async () => {
      const mockEmailInput: CreateEmailWithWireProperties = {
        wireId: 123,
        subject: "Test Email Subject",
        scheduledSendDate: new Date("2023-12-01T10:00:00Z"),
        attachments: "attachment1.pdf,attachment2.jpg",
      };

      const mockCreatedEmail = {
        emailId: "email-id-123",
        wireId: 123,
        subject: "Test Email Subject",
        scheduledSendDate: new Date("2023-12-01T10:00:00Z"),
        attachments: "attachment1.pdf,attachment2.jpg",
        createdAt: new Date(),
        updatedAt: new Date(),
        sentAt: null,
      };

      (mockPrismaClient.email.create as Mock).mockResolvedValue(
        mockCreatedEmail
      );

      const result = await createEmailWithWire(
        mockPrismaClient,
        mockEmailInput
      );

      expect(mockPrismaClient.email.create).toHaveBeenCalledWith({
        data: {
          wireId: 123,
          subject: "Test Email Subject",
          scheduledSendDate: new Date("2023-12-01T10:00:00Z"),
          attachments: "attachment1.pdf,attachment2.jpg",
        },
      });
      expect(result).toEqual(mockCreatedEmail);
    });

    it("should create an email with wire with minimal fields", async () => {
      const mockEmailInput: CreateEmailWithWireProperties = {
        wireId: 456,
        subject: "Simple Subject",
        scheduledSendDate: new Date("2023-12-02T14:30:00Z"),
        attachments: "",
      };

      const mockCreatedEmail = {
        emailId: "email-id-456",
        wireId: 456,
        subject: "Simple Subject",
        scheduledSendDate: new Date("2023-12-02T14:30:00Z"),
        attachments: "",
        createdAt: new Date(),
        updatedAt: new Date(),
        sentAt: null,
      };

      (mockPrismaClient.email.create as Mock).mockResolvedValue(
        mockCreatedEmail
      );

      const result = await createEmailWithWire(
        mockPrismaClient,
        mockEmailInput
      );

      expect(mockPrismaClient.email.create).toHaveBeenCalledWith({
        data: {
          wireId: 456,
          subject: "Simple Subject",
          scheduledSendDate: new Date("2023-12-02T14:30:00Z"),
          attachments: "",
        },
      });
      expect(result).toEqual(mockCreatedEmail);
    });

    it("should create an email with multiple attachments", async () => {
      const mockEmailInput: CreateEmailWithWireProperties = {
        wireId: 789,
        subject: "Email with Multiple Attachments",
        scheduledSendDate: new Date("2023-12-03T09:15:00Z"),
        attachments:
          '["/test/document1.xlsx","/test/document2.xlsx","/test/document2.xlsx"]',
      };

      const mockCreatedEmail = {
        emailId: "email-id-789",
        wireId: 789,
        subject: "Email with Multiple Attachments",
        scheduledSendDate: new Date("2023-12-03T09:15:00Z"),
        attachments:
          '["/test/document1.xlsx","/test/document2.xlsx","/test/document2.xlsx"]',
        createdAt: new Date(),
        updatedAt: new Date(),
        sentAt: null,
      };

      (mockPrismaClient.email.create as Mock).mockResolvedValue(
        mockCreatedEmail
      );

      const result = await createEmailWithWire(
        mockPrismaClient,
        mockEmailInput
      );

      expect(mockPrismaClient.email.create).toHaveBeenCalledWith({
        data: {
          wireId: 789,
          subject: "Email with Multiple Attachments",
          scheduledSendDate: new Date("2023-12-03T09:15:00Z"),
          attachments:
            '["/test/document1.xlsx","/test/document2.xlsx","/test/document2.xlsx"]',
        },
      });
      expect(result).toEqual(mockCreatedEmail);
    });

    it("should handle database errors during creation", async () => {
      const mockEmailInput: CreateEmailWithWireProperties = {
        wireId: 999,
        subject: "Test Subject",
        scheduledSendDate: new Date("2023-12-04T16:00:00Z"),
        attachments: "",
      };

      const mockError = new Error("Database connection failed");
      (mockPrismaClient.email.create as Mock).mockRejectedValue(mockError);

      await expect(
        createEmailWithWire(mockPrismaClient, mockEmailInput)
      ).rejects.toThrow("Database connection failed");

      expect(mockPrismaClient.email.create).toHaveBeenCalledWith({
        data: {
          wireId: 999,
          subject: "Test Subject",
          scheduledSendDate: new Date("2023-12-04T16:00:00Z"),
          attachments: "",
        },
      });
    });

    it("should handle validation errors", async () => {
      const mockEmailInput: CreateEmailWithWireProperties = {
        wireId: -1,
        subject: "Test Subject",
        scheduledSendDate: new Date("2023-12-05T12:00:00Z"),
        attachments: "",
      };

      const mockError = new Error("Invalid wire ID");
      (mockPrismaClient.email.create as Mock).mockRejectedValue(mockError);

      await expect(
        createEmailWithWire(mockPrismaClient, mockEmailInput)
      ).rejects.toThrow("Invalid wire ID");
    });

    it("should handle empty subject", async () => {
      const mockEmailInput: CreateEmailWithWireProperties = {
        wireId: 100,
        subject: "",
        scheduledSendDate: new Date("2023-12-06T08:00:00Z"),
        attachments: "",
      };

      const mockCreatedEmail = {
        emailId: "email-id-100",
        wireId: 100,
        subject: "",
        scheduledSendDate: new Date("2023-12-06T08:00:00Z"),
        attachments: "",
        createdAt: new Date(),
        updatedAt: new Date(),
        sentAt: null,
      };

      (mockPrismaClient.email.create as Mock).mockResolvedValue(
        mockCreatedEmail
      );

      const result = await createEmailWithWire(
        mockPrismaClient,
        mockEmailInput
      );

      expect(mockPrismaClient.email.create).toHaveBeenCalledWith({
        data: {
          wireId: 100,
          subject: "",
          scheduledSendDate: new Date("2023-12-06T08:00:00Z"),
          attachments: "",
        },
      });
      expect(result).toEqual(mockCreatedEmail);
    });

    it("should handle future scheduled send date", async () => {
      const futureDate = new Date();
      futureDate.setMonth(futureDate.getMonth() + 1);

      const mockEmailInput: CreateEmailWithWireProperties = {
        wireId: 200,
        subject: "Future Scheduled Email",
        scheduledSendDate: futureDate,
        attachments: '["/test/future_document.pdf"]',
      };

      const mockCreatedEmail = {
        emailId: "email-id-200",
        wireId: 200,
        subject: "Future Scheduled Email",
        scheduledSendDate: futureDate,
        attachments: '["/test/future_document.pdf"]',
        createdAt: new Date(),
        updatedAt: new Date(),
        sentAt: null,
      };

      (mockPrismaClient.email.create as Mock).mockResolvedValue(
        mockCreatedEmail
      );

      const result = await createEmailWithWire(
        mockPrismaClient,
        mockEmailInput
      );

      expect(mockPrismaClient.email.create).toHaveBeenCalledWith({
        data: {
          wireId: 200,
          subject: "Future Scheduled Email",
          scheduledSendDate: futureDate,
          attachments: '["/test/future_document.pdf"]',
        },
      });
      expect(result).toEqual(mockCreatedEmail);
    });
  });
});
