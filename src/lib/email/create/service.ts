import { createEmailWithWire } from "@lib/email/create/repository";
import { getAttachments } from "@lib/filesystem/attachments/service";
import { type Wire } from "@lib/wire/create/types";

import type { ApprovalCustomerSettlement } from "@lib/customer-settlements/read/type";
import type { PrismaClient } from "@prisma/client";

export const createEmail = async (
  prisma: PrismaClient,
  settlement: ApprovalCustomerSettlement,
  wire: Wire
) => {
  const { serviceNumber, customerName } = settlement.customer;

  const attachments = await getAttachments(prisma, settlement);

  return createEmailWithWire(prisma, {
    wireId: wire.wireId,
    subject: `${serviceNumber} - ${customerName} - Settlement Notification`,
    scheduledSendDate: wire.finalWireDate,
    attachments: JSON.stringify(attachments),
  });
};
