import { type CreateEmailWithWireProperties } from "@lib/email/create/type";

import type { PrismaClient } from "@prisma/client";

export const createEmailWithWire = async (
  prisma: PrismaClient,
  {
    wireId,
    subject,
    scheduledSendDate,
    attachments,
  }: CreateEmailWithWireProperties
) => {
  return prisma.email.create({
    data: {
      wireId,
      subject,
      scheduledSendDate,
      attachments,
    },
  });
};
