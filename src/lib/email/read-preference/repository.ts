import type { PrismaClient } from "@prisma/client";

const defaultPreference = {
  filters: {
    status: "Email: Pending",
    subject: "",
    toEmail: "",
    entity: "",
    frequency: "",
    merchantType: "",
    sortColumnName: "",
    sortDirection: "none",
  },
  pageNumber: 1,
  recordsPerPage: 20,
};

export const getEmailPreference = async (
  userId: number,
  prisma: PrismaClient
) => {
  const userPreference = await prisma.userPreference.findUnique({
    where: { userId },
    select: {
      emailPreference: true,
    },
  });

  if (!userPreference) {
    return defaultPreference;
  }

  return userPreference.emailPreference;
};
