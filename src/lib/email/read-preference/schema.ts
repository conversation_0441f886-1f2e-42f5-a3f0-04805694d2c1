import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Fetch Email Preference of User",
  description:
    "Fetches the email preference of the user, or default preferences if not set.",
  tags: [tags.email],
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful response",
      type: "object",
      properties: {
        data: {
          type: "object",
          properties: {
            filters: {
              type: "object",
              properties: {
                status: {
                  type: "string",
                  enum: ["Email: Pending", "Email: Sent"],
                  example: "Email: Pending",
                },
                subject: { type: "string", example: "Test Subject" },
                startSendDate: { type: "string", format: "date-time" },
                endSendDate: { type: "string", format: "date-time" },
                toEmail: { type: "string", example: "<EMAIL>" },
                entity: {
                  type: "string",
                  example: "2",
                },
                frequency: {
                  type: "string",
                  example: "2",
                },
                merchantType: {
                  type: "string",
                  example: "1",
                },
                sortColumnName: {
                  type: "string",
                  example: "subject",
                },
                sortDirection: {
                  type: "string",
                  enum: ["asc", "desc", "none"],
                  example: "asc",
                },
              },
              required: [
                "status",
                "subject",
                "toEmail",
                "entity",
                "frequency",
                "merchantType",
                "sortColumnName",
                "sortDirection",
              ],
            },
            pageNumber: {
              type: "number",
              example: 1,
            },
            recordsPerPage: {
              type: "number",
              example: 20,
            },
          },
          required: ["filters", "pageNumber", "recordsPerPage"],
        },
      },
      required: ["data"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Error response",
      type: "object",
      properties: {
        message: { type: "string" },
      },
      required: ["message"],
    },
  },
};
