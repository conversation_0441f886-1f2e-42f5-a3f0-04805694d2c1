import { getEmailPreference } from "@lib/email/read-preference/repository";

import type { FastifyReply, FastifyRequest } from "fastify";

export const handler = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const { prisma } = request.server;

    const preference = await getEmailPreference(request.userProfile.id, prisma);

    return await reply.send({ data: preference });
  } catch (error) {
    request.log.error(error);
    await reply.code(500).send({
      message: (error as Error).message,
    });
  }
};
