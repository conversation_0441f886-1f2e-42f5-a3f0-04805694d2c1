export function getCopyrightNotice(entityName: string): string {
  const currentYear = new Date().getFullYear();
  let entityCorpName = "";

  switch (entityName) {
    case "Gigadat": {
      entityCorpName = "GigadatSolutions";
      break;
    }

    case "Wyzia": {
      entityCorpName = "Wyzia";
      break;
    }

    case "Bitfy": {
      entityCorpName = "Bitfy Payments";
      break;
    }

    default: {
      throw new Error("No correct entity provided");
    }
  }

  return `Ⓒ ${currentYear} ${entityCorpName} Inc. All rights reserved.`;
}
