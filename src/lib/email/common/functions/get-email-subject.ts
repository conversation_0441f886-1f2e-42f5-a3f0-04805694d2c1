type EmailSubjectArguments = {
  serviceNumber: string;
  customerName: string;
  transferInId?: string | unknown;
  wireId?: string | unknown;
  settlementId?: string | unknown;
};

export type EmailSubjectResponse = {
  message: string;
  notification: string;
};

export const getEmailSubject = ({
  serviceNumber = "",
  customerName = "",
  transferInId = null,
  wireId = null,
  settlementId = null,
}: EmailSubjectArguments): EmailSubjectResponse => {
  if ((!serviceNumber || !customerName) && !(transferInId && wireId)) {
    throw new Error(
      "Haven't provided one of the required arguments to generate the subject"
    );
  }

  let notification = "";

  if (transferInId) {
    notification = "Payment Received";
  } else if (wireId) {
    notification = settlementId ? "Settlement" : "Payment Send";
  }

  const message = `${serviceNumber} - ${customerName} - ${notification} Notification`;

  return {
    message,
    notification,
  };
};
