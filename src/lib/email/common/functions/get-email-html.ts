import { templateNames } from "@constants/email";
import Handlebars from "handlebars";

import { type TemplateData } from "../../../../types/email/template";

function buildTemplate(templatePart: string, data: TemplateData): string {
  const template = Handlebars.compile(templatePart);

  return template(data);
}

function getEmailHeader(data: TemplateData): string {
  const headerTemplate = `
    <header>
      <div style="display: flex; justify-content: center; padding: 30px 20px 20px 20px">
        <img src="{{entityLogo}}" width="160" alt="{{entityName}} Logo" />
      </div>

      <div style="background: {{entityNotificationStyle}}; color: white; padding: 40px 20px;">
        <div style="font-size: 16px; padding-bottom: 5px; font-weight: bold">Notification</div>
        <h3>{{subjectNotification}}</h3>
      </div>
    </header>
  `;

  return buildTemplate(headerTemplate, data);
}

function getWireTransferBody(data: TemplateData): string {
  const bodyTemplate = `
    <main>
      <div style="padding: 20px">
        <div style="padding: 10px">
          Hello {{clientName}},
        </div>

        <div style="padding: 10px">
          We have now received <b>\${{wireAmount}}</b> in our bank and have credited this to your account.
        </div>

        {{#if comment}}
          <div style="padding: 10px">{{comment}}</div>
        {{/if}}

        <div style="padding: 10px">
          Regards,<br/>
          {{entityName}} Finance
        </div>
      </div>
    </main>
  `;

  return buildTemplate(bodyTemplate, data);
}

function getManualWireBody(data: TemplateData): string {
  const bodyTemplate = `
    <main>
      <div style="padding: 20px">
        <div style="padding: 10px">
          Hello {{clientName}},
        </div>

        <div style="padding: 10px">
          As per your request, <b>\${{wireAmount}}</b> will be sent shortly.
        </div>

        {{#if comment}}
          <div style="padding: 10px">{{comment}}</div>
        {{/if}}

        <div style="padding: 10px">
          Regards,<br/>
          {{entityName}} Finance
        </div>
    </div>
    </main>
  `;

  return buildTemplate(bodyTemplate, data);
}

function getSettlementBody(data: TemplateData): string {
  const bodyTemplate = `
    <main>
      <div style="padding: 20px">
        <div style="padding: 10px">Hello {{clientName}},</div>

        <div style="padding: 10px">
          <div>
            {{#if isSftpEnabled}}
              Settlement statements are added to SFTP
            {{else}}
              Kindly review the enclosed settlement statements.
            {{/if}}
          </div>

          <div>
            {{#if fundsGoingOut}}
              Payment of <b>\${{wireAmount}}</b> will be sent shortly.
            {{else}}
              Your balance has been carried forward.
            {{/if}}
          </div>

          <div>
              {{#if isBluskyEnabled}}
                Please note that both the current and historical balances are accessible through the online portal.
              {{else}}
                {{#if showEndBalanceInTemplate}}
                  Your balance at the end of the period is <b>\${{endBalance}}</b>.
                {{/if}}
              {{/if}}
          </div>
        </div>

        {{#if comment}}
          <div style="padding: 10px">{{comment}}</div>
        {{/if}}

        <div style="padding: 10px">
          Regards,<br/>
          {{entityName}} Finance
        </div>
      </div>
    </main>
  `;

  return buildTemplate(bodyTemplate, data);
}

function getEmailFooter(data: TemplateData): string {
  const footerTemplate = `
    <hr style="margin: 10px; border: none; height: 2px; background-color: black;">

    <footer>
      <div>
        <div style="display: flex; justify-content: center;">
          <div style="padding: 30px; justify-content: center;">{{copyrightNotice}}</div>
        </div>
        <div style="display: flex; justify-content: center;">
          <img src="{{entityLogo}}" width="160" alt="{{entityName}} Logo" />
        </div>
      </div>
    </footer>
  `;

  return buildTemplate(footerTemplate, data);
}

export function getEmailHtml(templateName: string, data: TemplateData): string {
  let emailHtml = "";

  emailHtml += getEmailHeader(data);

  switch (templateName) {
    case templateNames.wireTransfer: {
      emailHtml += getWireTransferBody(data);
      break;
    }

    case templateNames.manualWire: {
      emailHtml += getManualWireBody(data);
      break;
    }

    case templateNames.settlementWire: {
      emailHtml += getSettlementBody(data);
      break;
    }

    default: {
      emailHtml = String(emailHtml);
    }
  }

  emailHtml += getEmailFooter(data);

  const sanitizedEmailHtml = emailHtml.replaceAll("\n", "");

  return `<html lang="en"><body>${sanitizedEmailHtml}</body></html>`;
}
