import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Fetch Email by ID",
  description: "Fetches an email by its ID.",
  tags: [tags.email],
  params: {
    type: "object",
    properties: {
      id: { type: "number" },
    },
    required: ["id"],
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful response",
      type: "object",
      properties: {
        data: {
          type: "object",
          properties: {
            email: {
              properties: {
                id: { type: "string" },
                to: { type: "array", items: { type: "string" } },
                cc: { type: "array", items: { type: "string" } },
                subject: { type: "string" },
                sendDate: { type: "string", format: "date" },
                comment: { type: "string" },
                sentById: { type: "number" },
                sentAt: { type: "string", format: "date" },
                isEndBalance: { type: "boolean" },
                attachments: {
                  type: "object",
                  properties: {
                    rootFolderPath: { type: "string" },
                    files: {
                      type: "array",
                      items: {
                        type: "object",
                        properties: {
                          filePath: { type: "string" },
                          isFileExist: { type: "boolean" },
                        },
                        required: ["filePath", "isFileExist"],
                      },
                    },
                  },
                },
              },
              required: ["id", "to", "cc", "subject", "sendDate", "comment"],
            },
            html: { type: "string" },
          },
          required: ["email", "html"],
        },
      },
      required: ["data"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Internal server error",
      type: "object",
      required: ["message"],
      properties: {
        message: {
          type: "string",
          description: "Error message explaining the failure",
        },
      },
    },
  },
};
