import fs from "node:fs";

import { resolveFilePath } from "../../../utils/file-system";
import { safeParseJson } from "../../../utils/safe-parse-json";

import type { PrismaClient } from "@prisma/client";

type Attachments = {
  rootFolderPath: string;
  files: Array<{ filePath: string; isFileExist: boolean }>;
};

type EmailRecordStatic = {
  id: number;
  wireId?: number;
  transferInId?: number;
  subject: string;
  sendDate: Date;
  comment: string;
  isEndBalance: boolean;
  sentAt?: Date;
  sentById?: number;
};
type EmailRecordDynamic = {
  to: string[];
  cc: string[];
  attachments?: Attachments;
};

type EmailRecord = EmailRecordStatic & EmailRecordDynamic;

async function getEmailById(
  prisma: PrismaClient,
  emailId: number
): Promise<EmailRecord> {
  const emailRecord = await prisma.email.findUnique({
    where: {
      emailId,
      deletedAt: null,
    },
    include: { wire: { select: { settlementId: true } } },
  });

  if (!emailRecord) {
    throw new Error(`Email with id ${emailId} not found`);
  }

  const attachmentList = safeParseJson(emailRecord.attachments ?? []);

  const staticData = {
    id: emailRecord.emailId,
    ...(emailRecord?.wireId && { wireId: emailRecord.wireId }),
    ...(emailRecord?.transferInId && {
      transferInId: emailRecord.transferInId,
    }),
    subject: emailRecord.subject,
    sendDate: emailRecord.scheduledSendDate,
    comment: emailRecord?.comment ?? "",
    isEndBalance: emailRecord.isEndBalance,
    ...(emailRecord.sentAt && { sentAt: emailRecord.sentAt }),
    ...(emailRecord.sentById && { sentById: emailRecord.sentById }),
  } satisfies EmailRecordStatic;

  if (emailRecord.sentById ?? emailRecord.sentAt) {
    return {
      ...staticData,
      to: safeParseJson(emailRecord.toMeta ?? []),
      cc: safeParseJson(emailRecord?.ccMeta ?? []),
      attachments: {
        rootFolderPath: "",
        files: attachmentList.map((file) => ({
          filePath: file,
          isFileExist: fs.existsSync(resolveFilePath(file)),
        })),
      },
    } satisfies EmailRecord;
  }

  if (emailRecord.wireId) {
    if (emailRecord.wire?.settlementId) {
      const { emailRecordDynamic, settlementBaseLocation } =
        await getEmailDynamicDataForSettlement(
          prisma,
          emailRecord.wire.settlementId
        );

      return {
        ...staticData,
        ...emailRecordDynamic,
        attachments: {
          rootFolderPath: settlementBaseLocation,
          files: attachmentList.map((file) => ({
            filePath: file,
            isFileExist: fs.existsSync(resolveFilePath(file)),
          })),
        },
      };
    }

    const emailForWire = await getEmailDynamicDataForManualWire(
      prisma,
      emailRecord.wireId
    );

    return {
      ...staticData,
      ...emailForWire,
    };
  }

  if (emailRecord.transferInId) {
    const transferInEmail = await getEmailDynamicDataForTransferIn(
      prisma,
      emailRecord.transferInId
    );

    return {
      ...staticData,
      ...transferInEmail,
    };
  }

  throw new Error(
    `Email with id ${emailId} is missing both wireId and transferInId`
  );
}

async function getEmailDynamicDataForSettlement(
  prisma: PrismaClient,
  settlementId: number
): Promise<{
  emailRecordDynamic: EmailRecordDynamic;
  settlementBaseLocation: string;
}> {
  const settlement = await prisma.customerSettlements.findUnique({
    where: {
      customerSettlementsId: settlementId,
    },
    include: {
      customerCustomerType: {
        select: {
          statementFolderLocation: true,
          customer: {
            include: {
              emailConfiguration: true,
            },
          },
        },
      },
    },
  });
  const customer = settlement?.customerCustomerType?.customer;

  const emailRecordDynamic = {
    to: safeParseJson(customer?.emailConfiguration?.toMeta ?? []),
    cc: safeParseJson(customer?.emailConfiguration?.ccMeta ?? []),
  };

  return {
    emailRecordDynamic,
    settlementBaseLocation:
      settlement?.customerCustomerType?.statementFolderLocation ?? "",
  };
}

async function getEmailDynamicDataForManualWire(
  prisma: PrismaClient,
  wireId: number
): Promise<EmailRecordDynamic> {
  const wire = await prisma.wire.findUnique({
    where: {
      wireId,
    },
    include: {
      customer: {
        include: {
          entity: true,
          emailConfiguration: true,
        },
      },
    },
  });
  const customer = wire?.customer;

  return {
    to: safeParseJson(customer?.emailConfiguration?.toMeta ?? []),
    cc: safeParseJson(customer?.emailConfiguration?.ccMeta ?? []),
  };
}

async function getEmailDynamicDataForTransferIn(
  prisma: PrismaClient,
  transferInId: number
): Promise<EmailRecordDynamic> {
  const transferIn = await prisma.customerWireInOuts.findUnique({
    where: {
      customerWireInPayOutId: transferInId,
    },
    include: {
      customer: {
        include: {
          entity: true,
          emailConfiguration: true,
        },
      },
    },
  });

  return {
    to: safeParseJson(transferIn?.customer?.emailConfiguration?.toMeta ?? []),
    cc: safeParseJson(transferIn?.customer?.emailConfiguration?.ccMeta ?? []),
  };
}

export { getEmailById, type Attachments, type EmailRecord };
