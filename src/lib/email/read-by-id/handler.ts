import { getEmailDetailsById } from "@lib/email/read-by-id/functions";

import type { FastifyReply, FastifyRequest } from "fastify";

type EmailByIdRequest = {
  id: number;
};

export const handler = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const { prisma, getEmailBody } = request.server;
    const { id } = request.params as EmailByIdRequest;

    const [emailBodyResponse, emailDetails] = await Promise.all([
      getEmailBody(id),
      getEmailDetailsById(prisma, id),
    ]);

    return await reply.send({
      data: {
        email: emailDetails,
        html: emailBodyResponse.emailBodyHtml,
      },
    });
  } catch (error) {
    request.log.error(error);
    await reply.code(500).send({
      success: false,
      message: "Error fetching email, please try again.",
      error: { message: (error as Error).message },
    });
  }
};
