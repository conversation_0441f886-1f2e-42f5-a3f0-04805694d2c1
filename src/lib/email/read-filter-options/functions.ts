import { getCustomerTypes } from "@lib/customerType/read-all/repositories";
import { getEntities } from "@lib/entity/read-all/repositories";
import { getAllFrequencies } from "@lib/frequency/repository";

import { type CustomerTypeEmailFilter } from "../../../types/customerType/customer-type";
import { type Entity } from "../../../types/entity/entity";
import { type Frequency } from "../../../types/frequency/frequency";

import type { PrismaClient } from "@prisma/client";

export type EmailFilterOptionsResponse = {
  entities: Entity[];
  frequencies: Frequency[];
  merchantTypes: CustomerTypeEmailFilter[];
};

export async function getEmailFilterOptions(prisma: PrismaClient) {
  const [entities, frequencies, merchantTypes] = await Promise.all([
    getEntities(prisma),
    getAllFrequencies(prisma),
    getCustomerTypes(prisma),
  ]);

  return mapFilterOptionsResponse({ entities, frequencies, merchantTypes });
}

export function mapFilterOptionsResponse({
  entities,
  frequencies,
  merchantTypes,
}: EmailFilterOptionsResponse) {
  return {
    entities: transformFiltersToRecord(entities, "entityName", "entityId"),
    frequencies: transformFiltersToRecord(
      frequencies,
      "frequencyName",
      "frequencyId"
    ),
    merchantTypes: transformFiltersToRecord(
      merchantTypes,
      "customerTypeName",
      "customerTypeId"
    ),
  };
}

function transformFiltersToRecord<T>(
  array: T[],
  keyField: keyof T,
  valueField: keyof T
): Record<string, number> {
  const result: Record<string, number> = {};

  for (const item of array) {
    const key = String(item[keyField]);
    result[key] = Number(item[valueField]);
  }

  return result;
}
