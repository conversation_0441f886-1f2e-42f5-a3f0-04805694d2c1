import { getEmailFilterOptions } from "@lib/email/read-filter-options/functions";

import type { FastifyReply, FastifyRequest } from "fastify";

export const handler = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const { prisma } = request.server;

    const filterOptions = await getEmailFilterOptions(prisma);

    return await reply.send({
      data: filterOptions,
    });
  } catch (error) {
    request.log.error(error);
    await reply.code(500).send({
      message: (error as Error).message,
    });
  }
};
