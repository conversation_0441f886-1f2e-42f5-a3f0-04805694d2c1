import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Fetch Email Filter Options",
  description: "Fetches the available options in the email filters.",
  tags: [tags.email],
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful response",
      type: "object",
      properties: {
        data: {
          type: "object",
          properties: {
            entities: {
              type: "object",
              additionalProperties: {
                type: "number",
              },
              description: "Object of entity name and ID pairs",
            },
            frequencies: {
              type: "object",
              additionalProperties: {
                type: "number",
              },
              description: "Object of statement frequency name and ID pairs",
            },
            merchantTypes: {
              type: "object",
              additionalProperties: {
                type: "number",
              },
              description: "Object of merchant type name and ID pairs",
            },
          },
          required: ["entities", "frequencies", "merchantTypes"],
        },
      },
      required: ["data"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Error response",
      type: "object",
      properties: {
        message: { type: "string" },
      },
      required: ["message"],
    },
  },
};
