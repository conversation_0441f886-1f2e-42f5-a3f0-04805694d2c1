import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

const kycTransactionObjectProperties = {
  name: {
    type: "string",
    description: "Name of the user",
  },
  email: {
    type: "string",
    description: "Email of the user",
  },
  type: {
    type: "string",
    description: "Type of the KYC transaction",
  },
  clientUserId: {
    type: "string",
    description: "Client user ID associated with the KYC transaction",
  },
  status: {
    type: "string",
    description: "Status of the KYC transaction",
  },
  dispCreated: {
    type: "string",
    format: "date-time",
    description: "Creation date of the KYC transaction",
  },
  dispUpdated: {
    type: "string",
    format: "date-time",
    description: "Last updated date of the KYC transaction",
  },
  serviceNumber: {
    type: "string",
    description: "Service number associated with the KYC transaction",
  },
};

export const schema: FastifySchema = {
  summary: "Retrieve all KYC transaction with jaql as well as sql",
  description: `Fetches all KYC transactions based on the service number, from date, and to date.`,
  tags: [tags.test],
  body: {
    type: "object",
    required: ["serviceNumber", "fromDate", "toDate"],
    properties: {
      serviceNumber: {
        type: "string",
        description: "Service number to filter KYC transactions",
      },
      fromDate: {
        type: "object",
        properties: {
          year: { type: "number" },
          month: { type: "number", minimum: 1, maximum: 12 },
          day: { type: "number", minimum: 1, maximum: 31 },
        },
        description: "Start date for the transactions",
      },
      toDate: {
        type: "object",
        properties: {
          year: { type: "number" },
          month: { type: "number", minimum: 1, maximum: 12 },
          day: { type: "number", minimum: 1, maximum: 31 },
        },
        description: "End date for the transactions",
      },
    },
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful response with a list of KYC transactions",
      type: "object",
      properties: {
        data: {
          type: "object",
          properties: {
            jaql: {
              type: "array",
              description: "List of KYC transactions",
              items: {
                type: "object",
                properties: kycTransactionObjectProperties,
              },
            },
            sql: {
              type: "array",
              description: "List of KYC transactions",
              items: {
                type: "object",
                properties: kycTransactionObjectProperties,
              },
            },
          },
        },
      },
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Error response",
      type: "object",
      properties: {
        error: {
          type: "object",
          properties: {
            message: { type: "string", description: "Error message" },
          },
        },
      },
    },
  },
};
