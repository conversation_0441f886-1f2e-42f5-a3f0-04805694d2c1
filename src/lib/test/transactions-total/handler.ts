import { type DateOnly } from "@utils/date-only";

import { getAuthToken } from "../../../services/blusky/authentication";
import {
  getTransactionsTotalAmount,
  getTransactionsTotalAmountWithJaql,
} from "../../../services/blusky/transactions";

import type { FastifyReply, FastifyRequest } from "fastify";

type RequestBody = {
  serviceNumbers: string[];
  fromDate: DateOnly;
  toDate: DateOnly;
};

export const handler = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const timeout = 300_000; // 5 minutes
    reply.raw.setTimeout(timeout);

    const token = await getAuthToken();
    const { serviceNumbers, fromDate, toDate } = request.body as RequestBody;

    const jaqlResult = await getTransactionsTotalAmountWithJaql(
      serviceNumbers,
      fromDate,
      toDate,
      token
    );

    const sqlResult = await getTransactionsTotalAmount(
      serviceNumbers,
      fromDate,
      toDate,
      token
    );

    return await reply.send({
      data: {
        jaql: jaqlResult,
        sql: sqlResult,
      },
    });
  } catch (error) {
    request.log.error(error);
    await reply.code(500).send({
      error: { message: (error as Error).message },
    });
  }
};
