import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Retrieve transaction total amounts with jaql as well as sql",
  description: `Fetches all transaction totals based on the service number, from date, and to date.`,
  tags: [tags.test],
  body: {
    type: "object",
    required: ["serviceNumbers", "fromDate", "toDate"],
    properties: {
      serviceNumbers: {
        type: "array",
        items: { type: "string" },
        description: "List of service numbers to filter transactions",
      },
      fromDate: {
        type: "object",
        required: ["year", "month", "day"],
        properties: {
          year: { type: "integer", description: "Year of the date" },
          month: { type: "integer", description: "Month of the date" },
          day: { type: "integer", description: "Day of the date" },
        },
      },
      toDate: {
        type: "object",
        required: ["year", "month", "day"],
        properties: {
          year: { type: "integer", description: "Year of the date" },
          month: { type: "integer", description: "Month of the date" },
          day: { type: "integer", description: "Day of the date" },
        },
      },
    },
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful response with a list total amounts",
      type: "object",
      properties: {
        data: {
          type: "object",
          properties: {
            jaql: {
              type: "object",
              properties: {
                // eslint-disable-next-line @typescript-eslint/naming-convention
                IDP: {
                  type: "number",
                  description: "Total amount for IDP service",
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ETI: {
                  type: "number",
                  description: "Total amount for ETI service",
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ETF: {
                  type: "number",
                  description: "Total amount for ETF service",
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                RFM: {
                  type: "number",
                  description: "Total amount for RFM service",
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ETO: {
                  type: "number",
                  description: "Total amount for ETO service",
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ACH: {
                  type: "number",
                  description: "Total amount for ACH service",
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                RTO: {
                  type: "number",
                  description: "Total amount for RTO service",
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                RTX: {
                  type: "number",
                  description: "Total amount for RTX service",
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ANR: {
                  type: "number",
                  description: "Total amount for ANR service",
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ANX: {
                  type: "number",
                  description: "Total amount for ANX service",
                },
              },
            },
            sql: {
              type: "object",
              properties: {
                // eslint-disable-next-line @typescript-eslint/naming-convention
                IDP: {
                  type: "number",
                  description: "Total amount for IDP service",
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ETI: {
                  type: "number",
                  description: "Total amount for ETI service",
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ETF: {
                  type: "number",
                  description: "Total amount for ETF service",
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                RFM: {
                  type: "number",
                  description: "Total amount for RFM service",
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ETO: {
                  type: "number",
                  description: "Total amount for ETO service",
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ACH: {
                  type: "number",
                  description: "Total amount for ACH service",
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                RTO: {
                  type: "number",
                  description: "Total amount for RTO service",
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                RTX: {
                  type: "number",
                  description: "Total amount for RTX service",
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ANR: {
                  type: "number",
                  description: "Total amount for ANR service",
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ANX: {
                  type: "number",
                  description: "Total amount for ANX service",
                },
              },
            },
          },
        },
      },
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Error response",
      type: "object",
      properties: {
        error: {
          type: "object",
          properties: {
            message: { type: "string", description: "Error message" },
          },
        },
      },
    },
  },
};
