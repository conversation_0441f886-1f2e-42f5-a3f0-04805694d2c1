import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

const transactionItem = {
  createdDate: {
    type: "string",
    description: "Date when the transaction was created",
  },
  updatedDate: {
    type: "string",
    description: "Date when the transaction was last updated",
  },
  serviceNumber: {
    type: "string",
    description: "Service number associated with the transaction",
  },
  originalAmt: {
    type: "number",
    description: "Original amount of the transaction",
  },
  refundAmt: {
    type: "number",
    description: "Refund amount for the transaction",
  },
  finalAmt: {
    type: "number",
    description: "Final amount after any refunds",
  },
  currency: {
    type: "string",
    description: "Currency of the transaction amount",
  },
  country: {
    type: "string",
    description: "Country associated with the transaction",
  },
  billable: {
    type: "string",
    description: "Indicates if the transaction is billable",
  },
  platform: {
    type: "string",
    description: "Platform where the transaction occurred",
  },
  custNumber: {
    type: "string",
    description: "Customer number associated with the transaction",
  },
  rcode: {
    type: "integer",
    description: "Response code for the transaction",
  },
  integratorName: {
    type: "string",
    description: "Name of the integrator for the transaction",
  },
  programName: {
    type: "string",
    description: "Name of the program associated with the transaction",
  },
  billingName: {
    type: "string",
    description: "Billing name for the transaction",
  },
  // eslint-disable-next-line @typescript-eslint/naming-convention
  transactionID: {
    type: "string",
    description: "Unique identifier for the transaction",
  },
  // eslint-disable-next-line @typescript-eslint/naming-convention
  receiptID: {
    type: "string",
    description: "Receipt identifier for the transaction",
  },
  interacRef: {
    type: "string",
    description: "Interac reference for the transaction",
  },
  // eslint-disable-next-line @typescript-eslint/naming-convention
  fIName: {
    type: "string",
    description: "Financial institution name associated with the transaction",
  },
  status: {
    type: "string",
    description: "Status of the transaction",
  },
};

export const schema: FastifySchema = {
  summary: "Retrieve transaction total amounts with jaql as well as sql",
  description: `Fetches all transaction totals based on the service number, from date, and to date.`,
  tags: [tags.test],
  body: {
    type: "object",
    required: ["serviceNumber", "fromDate", "toDate"],
    properties: {
      serviceNumber: {
        type: "string",
        description: "Service number to filter transactions",
      },
      fromDate: {
        type: "object",
        required: ["year", "month", "day"],
        properties: {
          year: { type: "integer", description: "Year of the date" },
          month: { type: "integer", description: "Month of the date" },
          day: { type: "integer", description: "Day of the date" },
        },
      },
      toDate: {
        type: "object",
        required: ["year", "month", "day"],
        properties: {
          year: { type: "integer", description: "Year of the date" },
          month: { type: "integer", description: "Month of the date" },
          day: { type: "integer", description: "Day of the date" },
        },
      },
    },
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful response with a list of transactions",
      type: "object",
      properties: {
        data: {
          type: "object",
          properties: {
            jaql: {
              type: "object",
              properties: {
                // eslint-disable-next-line @typescript-eslint/naming-convention
                IDP: {
                  type: "array",
                  items: transactionItem,
                  description: "All IDP transactions",
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ETI: {
                  type: "array",
                  items: transactionItem,
                  description: "All ETI transactions",
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ETF: {
                  type: "array",
                  description: "All ETF transactions",
                  items: transactionItem,
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                RFM: {
                  type: "array",
                  description: "All RFM transactions",
                  items: transactionItem,
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ETO: {
                  type: "array",
                  description: "All ETO transactions",
                  items: transactionItem,
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ACH: {
                  type: "array",
                  description: "All ACH transactions",
                  items: transactionItem,
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                RTO: {
                  type: "array",
                  description: "All RTO transactions",
                  items: transactionItem,
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                RTX: {
                  type: "array",
                  description: "All RTX transactions",
                  items: transactionItem,
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ANR: {
                  type: "array",
                  description: "All ANR transactions",
                  items: transactionItem,
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ANX: {
                  type: "array",
                  description: "All ANX transactions",
                  items: transactionItem,
                },
              },
            },
            sql: {
              type: "object",
              properties: {
                // eslint-disable-next-line @typescript-eslint/naming-convention
                IDP: {
                  type: "array",
                  items: transactionItem,
                  description: "All IDP transactions",
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ETI: {
                  type: "array",
                  items: transactionItem,
                  description: "All ETI transactions",
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ETF: {
                  type: "array",
                  description: "All ETF transactions",
                  items: transactionItem,
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                RFM: {
                  type: "array",
                  description: "All RFM transactions",
                  items: transactionItem,
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ETO: {
                  type: "array",
                  description: "All ETO transactions",
                  items: transactionItem,
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ACH: {
                  type: "array",
                  description: "All ACH transactions",
                  items: transactionItem,
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                RTO: {
                  type: "array",
                  description: "All RTO transactions",
                  items: transactionItem,
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                RTX: {
                  type: "array",
                  description: "All RTX transactions",
                  items: transactionItem,
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ANR: {
                  type: "array",
                  description: "All ANR transactions",
                  items: transactionItem,
                },
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ANX: {
                  type: "array",
                  description: "All ANX transactions",
                  items: transactionItem,
                },
              },
            },
          },
        },
      },
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Error response",
      type: "object",
      properties: {
        error: {
          type: "object",
          properties: {
            message: { type: "string", description: "Error message" },
          },
        },
      },
    },
  },
};
