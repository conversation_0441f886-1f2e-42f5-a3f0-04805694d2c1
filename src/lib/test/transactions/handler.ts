import { type DateOnly } from "@utils/date-only";

import {
  getTransactions,
  getTransactionsWithJaql,
  transactionStatus,
} from "../../../services/blusky/transactions";

import type { FastifyReply, FastifyRequest } from "fastify";

type RequestBody = {
  serviceNumber: string;
  fromDate: DateOnly;
  toDate: DateOnly;
};

export const handler = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const { serviceNumber, fromDate, toDate } = request.body as RequestBody;

    const jaqlResult = await getTransactions(
      serviceNumber,
      { fromDate, toDate },
      {
        aggregate: {
          [transactionStatus.rejected1]: {
            aggregateForPlatforms: ["ETI", "RFM"],
            targetPlatform: "RTO",
            keepInOriginal: false,
          },
        },
      }
    );

    const sqlResult = await getTransactionsWithJaql(
      serviceNumber,
      { fromDate, toDate },
      {
        aggregate: {
          [transactionStatus.rejected1]: {
            aggregateForPlatforms: ["ETI", "RFM"],
            targetPlatform: "RTO",
            keepInOriginal: false,
          },
        },
      }
    );

    return await reply.send({
      data: {
        jaql: jaqlResult,
        sql: sqlResult,
      },
    });
  } catch (error) {
    request.log.error(error);
    await reply.code(500).send({
      error: { message: (error as Error).message },
    });
  }
};
