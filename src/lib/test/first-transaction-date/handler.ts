import { getAuthToken } from "../../../services/blusky/authentication";
import {
  getFirstTransactionEver,
  getFirstTransactionEverWithJaql,
} from "../../../services/blusky/transactions";

import type { FastifyReply, FastifyRequest } from "fastify";

type RequestBody = {
  serviceNumber: string;
};

export const handler = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const { serviceNumber } = request.body as RequestBody;

    const token = await getAuthToken();

    const jaqlResult = await getFirstTransactionEverWithJaql(
      serviceNumber,
      token
    );

    const sqlResult = await getFirstTransactionEver(serviceNumber, token);

    return await reply.send({
      data: {
        jaql: jaqlResult,
        sql: sqlResult,
      },
    });
  } catch (error) {
    request.log.error(error);
    await reply.code(500).send({
      error: { message: (error as Error).message },
    });
  }
};
