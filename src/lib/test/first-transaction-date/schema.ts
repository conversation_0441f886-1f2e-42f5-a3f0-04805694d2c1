import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Retrieve first transaction date of a service number",
  description:
    "Fetches the first transaction date for a specified service number, using both JAQL and SQL.",
  tags: [tags.test],
  body: {
    type: "object",
    required: ["serviceNumber"],
    properties: {
      serviceNumber: {
        type: "string",
        description: "Service number to get first transaction date for",
      },
    },
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description:
        "Successful response with first transaction dates from JAQL and SQL",
      type: "object",
      properties: {
        data: {
          type: "object",
          properties: {
            jaql: {
              type: "object",
              properties: {
                year: {
                  type: "integer",
                  description: "Year of the first transaction",
                },
                month: {
                  type: "integer",
                  description: "Month of the first transaction",
                },
                day: {
                  type: "integer",
                  description: "Day of the first transaction",
                },
              },
            },
            sql: {
              type: "object",
              properties: {
                year: {
                  type: "integer",
                  description: "Year of the first transaction",
                },
                month: {
                  type: "integer",
                  description: "Month of the first transaction",
                },
                day: {
                  type: "integer",
                  description: "Day of the first transaction",
                },
              },
            },
          },
        },
      },
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Error response",
      type: "object",
      properties: {
        error: {
          type: "object",
          properties: {
            message: { type: "string", description: "Error message" },
          },
        },
      },
    },
  },
};
