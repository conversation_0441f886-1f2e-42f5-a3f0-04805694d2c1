type Customer = {
  customerId: number;
  customerCustomerTypeId: number;
  serviceNumber: string;
  entityName: string;
  entityId: number;
  entityLogoName: string;
  customerTradingName: string;
  customerName: string;
  statementFolderLocation: string;
  enabled: boolean;
  customerTypeName: string;
  customerTypeId: number;
  nonMerchantSettlementType?: string | undefined;
  countryName?: string | undefined;
  countryId?: number | undefined;
  provinceName?: string | undefined;
  provinceId?: number | undefined;
};

export { type Customer };
