import { getCustomersByFolderPath } from "@lib/common/customer-by-folder-path/functions";

import type { FastifyReply, FastifyRequest } from "fastify";

export const handler = async (
  request: FastifyRequest<{
    Querystring: { folderPath: string };
  }>,
  reply: FastifyReply
) => {
  const { folderPath } = request.query;

  try {
    const customers = await getCustomersByFolderPath(
      request.server.prisma,
      folderPath
    );

    return await reply.send(customers);
  } catch (error) {
    request.log.error(error);

    return reply.code(500).send({ message: (error as Error)?.message });
  }
};
