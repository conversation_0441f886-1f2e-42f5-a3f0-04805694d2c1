import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Retrieve customers associated with a folder path",
  description: `
      This endpoint retrieves all customers linked to the specified folder path where 
      customer settlement files are stored.
    `,
  tags: [tags.customer],
  querystring: {
    type: "object",
    required: ["folderPath"],
    properties: {
      folderPath: {
        type: "string",
        description:
          "The folder path used for storing customer settlement files.",
        minLength: 1,
      },
    },
  },

  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description:
        "A list of customers associated with the provided folder path",
      type: "array",
      items: {
        type: "object",
        properties: {
          customerId: {
            type: "number",
            description: "The unique ID of the customer",
          },
          name: {
            type: "string",
            description: "The name of the customer",
          },
          serviceNumber: {
            type: "string",
            description: "The service number linked to the customer",
          },
          statementFolderLocation: {
            type: "string",
            description:
              "The folder path where the customer's settlement files are stored",
          },
          customerType: {
            type: "string",
            description:
              "The type of customer exampe Merchant, Agent, Sub Agent or Integrator",
          },
        },
      },
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    400: {
      description: "Invalid or missing folder path in the request",
      type: "object",
      properties: {
        message: { type: "string", description: "A descriptive error message" },
      },
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Internal server error",
      type: "object",
      required: ["message"],
      properties: {
        message: {
          type: "string",
          description: "Error message explaining the failure",
        },
      },
    },
  },
};
