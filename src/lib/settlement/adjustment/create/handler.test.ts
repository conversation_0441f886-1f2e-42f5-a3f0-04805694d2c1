import { Decimal } from "@prisma/client/runtime/library";

import { handler } from "./handler";
import { createSettlementAdjustmentService } from "./service";

import type { FastifyReply, FastifyRequest } from "fastify";

type RequestBody = {
  label: string;
  amount: number;
  comment?: string;
  displayCommentExcel: boolean;
  platformCode: string;
  customerId: string;
  fromDate: string;
  toDate: string;
};

vi.mock("./service", () => ({
  createSettlementAdjustmentService: vi.fn(),
}));

const mockPrisma = {};

const mockRequest = {
  body: {
    label: "Test Adjustment",
    amount: 100.5,
    comment: "Test comment",
    displayCommentExcel: true,
    platformCode: "RFM",
    customerId: "123",
    fromDate: "2024-01-01",
    toDate: "2024-01-15",
  },
  server: { prisma: mockPrisma },
  log: { error: vi.fn() },
} as unknown as FastifyRequest;

const mockReply = {
  code: vi.fn(() => mockReply),
  send: vi.fn(),
} as unknown as FastifyReply;

describe("Settlement Adjustment Create Handler", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should create adjustment successfully", async () => {
    const mockCreatedAdjustment = {
      customerSettlementAdjustmentId: 789,
      customerSettlementsId: 456,
      label: "Test Adjustment",
      amount: new Decimal(100.5),
      comment: "Test comment",
      displayCommentExcel: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    };

    vi.mocked(createSettlementAdjustmentService).mockResolvedValue(
      mockCreatedAdjustment
    );

    await handler(mockRequest, mockReply);

    expect(createSettlementAdjustmentService).toHaveBeenCalledWith(
      mockRequest.body,
      mockRequest.server.prisma
    );
    expect(mockReply.code).toHaveBeenCalledWith(200);
    expect(mockReply.send).toHaveBeenCalledWith(mockCreatedAdjustment);
  });

  it("should handle null comment correctly", async () => {
    const requestWithoutComment = {
      ...mockRequest,
      body: {
        ...(mockRequest.body as RequestBody),
        comment: undefined,
      },
    } as unknown as FastifyRequest;

    const mockCreatedAdjustment = {
      customerSettlementAdjustmentId: 789,
      customerSettlementsId: 456,
      label: "Test Adjustment",
      amount: new Decimal(100.5),
      comment: null,
      displayCommentExcel: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    };

    vi.mocked(createSettlementAdjustmentService).mockResolvedValue(
      mockCreatedAdjustment
    );

    await handler(requestWithoutComment, mockReply);

    expect(createSettlementAdjustmentService).toHaveBeenCalledWith(
      requestWithoutComment.body,
      mockRequest.server.prisma
    );
  });

  it("should handle displayCommentExcel false correctly", async () => {
    const requestWithDisplayCommentFalse = {
      ...mockRequest,
      body: {
        ...(mockRequest.body as RequestBody),
        displayCommentExcel: false,
      },
    } as unknown as FastifyRequest;

    const mockCreatedAdjustment = {
      customerSettlementAdjustmentId: 789,
      customerSettlementsId: 456,
      label: "Test Adjustment",
      amount: new Decimal(100.5),
      comment: "Test comment",
      displayCommentExcel: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    };

    vi.mocked(createSettlementAdjustmentService).mockResolvedValue(
      mockCreatedAdjustment
    );

    await handler(requestWithDisplayCommentFalse, mockReply);

    expect(createSettlementAdjustmentService).toHaveBeenCalledWith(
      requestWithDisplayCommentFalse.body,
      mockRequest.server.prisma
    );
  });

  it("should throw error when platform code not found", async () => {
    const error = new Error("Platform code RFM not found.");
    vi.mocked(createSettlementAdjustmentService).mockRejectedValue(error);

    await handler(mockRequest, mockReply);

    expect(mockRequest.log.error).toHaveBeenCalledWith(error);
    expect(mockReply.code).toHaveBeenCalledWith(404);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: "Platform code RFM not found.",
    });
  });

  it("should handle service error", async () => {
    const error = new Error("Failed to create adjustment");
    vi.mocked(createSettlementAdjustmentService).mockRejectedValue(error);

    await handler(mockRequest, mockReply);

    expect(mockRequest.log.error).toHaveBeenCalledWith(error);
    expect(mockReply.code).toHaveBeenCalledWith(500);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: "Failed to create adjustment",
    });
  });

  it("should handle invalid customer ID", async () => {
    const error = new TypeError("Invalid customer ID: abc");
    vi.mocked(createSettlementAdjustmentService).mockRejectedValue(error);

    await handler(mockRequest, mockReply);

    expect(mockRequest.log.error).toHaveBeenCalledWith(error);
    expect(mockReply.code).toHaveBeenCalledWith(400);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: "Invalid customer ID: abc",
    });
  });
});
