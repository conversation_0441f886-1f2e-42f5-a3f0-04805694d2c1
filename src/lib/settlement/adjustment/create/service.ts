import { getPlatformIdByCode } from "@lib/platform/read/repositories";
import { getSettlementIdByCustomerIdPlatformIdAndPeriod } from "@lib/settlement/repository/settlement-frequency";
import { type PlatformCode } from "@lib/settlement/repository/types";

import { createSettlementAdjustment } from "./repository";

import type { PrismaClient } from "@prisma/client";

export type CreateSettlementAdjustmentRequest = {
  label: string;
  amount: number;
  comment?: string;
  displayCommentExcel: boolean;
  platformCode: PlatformCode;
  customerId: string;
  fromDate: string;
  toDate: string;
};

export const createSettlementAdjustmentService = async (
  request: CreateSettlementAdjustmentRequest,
  prisma: PrismaClient
) => {
  const {
    label,
    amount,
    comment,
    displayCommentExcel,
    platformCode,
    customerId,
    fromDate,
    toDate,
  } = request;

  const platformId: number = await getPlatformIdByCode(platformCode, prisma);

  const customerIdNumber = Number.parseInt(customerId, 10);

  if (Number.isNaN(customerIdNumber)) {
    throw new TypeError(`Invalid customer ID: ${customerId}`);
  }

  const customerSettlementsId =
    await getSettlementIdByCustomerIdPlatformIdAndPeriod(
      customerIdNumber,
      platformId,
      new Date(fromDate),
      new Date(toDate),
      prisma
    );

  const adjustment = await createSettlementAdjustment(
    {
      customerSettlementsId,
      label,
      amount,
      comment,
      displayCommentExcel,
    },
    prisma
  );

  return adjustment;
};
