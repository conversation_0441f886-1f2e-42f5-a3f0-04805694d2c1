import type { PrismaClient } from "@prisma/client";

export type CreateSettlementAdjustmentData = {
  customerSettlementsId: number;
  label: string;
  amount: number;
  comment: string | undefined;
  displayCommentExcel: boolean;
};

export const createSettlementAdjustment = async (
  data: CreateSettlementAdjustmentData,
  prisma: PrismaClient
) => {
  return prisma.customerSettlementAdjustments.create({
    data: {
      ...data,
      comment: data.comment ?? null,
    },
  });
};
