import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Create settlement adjustment",
  description: "Creates an adjustment for a specific settlement",
  tags: [tags.settlement],
  body: {
    type: "object",
    required: [
      "label",
      "amount",
      "displayCommentExcel",
      "platformCode",
      "customerId",
      "fromDate",
      "toDate",
    ],
    properties: {
      label: {
        type: "string",
        description: "Descriptive label for the adjustment",
      },
      amount: {
        type: "number",
        description: "Adjustment amount (can be positive or negative)",
      },
      comment: {
        type: "string",
        description: "Optional comment for the adjustment",
      },
      displayCommentExcel: {
        type: "boolean",
        description: "Whether to display the comment in Excel exports",
      },
      platformCode: {
        type: "string",
        description: "Platform code for the settlement",
      },
      customerId: {
        type: "string",
        description: "Customer ID",
      },
      fromDate: {
        type: "string",
        format: "date",
        description: "Start date of the settlement period",
      },
      toDate: {
        type: "string",
        format: "date",
        description: "End date of the settlement period",
      },
    },
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful response",
      type: "object",
      properties: {
        customerSettlementAdjustmentId: { type: "number" },
        customerSettlementsId: { type: "number" },
        label: { type: "string" },
        amount: { type: "number" },
        comment: { type: ["string", "null"] },
        displayCommentExcel: { type: "boolean" },
        createdAt: { type: "string", format: "date" },
        updatedAt: { type: "string", format: "date" },
        deletedAt: { type: "string", format: "date" },
      },
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    400: {
      description: "Bad Request - Invalid input data",
      type: "object",
      properties: {
        message: { type: "string" },
        error: { type: "string" },
      },
      required: ["message"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    404: {
      description: "Settlement adjustment not found",
      type: "object",
      properties: {
        message: { type: "string" },
        error: { type: "string" },
      },
      required: ["message"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Internal Server Error",
      type: "object",
      properties: {
        message: { type: "string" },
        error: { type: "string" },
      },
      required: ["message"],
    },
  },
};
