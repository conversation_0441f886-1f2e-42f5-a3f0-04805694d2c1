import {
  createSettlementAdjustmentService,
  type CreateSettlementAdjustmentRequest,
} from "./service";

import type { FastifyReply, FastifyRequest } from "fastify";

export const handler = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const requestData = request.body as CreateSettlementAdjustmentRequest;

    const adjustment = await createSettlementAdjustmentService(
      requestData,
      request.server.prisma
    );

    return await reply.code(200).send(adjustment);
  } catch (error) {
    request.log.error(error);

    const errorMessage = (error as Error).message;

    if (errorMessage.includes("not found")) {
      return reply.code(404).send({
        message: errorMessage,
      });
    }

    if (errorMessage.includes("Invalid")) {
      return reply.code(400).send({
        message: errorMessage,
      });
    }

    return reply.code(500).send({
      message: errorMessage,
    });
  }
};
