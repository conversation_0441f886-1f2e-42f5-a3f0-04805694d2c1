import { softDeleteSettlementAdjustmentByIdService } from "./service";

import type { FastifyReply, FastifyRequest } from "fastify";

export const handler = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const { adjustmentId } = request.params as { adjustmentId: string };

    const adjustmentIdNumber = Number.parseInt(adjustmentId, 10);

    if (Number.isNaN(adjustmentIdNumber)) {
      return await reply.code(400).send({
        message: "Invalid adjustment ID",
      });
    }

    const removedAdjustment = await softDeleteSettlementAdjustmentByIdService(
      adjustmentIdNumber,
      request.server.prisma
    );

    return await reply.code(200).send(removedAdjustment);
  } catch (error) {
    request.log.error(error);

    const errorMessage = (error as Error).message;

    if (errorMessage.includes("not found")) {
      return reply.code(404).send({
        message: errorMessage,
      });
    }

    if (errorMessage.includes("Invalid")) {
      return reply.code(400).send({
        message: errorMessage,
      });
    }

    return reply.code(500).send({
      message: errorMessage,
    });
  }
};
