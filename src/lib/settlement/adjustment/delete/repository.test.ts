import { Decimal } from "@prisma/client/runtime/library";

import { softDeleteSettlementAdjustmentById } from "./repository";

import type { PrismaClient } from "@prisma/client";

vi.mock("@prisma/client");

describe("softDeleteSettlementAdjustmentById", () => {
  const mockPrisma = {
    customerSettlementAdjustments: {
      findUnique: vi.fn(),
      update: vi.fn(),
    },
  } as unknown as PrismaClient;

  afterEach(() => {
    vi.clearAllMocks();
  });

  it("should soft delete a settlement adjustment by ID", async () => {
    const mockAdjustment = {
      customerSettlementAdjustmentId: 123,
      customerSettlementsId: 456,
      label: "Test Adjustment",
      amount: new Decimal(100.5),
      comment: "Test comment",
      displayCommentExcel: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: new Date(),
    };

    vi.mocked(
      mockPrisma.customerSettlementAdjustments.findUnique
    ).mockResolvedValue(mockAdjustment);

    vi.mocked(
      mockPrisma.customerSettlementAdjustments.update
    ).mockResolvedValue(mockAdjustment);

    const result = await softDeleteSettlementAdjustmentById(123, mockPrisma);

    expect(
      mockPrisma.customerSettlementAdjustments.update
    ).toHaveBeenCalledWith({
      where: {
        customerSettlementAdjustmentId: 123,
        deletedAt: null,
      },
      data: {
        deletedAt: expect.any(Date) as Date,
      },
    });

    expect(result).toEqual(mockAdjustment);
  });

  it("should handle errors when adjustment is not found", async () => {
    const error = new Error("Record not found");
    vi.mocked(
      mockPrisma.customerSettlementAdjustments.update
    ).mockRejectedValue(error);

    await expect(
      softDeleteSettlementAdjustmentById(999, mockPrisma)
    ).rejects.toThrow("Record not found");
  });
});
