import type { PrismaClient } from "@prisma/client";

export const softDeleteSettlementAdjustmentById = async (
  adjustmentId: number,
  prisma: PrismaClient
) => {
  const adjustment = await prisma.customerSettlementAdjustments.findUnique({
    where: {
      customerSettlementAdjustmentId: adjustmentId,
      deletedAt: null,
    },
  });

  if (!adjustment) {
    throw new Error("Settlement adjustment not found");
  }

  return prisma.customerSettlementAdjustments.update({
    where: {
      customerSettlementAdjustmentId: adjustmentId,
      deletedAt: null,
    },
    data: {
      deletedAt: new Date(),
    },
  });
};
