import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Delete settlement adjustment",
  description: "Delete an adjustment for a specific settlement by ID",
  tags: [tags.settlement],
  params: {
    type: "object",
    required: ["id", "adjustmentId"],
    properties: {
      id: {
        type: "string",
        description: "Settlement ID",
      },
      adjustmentId: {
        type: "string",
        description: "Adjustment ID to delete",
      },
    },
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful deletion",
      type: "object",
      properties: {
        customerSettlementAdjustmentId: { type: "number" },
        customerSettlementsId: { type: "number" },
        label: { type: "string" },
        amount: { type: "number" },
        comment: { type: ["string", "null"] },
        displayCommentExcel: { type: "boolean" },
        createdAt: { type: "string", format: "date" },
        updatedAt: { type: "string", format: "date" },
        deletedAt: { type: "string", format: "date" },
      },
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    400: {
      description: "Bad Request - Invalid input data",
      type: "object",
      properties: {
        message: { type: "string" },
        error: { type: "string" },
      },
      required: ["message"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    404: {
      description: "Settlement adjustment not found",
      type: "object",
      properties: {
        message: { type: "string" },
        error: { type: "string" },
      },
      required: ["message"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Internal Server Error",
      type: "object",
      properties: {
        message: { type: "string" },
        error: { type: "string" },
      },
      required: ["message"],
    },
  },
};
