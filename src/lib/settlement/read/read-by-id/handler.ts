import { getSettlementData } from "./functions/get-settlement-data";

import type { FastifyReply, FastifyRequest } from "fastify";

type RequestParameters = {
  id: number;
};

export const handler = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const { id } = request.params as RequestParameters;

    const settlementData = await getSettlementData(id, request.server.prisma);

    if (!settlementData) {
      return await reply.code(404).send({
        message: "Settlement not found",
      });
    }

    return await reply.send(settlementData);
  } catch (error) {
    request.log.error(error);
    await reply.code(500).send({
      message: (error as Error).message,
    });
  }
};
