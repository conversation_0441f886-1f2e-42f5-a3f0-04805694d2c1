export type SettlementInformation = {
  customerId: number;
  fromDate: Date;
  toDate: Date;
};

export type SettlementFilters = {
  filters: {
    textInputValue: string;
    clientType: string;
    displayAdjusted: string;
    state: string;
    status: string;
    frequency: string;
    startDate: string | undefined;
    endDate: string | undefined;
  };
  sortKey: string;
  sortOrder: "asc" | "desc";
  pageNumber: number;
  recordsPerPage: number;
};
