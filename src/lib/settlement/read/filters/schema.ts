import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Fetch Settlement Filters",
  description: "Fetches the setttlement filters.",
  tags: [tags.build],
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful response",
      type: "object",
      properties: {
        clientTypes: {
          type: "array",
          items: {
            type: "string",
            description: "Type of the client",
          },
        },
        settlementStates: {
          type: "array",
          items: {
            type: "string",
            description: "State of the settlement",
          },
        },
        settlementFrequencies: {
          type: "array",
          items: {
            type: "string",
            description: "Frequency of the settlement",
          },
        },
      },
      required: ["clientTypes", "settlementStates", "settlementFrequencies"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Error response",
      type: "object",
      properties: {
        message: { type: "string" },
      },
      required: ["message"],
    },
  },
};
