import { type PrismaClient } from "@prisma/client";

async function getClientTypeNames(prisma: PrismaClient) {
  return prisma.customerType
    .findMany({
      select: {
        customerTypeName: true,
      },
    })
    .then((clientTypesResponse) => {
      return new Set(
        clientTypesResponse.map((clientType) => {
          return clientType.customerTypeName;
        })
      );
    });
}

export { getClientTypeNames };
