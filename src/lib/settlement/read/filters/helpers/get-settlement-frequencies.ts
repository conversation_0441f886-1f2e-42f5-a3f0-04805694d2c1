import { type PrismaClient } from "@prisma/client";

async function getSettlementFrequencies(prisma: PrismaClient) {
  return prisma.statementFrequency
    .findMany({
      select: {
        statementFrequencyName: true,
      },
    })
    .then((settlementFrequenciesResponse) => {
      return new Set(
        settlementFrequenciesResponse.map((statementFrequency) => {
          return statementFrequency.statementFrequencyName;
        })
      );
    });
}

export { getSettlementFrequencies };
