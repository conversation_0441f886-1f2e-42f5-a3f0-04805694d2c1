import { type PrismaClient } from "@prisma/client";

async function getSettlementStates(prisma: PrismaClient) {
  return prisma.settlementState
    .findMany({
      select: {
        stateName: true,
      },
    })
    .then((settlementStatesResponse) => {
      return new Set(
        settlementStatesResponse.map((settlementState) => {
          return settlementState.stateName;
        })
      );
    });
}

export { getSettlementStates };
