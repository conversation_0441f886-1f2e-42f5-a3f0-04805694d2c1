import { getSettlementFrequencies } from "./get-settlement-frequencies";

describe("get-settlement-frequencies", () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it("should return a set of settlement frequencies", async () => {
    const mockPrisma = {
      statementFrequency: {
        findMany: vi
          .fn()
          .mockResolvedValue([
            { statementFrequencyName: "Monthly" },
            { statementFrequencyName: "Semi-Monthly" },
            { statementFrequencyName: "Twice Per Week" },
            { statementFrequencyName: "Twice Per Week" },
            { statementFrequencyName: "Weekly-Monday" },
            { statementFrequencyName: "Weekly-Friday" },
          ]),
      },
    };

    // @ts-expect-error Not checking for prisma types in tests
    const result = await getSettlementFrequencies(mockPrisma);

    expect(result).toEqual(
      new Set([
        "Monthly",
        "Semi-Monthly",
        "Twice Per Week",
        "Weekly-Monday",
        "Weekly-Friday",
      ])
    );
    expect(mockPrisma.statementFrequency.findMany).toHaveBeenCalledWith({
      select: { statementFrequencyName: true },
    });
  });

  it("should return an empty set when no frequencies are found", async () => {
    const mockPrisma = {
      statementFrequency: {
        findMany: vi.fn().mockResolvedValue([]),
      },
    };

    // @ts-expect-error Not checking for prisma types in tests
    const result = await getSettlementFrequencies(mockPrisma);

    expect(result).toEqual(new Set());
    expect(mockPrisma.statementFrequency.findMany).toHaveBeenCalledWith({
      select: { statementFrequencyName: true },
    });
  });

  it("should handle errors gracefully", async () => {
    const mockPrisma = {
      statementFrequency: {
        findMany: vi.fn().mockRejectedValue(new Error("Database error")),
      },
    };

    // @ts-expect-error Not checking for prisma types in tests
    await expect(getSettlementFrequencies(mockPrisma)).rejects.toThrow(
      "Database error"
    );

    expect(mockPrisma.statementFrequency.findMany).toHaveBeenCalledWith({
      select: { statementFrequencyName: true },
    });
  });
});
