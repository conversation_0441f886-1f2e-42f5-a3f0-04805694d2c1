import { getClientTypeNames } from "./get-client-type-names";

describe("get-client-type-names", () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it("should return a set of client type names", async () => {
    const mockPrisma = {
      customerType: {
        findMany: vi
          .fn()
          .mockResolvedValue([
            { customerTypeName: "Agent" },
            { customerTypeName: "Integrator" },
            { customerTypeName: "Merchant" },
            { customerTypeName: "Sub Agent" },
            { customerTypeName: "Sub Agent" },
          ]),
      },
    };

    // @ts-expect-error Not checking for prisma types in tests
    const result = await getClientTypeNames(mockPrisma);

    expect(result).toEqual(
      new Set(["Agent", "Integrator", "Merchant", "Sub Agent"])
    );
    expect(mockPrisma.customerType.findMany).toHaveBeenCalledWith({
      select: { customerTypeName: true },
    });
  });

  it("should return an empty set when no client types are found", async () => {
    const mockPrisma = {
      customerType: {
        findMany: vi.fn().mockResolvedValue([]),
      },
    };

    // @ts-expect-error Not checking for prisma types in tests
    const result = await getClientTypeNames(mockPrisma);

    expect(result).toEqual(new Set());
    expect(mockPrisma.customerType.findMany).toHaveBeenCalledWith({
      select: { customerTypeName: true },
    });
  });

  it("should handle errors gracefully", async () => {
    const mockPrisma = {
      customerType: {
        findMany: vi.fn().mockRejectedValue(new Error("Database error")),
      },
    };

    // @ts-expect-error Not checking for prisma types in tests
    await expect(getClientTypeNames(mockPrisma)).rejects.toThrow(
      "Database error"
    );

    expect(mockPrisma.customerType.findMany).toHaveBeenCalledWith({
      select: { customerTypeName: true },
    });
  });
});
