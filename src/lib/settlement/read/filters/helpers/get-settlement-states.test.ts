import { getSettlementStates } from "./get-settlement-states";

describe("get-settlement-states", () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it("should return a set of settlement states", async () => {
    const mockPrisma = {
      settlementState: {
        findMany: vi
          .fn()
          .mockResolvedValue([
            { stateName: "Approval Pending" },
            { stateName: "Approval Success" },
            { stateName: "Processing" },
            { stateName: "Approval Processing" },
            { stateName: "Error" },
            { stateName: "Approval Error" },
            { stateName: "Skipped" },
            { stateName: "Skipped" },
          ]),
      },
    };

    // @ts-expect-error Not checking for prisma types in tests
    const result = await getSettlementStates(mockPrisma);

    expect(result).toEqual(
      new Set([
        "Approval Pending",
        "Approval Success",
        "Processing",
        "Approval Processing",
        "Error",
        "Approval Error",
        "Skipped",
      ])
    );
    expect(mockPrisma.settlementState.findMany).toHaveBeenCalledWith({
      select: { stateName: true },
    });
  });

  it("should return an empty set when no settlement states are found", async () => {
    const mockPrisma = {
      settlementState: {
        findMany: vi.fn().mockResolvedValue([]),
      },
    };

    // @ts-expect-error Not checking for prisma types in tests
    const result = await getSettlementStates(mockPrisma);

    expect(result).toEqual(new Set());
    expect(mockPrisma.settlementState.findMany).toHaveBeenCalledWith({
      select: { stateName: true },
    });
  });

  it("should handle errors gracefully", async () => {
    const mockPrisma = {
      settlementState: {
        findMany: vi.fn().mockRejectedValue(new Error("Database error")),
      },
    };

    // @ts-expect-error Not checking for prisma types in tests
    await expect(getSettlementStates(mockPrisma)).rejects.toThrow(
      "Database error"
    );

    expect(mockPrisma.settlementState.findMany).toHaveBeenCalledWith({
      select: { stateName: true },
    });
  });
});
