import * as GetSettlementFilters from "@lib/settlement/read/filters/get-settlement-filters";
import { type FastifyReply, type FastifyRequest } from "fastify";

import { handler } from "./handler";

describe("read settlements handler", () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it("should return settlement filters", async () => {
    const spyGetSettlementFilters = vi
      .spyOn(GetSettlementFilters, "getSettlementFilters")
      .mockResolvedValue({
        clientTypes: ["Agent", "Integrator", "Merchant", "Sub Agent"],
        settlementFrequencies: [
          "Monthly",
          "Semi-Monthly",
          "Twice Per Week",
          "Weekly-Monday",
          "Weekly-Friday",
        ],
        settlementStates: [
          "Approval Pending",
          "Approval Success",
          "Processing",
          "Approval Processing",
          "Error",
          "Approval Error",
          "Skipped",
        ],
      });

    const mockRequest = {
      server: {
        prisma: {},
      },
    } as unknown as FastifyRequest<Record<string, unknown>>;

    const mockReply = {
      send: vi.fn(),
      code: vi.fn().mockReturnThis(),
    } as unknown as FastifyReply;

    await handler(mockRequest, mockReply);

    expect(spyGetSettlementFilters).toHaveBeenCalled();
    expect(mockReply.send).toHaveBeenCalledWith({
      clientTypes: ["Agent", "Integrator", "Merchant", "Sub Agent"],
      settlementFrequencies: [
        "Monthly",
        "Semi-Monthly",
        "Twice Per Week",
        "Weekly-Monday",
        "Weekly-Friday",
      ],
      settlementStates: [
        "Approval Pending",
        "Approval Success",
        "Processing",
        "Approval Processing",
        "Error",
        "Approval Error",
        "Skipped",
      ],
    });
  });

  it("should handle errors", async () => {
    const errorMessage = "Some kind of error";
    const spyGetSettlementFilters = vi
      .spyOn(GetSettlementFilters, "getSettlementFilters")
      .mockRejectedValue(new Error(errorMessage));

    const mockRequest = {
      server: {
        prisma: {},
      },
      log: {
        error: vi.fn(),
      },
    } as unknown as FastifyRequest<Record<string, unknown>>;

    const mockReply = {
      send: vi.fn(),
      code: vi.fn().mockReturnThis(),
    } as unknown as FastifyReply;

    await handler(mockRequest, mockReply);

    expect(spyGetSettlementFilters).toHaveBeenCalled();
    expect(mockReply.code).toHaveBeenCalledWith(500);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: errorMessage,
    });
    expect(mockRequest.log.error).toHaveBeenCalledWith(new Error(errorMessage));
  });
});
