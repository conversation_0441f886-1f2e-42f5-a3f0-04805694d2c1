import { getSettlementFilters } from "./get-settlement-filters";

import type { FastifyReply, FastifyRequest } from "fastify";

export const handler = async (
  request: FastifyRequest<Record<string, unknown>>,
  reply: FastifyReply
) => {
  try {
    const result = await getSettlementFilters(request.server.prisma);

    return await reply.send(result);
  } catch (error) {
    request.log.error(error);
    await reply.code(500).send({
      message: (error as Error).message || "Internal Server Error",
    });
  }
};
