import { type PrismaClient } from "@prisma/client";

import { getClientTypeNames } from "./helpers/get-client-type-names";
import { getSettlementFrequencies } from "./helpers/get-settlement-frequencies";
import { getSettlementStates } from "./helpers/get-settlement-states";

const getSettlementFilters = async (
  prisma: PrismaClient
): Promise<{
  clientTypes: string[];
  settlementStates: string[];
  settlementFrequencies: string[];
}> => {
  const clientTypePromise = getClientTypeNames(prisma);
  const settlementStatesPromise = getSettlementStates(prisma);
  const settlementFrequenciesPromise = getSettlementFrequencies(prisma);

  return Promise.all([
    clientTypePromise,
    settlementStatesPromise,
    settlementFrequenciesPromise,
  ]).then(([clientTypes, settlementStates, settlementFrequencies]) => {
    return {
      clientTypes: [...clientTypes],
      settlementStates: [...settlementStates],
      settlementFrequencies: [...settlementFrequencies],
    };
  });
};

export { getSettlementFilters };
