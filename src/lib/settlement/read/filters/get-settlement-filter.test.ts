import { type PrismaClient } from "@prisma/client";

import { getSettlementFilters } from "./get-settlement-filters";
import * as GetClientTypeNames from "./helpers/get-client-type-names";
import * as GetSettlementFrequencies from "./helpers/get-settlement-frequencies";
import * as GetSettlementStates from "./helpers/get-settlement-states";

describe("getSettlementFilters", () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it("should return settlement filters", async () => {
    const spyGetClientTypeNames = vi
      .spyOn(GetClientTypeNames, "getClientTypeNames")
      .mockResolvedValue(new Set(["clientType1", "clientType2"]));
    const spyGetSettlementStates = vi
      .spyOn(GetSettlementStates, "getSettlementStates")
      .mockResolvedValue(new Set(["state1", "state2", "state2"]));
    const spyGetSettlementFrequencies = vi
      .spyOn(GetSettlementFrequencies, "getSettlementFrequencies")
      .mockResolvedValue(new Set(["frequency1", "frequency2"]));

    const mockPrisma = {};

    const result = await getSettlementFilters(mockPrisma as PrismaClient);
    expect(result).toEqual({
      clientTypes: ["clientType1", "clientType2"],
      settlementStates: ["state1", "state2"],
      settlementFrequencies: ["frequency1", "frequency2"],
    });

    expect(spyGetClientTypeNames).toHaveBeenCalledWith(mockPrisma);
    expect(spyGetSettlementStates).toHaveBeenCalledWith(mockPrisma);
    expect(spyGetSettlementFrequencies).toHaveBeenCalledWith(mockPrisma);
  });

  it("should handle errors", async () => {
    const mockPrisma = {};
    const errorMessage = "Test error";
    vi.spyOn(GetClientTypeNames, "getClientTypeNames").mockRejectedValue(
      new Error(errorMessage)
    );

    await expect(
      getSettlementFilters(mockPrisma as PrismaClient)
    ).rejects.toThrow(errorMessage);
  });
});
