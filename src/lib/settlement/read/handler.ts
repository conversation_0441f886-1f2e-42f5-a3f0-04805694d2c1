import { getSettlements } from "./get-settlements";
import { getFilterConditions } from "./helpers/filters/get-filter-conditions";

import type { FastifyReply, FastifyRequest } from "fastify";

type RequestBody = {
  offset: number;
  limit: number;
  sortKey: string;
  sortOrder: "asc" | "desc";
  nameOrServiceNumber?: string;
  clientType?: string;
  displayAdjusted?: string;
  state?: string;
  status?: string;
  frequency?: string;
  startDate?: string;
  endDate?: string;
};

export const handler = async (request: FastifyRequest, reply: FastifyReply) => {
  const {
    offset,
    limit,
    sortKey,
    sortOrder,
    nameOrServiceNumber,
    clientType,
    displayAdjusted,
    state,
    status,
    frequency,
    startDate,
    endDate,
  } = request.body as RequestBody;
  const { joinConditions, havingConditions, whereConditions } =
    getFilterConditions({
      nameOrServiceNumber: nameOrServiceNumber?.trim(),
      clientType,
      displayAdjusted,
      state,
      status,
      frequency,
      startDate,
      endDate,
    });

  try {
    const result = await getSettlements({
      prisma: request.server.prisma,
      joinConditions,
      whereConditions,
      havingConditions,
      offset,
      limit,
      sortKey,
      sortOrder,
    });

    return await reply.send(result);
  } catch (error) {
    request.log.error(error);
    await reply.code(500).send({
      message: (error as Error).message || "Internal Server Error",
    });
  }
};
