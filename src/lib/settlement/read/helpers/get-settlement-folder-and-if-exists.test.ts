import * as fsPromises from "node:fs/promises";

import { getSettlementFolderLocation } from "@lib/settlement/repository/get-settlement-folder-location";

import { getSettlementFolderAndIfExists } from "./get-settlement-folder-and-if-exists";

describe("getSettlementFolderAndIfExists", () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  vi.mock(
    "@lib/settlement/repository/get-settlement-folder-location.ts",
    () => ({
      getSettlementFolderLocation: vi.fn(),
    })
  );

  vi.mock("node:fs/promises", () => ({
    access: vi.fn(),
  }));

  it("should return null and false when customerCustomerTypeId is undefined", async () => {
    // @ts-expect-error mocking prisma type
    const result = await getSettlementFolderAndIfExists(undefined, {});
    expect(result).toEqual({
      settlementFolderLocation: undefined,
      settlementFolderExists: false,
    });
  });

  it("should return null and false when folder does not exist", async () => {
    // @ts-expect-error mocking prisma type
    const result = await getSettlementFolderAndIfExists(1, {});
    expect(result).toEqual({
      settlementFolderLocation: undefined,
      settlementFolderExists: false,
    });
  });

  it("should return folder location and true when folder exists", async () => {
    vi.mocked(getSettlementFolderLocation).mockResolvedValue("/test/123");
    vi.mocked(fsPromises.access).mockResolvedValue();

    // @ts-expect-error mocking prisma type
    const result = await getSettlementFolderAndIfExists(1, {});
    expect(result).toEqual({
      settlementFolderLocation: "/test/123",
      settlementFolderExists: true,
    });
  });

  it("should return folder location and false when folder is in DB but does not exist", async () => {
    vi.mocked(getSettlementFolderLocation).mockResolvedValue("/test/123");
    vi.mocked(fsPromises.access).mockRejectedValue(new Error("ENOENT"));

    // @ts-expect-error mocking prisma type
    const result = await getSettlementFolderAndIfExists(1, {});
    expect(result).toEqual({
      settlementFolderLocation: "/test/123",
      settlementFolderExists: false,
    });
  });
});
