import { Prisma } from "@prisma/client";

export const stateStatusJoin = Prisma.sql`JOIN (
    SELECT sgs.*
    FROM customerSettlementGenerationSchedule sgs
    JOIN (
        SELECT customerCustomerTypeId, fromDate, toDate, MAX(updatedAt) AS maxUpdatedAt
        FROM customerSettlementGenerationSchedule
        GROUP BY customerCustomerTypeId, fromDate, toDate
    ) max_update 
      ON sgs.customerCustomerTypeId = max_update.customerCustomerTypeId
     AND sgs.fromDate = max_update.fromDate
     AND sgs.toDate = max_update.toDate
     AND sgs.updatedAt = max_update.maxUpdatedAt
    JOIN (
        SELECT customerCustomerTypeId, fromDate, toDate, updatedAt, MAX(customerSettlementGenerationScheduleId) AS maxScheduleId
        FROM customerSettlementGenerationSchedule
        GROUP BY customerCustomerTypeId, fromDate, toDate, updatedAt
    ) max_schedule 
      ON sgs.customerCustomerTypeId = max_schedule.customerCustomerTypeId
     AND sgs.fromDate = max_schedule.fromDate
     AND sgs.toDate = max_schedule.toDate
     AND sgs.updatedAt = max_schedule.updatedAt
     AND sgs.customerSettlementGenerationScheduleId = max_schedule.maxScheduleId
) latest_sgs 
  ON s.customerCustomerTypeId = latest_sgs.customerCustomerTypeId 
 AND s.fromDate = latest_sgs.fromDate 
 AND s.toDate = latest_sgs.toDate
JOIN settlementState ss 
  ON latest_sgs.generationStatus = ss.generationStatus 
 AND latest_sgs.generationType = ss.generationType`;
