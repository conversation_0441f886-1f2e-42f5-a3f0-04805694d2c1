import { getSettlementPreference } from "./repository";
import { defaultSettlementPreferences } from "../../constants/default-preferences";
import { type SettlementFilters } from "../types";

import type { PrismaClient } from "@prisma/client";

export const getSettlementPreferencesService = async (
  userId: number,
  prisma: PrismaClient
): Promise<SettlementFilters> => {
  const preferences = await getSettlementPreference(userId, prisma);

  return preferences ?? defaultSettlementPreferences;
};
