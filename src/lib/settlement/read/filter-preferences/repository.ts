import { type SettlementFilters } from "../types";

import type { PrismaClient } from "@prisma/client";

export const getSettlementPreference = async (
  userId: number,
  prisma: PrismaClient
): Promise<SettlementFilters | undefined> => {
  const userPreference = await prisma.userPreference.findUnique({
    where: { userId },
    select: {
      settlementPreference: true,
    },
  });

  return userPreference?.settlementPreference as SettlementFilters | undefined;
};
