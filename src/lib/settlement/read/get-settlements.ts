import { getGroupedCustomerSettlementsBySort } from "./helpers/get-grouped-customer-settlements-by-sort";
import { getGroupedSettlementsTotalCount } from "./helpers/get-grouped-settlements-count";
import { groupSettlementData } from "./helpers/group-settlement-data";
import { type SettlementInformation } from "./types";
import { type SettlementSummary } from "../repository/types";

import type { Prisma, PrismaClient } from "@prisma/client";

const getSettlementsTimeout = 20_000; // 20 seconds

const getSettlements = async ({
  prisma,
  joinConditions,
  whereConditions,
  havingConditions,
  offset,
  limit,
  sortKey,
  sortOrder,
}: {
  prisma: PrismaClient;
  joinConditions: Prisma.Sql[];
  whereConditions: Prisma.Sql[];
  havingConditions: Prisma.Sql[];
  offset: number;
  limit: number;
  sortKey: string;
  sortOrder: "asc" | "desc";
}): Promise<{ totalCount: number; settlements: SettlementSummary[] }> => {
  const allPlatformRecords = await prisma.$transaction(
    async (tx) => {
      let customerSettlementsGrouped = [];

      // Step 1
      // Get the customer settlements grouped by customerId and fromDate
      // in their sorted order
      customerSettlementsGrouped = await getGroupedCustomerSettlementsBySort({
        tx,
        joinConditions,
        whereConditions,
        havingConditions,
        offset,
        limit,
        sortKey,
        sortOrder,
      });

      // Step 2
      // Create an array of GroupedSettlements objects that contains the data we will return
      const customerSettlementsData: SettlementInformation[] =
        customerSettlementsGrouped.map((group) => ({
          customerId: group.customerId,
          fromDate: group.fromDate,
          toDate: group.toDate,
        }));

      // Step 3
      // Get the total count of customer settlements for the pagination
      const totalCount = await getGroupedSettlementsTotalCount(
        tx,
        whereConditions,
        joinConditions,
        havingConditions
      );

      const settlementSummaryPromises = [];

      // Step 4
      // Get the X number of settlements from the DB for one settlement record
      for (const settlementData of customerSettlementsData) {
        settlementSummaryPromises.push(groupSettlementData(tx, settlementData));
      }

      // Step 5
      // Wait till all settlementSummaries are done

      const customerSettlementSummaries = await Promise.all(
        settlementSummaryPromises
      );

      return [totalCount, customerSettlementSummaries];
    },
    {
      timeout: getSettlementsTimeout,
    }
  );

  return {
    totalCount: allPlatformRecords[0] as number,
    settlements: allPlatformRecords[1] as SettlementSummary[],
  };
};

export { getSettlements };
