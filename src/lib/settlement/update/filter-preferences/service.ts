import { updateSettlementPreference } from "./repository";
import { type SettlementFilters } from "../../read/types";

import type { PrismaClient } from "@prisma/client";

export const updateSettlementPreferenceService = async (
  userId: number,
  settlementFilters: SettlementFilters,
  prisma: PrismaClient
): Promise<SettlementFilters> => {
  const preference = await updateSettlementPreference(
    userId,
    settlementFilters,
    prisma
  );

  return preference;
};
