/* eslint-disable @typescript-eslint/naming-convention */
import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

const filterPropertiesSchema = {
  type: "object",
  properties: {
    textInputValue: {
      type: "string",
    },
    clientType: {
      type: "string",
      enum: ["", "Agent", "Integrator", "Merchant", "Sub Agent"],
      description:
        "Client type filter - can be one of the specified values or empty string",
    },
    displayAdjusted: {
      type: "string",
      enum: ["Show All", "Adjustments Only", "No Adjustments"],
      description: "Display adjustment filter",
    },
    state: {
      type: "string",
      enum: [
        "",
        "Approval Pending",
        "Approval Success",
        "Processing",
        "Approval Processing",
        "Error",
        "Approval Error",
        "Skipped",
      ],
      description:
        "Settlement state filter - can be one of the specified values or empty string",
    },
    status: {
      type: "string",
      enum: ["Settlement Approval", "Settlement Generation", "No Filter"],
      description: "Settlement status filter",
    },
    frequency: {
      type: "string",
      enum: [
        "",
        "Monthly",
        "Semi-Monthly",
        "Twice Per Week",
        "Weekly-Monday",
        "Weekly-Friday",
      ],
      description:
        "Settlement frequency filter - can be one of the specified values or empty string",
    },
    startDate: {
      type: "string",
      format: "date-time",
    },
    endDate: {
      type: "string",
      format: "date-time",
    },
  },
  additionalProperties: false,
} as const;

const settlementFiltersSchema = {
  type: "object",
  properties: {
    filters: filterPropertiesSchema,
    sortKey: {
      type: "string",
      enum: ["fromDate", "toDate", "clientName"],
      description: "Field to sort by",
    },
    sortOrder: {
      type: "string",
      enum: ["asc", "desc"],
      description: "Sort direction",
    },
    pageNumber: {
      type: "number",
      minimum: 1,
      maximum: 10_000,
      description: "Page number starting from 1",
    },
    recordsPerPage: {
      type: "number",
      minimum: 1,
      maximum: 200,
      description: "Number of records per page",
    },
  },
  additionalProperties: false,
} as const;

export const schema: FastifySchema = {
  summary: "Update Settlement Preference of User",
  description:
    "Saves the settlement filter preference of the user, or default preferences if not set.",
  tags: [tags.settlement],
  body: settlementFiltersSchema,
  response: {
    200: {
      description: "Settlement preference data",
      ...settlementFiltersSchema,
    },
    400: {
      description: "Validation error response",
      type: "object",
      properties: {
        message: { type: "string" },
      },
      required: ["message"],
      additionalProperties: false,
    },
    500: {
      description: "Internal server error response",
      type: "object",
      properties: {
        message: { type: "string" },
      },
      required: ["message"],
      additionalProperties: false,
    },
  },
};
