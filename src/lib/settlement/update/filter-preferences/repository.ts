import { type SettlementFilters } from "../../read/types";

import type { PrismaClient } from "@prisma/client";

export const updateSettlementPreference = async (
  userId: number,
  settlementFilters: SettlementFilters,
  prisma: PrismaClient
): Promise<SettlementFilters> => {
  const { settlementPreference } = await prisma.userPreference.upsert({
    where: { userId },
    update: {
      settlementPreference: settlementFilters,
    },
    create: {
      userId,
      settlementPreference: settlementFilters,
    },
    select: {
      settlementPreference: true,
    },
  });

  if (!settlementPreference) {
    throw new Error("Failed to retrieve settlement preference");
  }

  return settlementPreference as SettlementFilters;
};
