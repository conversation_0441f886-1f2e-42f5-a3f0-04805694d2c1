import { vi, describe, it, expect, afterEach } from "vitest";

import { handler } from "./handler";
import { updateSettlementPreferenceService } from "./service";
import { type SettlementFilters } from "../../read/types";

import type { FastifyReply, FastifyRequest } from "fastify";

vi.mock("./service", () => ({
  updateSettlementPreferenceService: vi.fn(),
  defaultSettlementPreferences: {
    filters: {
      textInputValue: "",
      clientType: "",
      displayAdjusted: "Show All",
      state: "",
      status: "No Filter",
      frequency: "",
      startDate: "",
      endDate: "",
    },
    sortKey: "",
    sortOrder: "asc",
    pageNumber: 1,
    recordsPerPage: 20,
  },
}));

describe("Settlement Filter Preferences Handler", () => {
  const mockPrisma = {};
  const mockUserId = 123;

  const mockReply = {
    code: vi.fn(() => mockReply),
    send: vi.fn(),
  } as unknown as FastifyReply;

  const mockRequestBase = {
    server: { prisma: mockPrisma },
    userProfile: { id: mockUserId },
    log: { error: vi.fn() },
  };

  const mockPutRequestBody = {
    ...mockRequestBase,
    method: "PUT",
    body: {
      filters: {
        textInputValue: "test search",
        clientType: "Agent",
        displayAdjusted: "Show All",
        state: "Approval Success",
        status: "Settlement Approval",
        frequency: "Monthly",
        startDate: "2024-01-01",
        endDate: "2024-01-31",
      },
      sortKey: "customerName",
      sortOrder: "desc" as const,
      pageNumber: 2,
      recordsPerPage: 50,
    },
  } as unknown as FastifyRequest<{ Body: SettlementFilters }>;

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("update request with body (save preferences)", () => {
    it("should save and return user preferences", async () => {
      const savedPreference = mockPutRequestBody.body;
      vi.mocked(updateSettlementPreferenceService).mockResolvedValue(
        savedPreference
      );

      await handler(mockPutRequestBody, mockReply);

      expect(updateSettlementPreferenceService).toHaveBeenCalledWith(
        mockUserId,
        mockPutRequestBody.body,
        mockPrisma
      );
      expect(mockReply.code).toHaveBeenCalledWith(200);
      expect(mockReply.send).toHaveBeenCalledWith(savedPreference);
    });
  });
});
