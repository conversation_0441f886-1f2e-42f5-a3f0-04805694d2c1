import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Delete a list of settlements",
  description: `
    This endpoint deletes a list of settlements by their IDs.
    If there are any settlements in the list that have approved wires, then no settlements will be deleted at all.
    If deletion was not successful, the endpoint will return a list of settlement IDs that have approved wires.
  `,
  tags: [tags.settlement],
  body: {
    type: "object",
    required: ["settlementIds"],
    properties: {
      settlementIds: {
        type: "array",
        description: "List of settlement IDs to delete",
        minItems: 1,
        items: {
          type: "number",
          minimum: 1,
        },
      },
    },
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description:
        "Settlements deleted successfully, or failed due to approved wires",
      type: "object",
      properties: {
        success: {
          type: "boolean",
          description: `
            Indicates whether the settlements were deleted successfully.
            If false, failedSettlementIds will contain IDs of settlements that have approved wires.
          `,
        },
        failedSettlementIds: {
          type: "array",
          items: {
            type: "number",
          },
          description:
            "List of settlement IDs that have approved wires. Present when success is false.",
        },
      },
      required: ["success"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Internal server error",
      type: "object",
      required: ["message"],
      properties: {
        message: {
          type: "string",
          description: "Error message explaining the failure",
        },
      },
    },
  },
};
