import { deleteSettlements } from "./delete-settlements";
import {
  getSummarySettlement,
  getPlatformSettlements,
  getSettlementWires,
  hardDeleteSettlementEmails,
  hardDeleteSettlementWires,
  hardDeleteSettlementKycs,
  hardDeleteSettlementAdjustments,
  hardDeleteSettlements,
} from "../repository/delete-settlements";

import type { PrismaClient } from "@prisma/client";

vi.mock("../repository/delete-settlements", () => ({
  getSummarySettlement: vi.fn(),
  getPlatformSettlements: vi.fn(),
  getSettlementWires: vi.fn(),
  hardDeleteSettlementEmails: vi.fn(),
  hardDeleteSettlementWires: vi.fn(),
  hardDeleteSettlementKycs: vi.fn(),
  hardDeleteSettlementAdjustments: vi.fn(),
  hardDeleteSettlements: vi.fn(),
}));

const mockPrisma = {
  // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call
  $transaction: vi.fn((callback) => callback(mockTx)),
} as unknown as PrismaClient;
const mockTx = {};

describe("deleteSettlements", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should skip settlements if summarySettlement is not found", async () => {
    const result = await deleteSettlements([1, 2], mockPrisma);

    expect(getSummarySettlement).toHaveBeenCalledWith(1, mockPrisma);
    expect(getSummarySettlement).toHaveBeenCalledWith(2, mockPrisma);
    expect(hardDeleteSettlementEmails).not.toHaveBeenCalled();
    expect(hardDeleteSettlementWires).not.toHaveBeenCalled();
    expect(hardDeleteSettlementKycs).not.toHaveBeenCalled();
    expect(hardDeleteSettlementAdjustments).not.toHaveBeenCalled();
    expect(hardDeleteSettlements).not.toHaveBeenCalled();
    expect(result).toEqual([]);
  });

  it("should skip deletion and return failedSettlementIds if wires are approved", async () => {
    vi.mocked(getSummarySettlement).mockResolvedValueOnce({
      customerId: 1,
      fromDate: new Date(),
      toDate: new Date(),
    });
    vi.mocked(getPlatformSettlements).mockResolvedValueOnce([1, 10, 11]);
    vi.mocked(getSettlementWires).mockResolvedValueOnce([
      { wireId: 100, wireApprovedById: 2, isCancelled: false },
    ]);

    const result = await deleteSettlements([1], mockPrisma);

    expect(result).toEqual([1]);
    expect(hardDeleteSettlementEmails).not.toHaveBeenCalled();
    expect(hardDeleteSettlementWires).not.toHaveBeenCalled();
    expect(hardDeleteSettlementKycs).not.toHaveBeenCalled();
    expect(hardDeleteSettlementAdjustments).not.toHaveBeenCalled();
    expect(hardDeleteSettlements).not.toHaveBeenCalled();
  });

  it("should delete all related data if no wires are approved", async () => {
    vi.mocked(getSummarySettlement).mockResolvedValueOnce({
      customerId: 1,
      fromDate: new Date(),
      toDate: new Date(),
    });
    vi.mocked(getPlatformSettlements).mockResolvedValueOnce([1, 10, 11]);
    vi.mocked(getSettlementWires).mockResolvedValueOnce([
      { wireId: 100, wireApprovedById: 5, isCancelled: true },
      { wireId: 101, wireApprovedById: null, isCancelled: false },
    ]);

    await deleteSettlements([1], mockPrisma);

    const wireIds = [100, 101];
    const platformSettlementIds = [1, 10, 11];

    expect(hardDeleteSettlementEmails).toHaveBeenCalledWith(wireIds, mockTx);
    expect(hardDeleteSettlementWires).toHaveBeenCalledWith(wireIds, mockTx);
    expect(hardDeleteSettlementKycs).toHaveBeenCalledWith(
      platformSettlementIds,
      mockTx
    );
    expect(hardDeleteSettlementAdjustments).toHaveBeenCalledWith(
      platformSettlementIds,
      mockTx
    );
    expect(hardDeleteSettlements).toHaveBeenCalledWith(
      platformSettlementIds,
      mockTx
    );
  });

  it("should process multiple settlementIds, collect failed ones, and not proceed with deletion", async () => {
    // First: summary found, wires approved
    vi.mocked(getSummarySettlement)
      .mockResolvedValueOnce({
        customerId: 111,
        fromDate: new Date(),
        toDate: new Date(),
      })
      // Second: summary not found
      // eslint-disable-next-line unicorn/no-useless-undefined
      .mockResolvedValueOnce(undefined)
      // Third: summary found, wires not approved
      .mockResolvedValueOnce({
        customerId: 333,
        fromDate: new Date(),
        toDate: new Date(),
      })
      // Fourth: summary found, wires approved
      .mockResolvedValueOnce({
        customerId: 444,
        fromDate: new Date(),
        toDate: new Date(),
      });

    vi.mocked(getPlatformSettlements)
      .mockResolvedValueOnce([1, 10])
      .mockResolvedValueOnce([3, 30])
      .mockResolvedValueOnce([4, 40]);

    vi.mocked(getSettlementWires)
      .mockResolvedValueOnce([
        { wireId: 100, wireApprovedById: 5, isCancelled: false },
      ])
      .mockResolvedValueOnce([
        { wireId: 300, wireApprovedById: null, isCancelled: false },
      ])
      .mockResolvedValueOnce([
        { wireId: 400, wireApprovedById: null, isCancelled: true },
        { wireId: 401, wireApprovedById: 5, isCancelled: false },
      ]);

    const result = await deleteSettlements([1, 2, 3, 4], mockPrisma);

    expect(result).toEqual([1, 4]);
    expect(hardDeleteSettlementEmails).not.toHaveBeenCalled();
    expect(hardDeleteSettlementWires).not.toHaveBeenCalled();
    expect(hardDeleteSettlementKycs).not.toHaveBeenCalled();
    expect(hardDeleteSettlementAdjustments).not.toHaveBeenCalled();
    expect(hardDeleteSettlements).not.toHaveBeenCalled();
  });

  it("should handle empty settlementIds array", async () => {
    const result = await deleteSettlements([], mockPrisma);
    expect(result).toEqual([]);
    expect(getSummarySettlement).not.toHaveBeenCalled();
  });
});
