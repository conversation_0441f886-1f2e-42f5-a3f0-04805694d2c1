import {
  getPlatformSettlements,
  getSettlementWires,
  getSummarySettlement,
  hardDeleteSettlementAdjustments,
  hardDeleteSettlementEmails,
  hardDeleteSettlementKycs,
  hardDeleteSettlements,
  hardDeleteSettlementWires,
} from "../repository/delete-settlements";

import type { PrismaClient } from "@prisma/client";

export const deleteSettlements = async (
  settlementIds: number[],
  prisma: PrismaClient
): Promise<number[]> => {
  const failedSettlementIds: number[] = [];

  const settlementPromises = settlementIds.map(async (settlementId) => {
    const summarySettlement = await getSummarySettlement(settlementId, prisma);

    if (!summarySettlement) {
      return;
    }

    // Grab all related platform settlements - this includes the summary settlement as well
    const platformSettlementIds = await getPlatformSettlements(
      summarySettlement.customerId,
      summarySettlement.fromDate,
      summarySettlement.toDate,
      prisma
    );

    const wires = await getSettlementWires(platformSettlementIds, prisma);

    const hasApprovedWires = wires.some(
      (wire) => wire.wireApprovedById && !wire.isCancelled
    );

    // Cannot delete settlements with approved wires
    if (hasApprovedWires) {
      failedSettlementIds.push(settlementId);
    }

    return {
      platformSettlementIds,
      wireIds: wires.map((wire) => wire.wireId),
    };
  });

  const settlements = await Promise.all(settlementPromises);

  // If any settlement has approved wires, do not proceed to delete any settlements
  if (failedSettlementIds.length > 0) {
    return failedSettlementIds;
  }

  const validSettlements = settlements.filter(
    (settlement) => settlement !== undefined
  ) as Array<{
    platformSettlementIds: number[];
    wireIds: number[];
  }>;

  const deletePromises = validSettlements.map(
    async ({ platformSettlementIds, wireIds }) => {
      await prisma.$transaction(async (tx) => {
        await hardDeleteSettlementEmails(wireIds, tx);

        await hardDeleteSettlementWires(wireIds, tx);

        await hardDeleteSettlementKycs(platformSettlementIds, tx);

        await hardDeleteSettlementAdjustments(platformSettlementIds, tx);

        await hardDeleteSettlements(platformSettlementIds, tx);
      });
    }
  );

  await Promise.all(deletePromises);

  return failedSettlementIds;
};
