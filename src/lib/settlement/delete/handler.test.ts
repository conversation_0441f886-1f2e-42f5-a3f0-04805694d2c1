import { Prisma } from "@prisma/client";

import { deleteSettlements } from "./delete-settlements";
import { handler } from "./handler";

import type { FastifyReply, FastifyRequest } from "fastify";

vi.mock("./delete-settlements", () => ({
  deleteSettlements: vi.fn(),
}));

const request = {
  body: { settlementIds: [1, 2, 3] },
  userProfile: { id: 42, fullName: "Test User" },
  log: {
    info: vi.fn(),
    error: vi.fn(),
  },
  server: {
    prisma: {},
  },
} as unknown as FastifyRequest<{ Body: { settlementIds: number[] } }>;

const reply = {
  send: vi.fn(),
  code: vi.fn(() => reply),
} as unknown as FastifyReply;

describe("Delete Settlements Handler", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should send success true if all settlements deleted", async () => {
    vi.mocked(deleteSettlements).mockResolvedValue([]);

    await handler(request, reply);

    expect(deleteSettlements).toHaveBeenCalledWith(
      [1, 2, 3],
      request.server.prisma
    );
    expect(reply.send).toHaveBeenCalledWith({ success: true });
  });

  it("should send failedSettlementIds if some settlements failed", async () => {
    vi.mocked(deleteSettlements).mockResolvedValue([2]);

    await handler(request, reply);

    expect(reply.send).toHaveBeenCalledWith({
      success: false,
      failedSettlementIds: [2],
    });
  });

  it("should handle PrismaClientKnownRequestError", async () => {
    const error = new Prisma.PrismaClientKnownRequestError("msg", {
      code: "P2002",
      clientVersion: "1.0.0",
    });

    vi.mocked(deleteSettlements).mockRejectedValue(error);

    await handler(request, reply);

    expect(reply.code).toHaveBeenCalledWith(500);
    expect(reply.send).toHaveBeenCalledWith({
      message:
        "Database error occurred. Some settlements may not have been deleted.",
    });
  });

  it("should handle generic errors", async () => {
    const error = new Error("Something went wrong");
    vi.mocked(deleteSettlements).mockRejectedValue(error);

    await handler(request, reply);

    expect(reply.code).toHaveBeenCalledWith(500);
    expect(reply.send).toHaveBeenCalledWith({
      message: "Something went wrong",
    });
  });
});
