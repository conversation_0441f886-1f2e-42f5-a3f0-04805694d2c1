import { Prisma } from "@prisma/client";

import { deleteSettlements } from "./delete-settlements";

import type { FastifyReply, FastifyRequest } from "fastify";

type RequestBody = {
  settlementIds: number[];
};

export const handler = async (
  request: FastifyRequest<{ Body: RequestBody }>,
  reply: FastifyReply
) => {
  try {
    const { settlementIds } = request.body;
    const { id: userId, fullName: userName } = request.userProfile;

    request.log.info(
      {
        settlementIds,
        userId,
        userName,
      },
      "Starting deletion of settlements."
    );

    const failedSettlementIds = await deleteSettlements(
      settlementIds,
      request.server.prisma
    );

    if (failedSettlementIds.length > 0) {
      request.log.info(
        { settlementIds, failedSettlementIds, userId, userName },
        "Failed to delete settlements due to approved wires."
      );

      return await reply.send({
        success: false,
        failedSettlementIds,
      });
    }

    request.log.info(
      {
        settlementIds,
        userId,
        userName,
      },
      "Settlements deleted successfully."
    );

    return await reply.send({ success: true });
  } catch (error) {
    request.log.error(error);

    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      return reply.code(500).send({
        message:
          "Database error occurred. Some settlements may not have been deleted.",
      });
    }

    return reply.code(500).send({
      message: (error as Error).message,
    });
  }
};
