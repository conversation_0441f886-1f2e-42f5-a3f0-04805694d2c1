import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Get Settlements History with pagination",
  description: `Fetches a paginated list of the settlement job history based on pagination parameters (offset, limit).`,
  tags: [tags.settlement],
  querystring: {
    type: "object",
    required: ["offset", "limit"],
    properties: {
      offset: {
        type: "integer",
        minimum: 0,
        default: 0,
        description: "Pagination offset",
      },
      limit: {
        type: "integer",
        minimum: 1,
        maximum: 200,
        default: 20,
        description: "Number of records to retrieve",
      },
    },
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description:
        "Successful response with a list of settlement jobs and total count",
      type: "object",
      properties: {
        jobs: {
          type: "array",
          description: "List of settlement jobs",
          items: {
            type: "object",
            properties: {
              jobId: {
                type: "integer",
                description: "Settlement job ID",
              },
              fromDate: {
                type: "string",
                format: "date",
                description: "Start date of the settlement period",
              },
              toDate: {
                type: "string",
                format: "date",
                description: "End date of the settlement period",
              },
              status: {
                type: "string",
                enum: ["PROGRESS", "SUCCESS", "ERROR"],
                description: "Status of the settlement job",
              },
              userId: {
                type: "integer",
                description: "ID of the user that generated this job",
              },
              userName: {
                type: "string",
                description: "User name of the user that generated this job",
              },
              generatedAt: {
                type: "string",
                format: "date-time",
                description: "Date and time this job was generated at",
              },
              meta: {
                type: "object",
                properties: {
                  errorCount: {
                    type: "integer",
                    description:
                      "Number of settlements that had an error during generation",
                  },
                  totalCount: {
                    type: "integer",
                    description: "Total number of settlements generated",
                  },
                  skippedCount: {
                    type: "integer",
                    description:
                      "Number of settlements skipped during generation",
                  },
                  successCount: {
                    type: "integer",
                    description: "Number of settlements generated successfully",
                  },
                  timeTakenInSec: {
                    type: "number",
                    description:
                      "Time taken to generate the settlements in seconds",
                  },
                },
              },
            },
          },
        },
        totalCount: {
          type: "integer",
          description: "Total count of settlement jobs",
        },
      },
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Internal server error",
      type: "object",
      properties: {
        message: {
          type: "string",
          description: "User-friendly message indicating an error has occurred",
        },
        error: {
          type: "object",
          properties: {
            message: {
              type: "string",
              description: "Message of the error encountered",
            },
          },
        },
      },
    },
  },
};
