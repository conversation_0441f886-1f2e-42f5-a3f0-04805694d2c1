import { getAllFrequencies } from "@lib/frequency/repository";
import { getLatestJobsByFrequency } from "@lib/settlement/repository/settlement-frequency-job";

import type { FastifyReply, FastifyRequest } from "fastify";

type RequestBody = {
  frequencyId: number;
};

type JobData = {
  jobId: number;
  fromDate: Date;
  toDate: Date;
  status: string;
  userId: number | undefined;
  userName: string | undefined;
  frequencyId: string;
  frequencyName: string | undefined;
  createdAt: Date | undefined;
};

export const handler = async (
  request: FastifyRequest<{ Body: RequestBody }>,
  reply: FastifyReply
) => {
  try {
    const frequencies = await getAllFrequencies(request.server.prisma);

    const groupedJobs = await getLatestJobsByFrequency(request.server.prisma);

    const results: Record<string, JobData | Record<string, never>> = {};

    for (const frequency of frequencies) {
      const job = groupedJobs[frequency.frequencyId];
      results[frequency.frequencyName] = job
        ? {
            jobId: job.customerSettlementJobId,
            fromDate: job.fromDate,
            toDate: job.toDate,
            status: job.status,
            userId: job.user?.userId,
            userName: job.user?.userName,
            frequencyId: frequency.frequencyId.toString(),
            frequencyName: job.statementFrequency?.statementFrequencyName,
            createdAt: job.createdAt ?? undefined,
          }
        : {};
    }

    await reply.send({
      frequencyJobs: results,
      allFrequencies: frequencies,
    });
  } catch (error) {
    request.log.error(error);
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";

    await reply.code(500).send({
      message: errorMessage,
    });
  }
};
