import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Get Latest Jobs by Frequency",
  description: "Retrieve the latest settlement jobs grouped by frequency.",
  tags: [tags.settlement],
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      type: "object",
      properties: {
        frequencyJobs: {
          type: "object",
          additionalProperties: {
            type: "object",
            properties: {
              jobId: { type: "number" },
              fromDate: { type: "string", format: "date-time" },
              toDate: { type: "string", format: "date-time" },
              status: { type: "string" },
              userId: { type: "number" },
              userName: { type: "string" },
              frequencyId: { type: "string" },
              frequencyName: { type: "string" },
              createdAt: { type: "string", format: "date-time" },
            },
            required: [
              "jobId",
              "fromDate",
              "toDate",
              "status",
              "userId",
              "userName",
              "frequencyId",
              "frequencyName",
              "createdAt",
            ],
            nullable: true,
          },
        },
        allFrequencies: {
          type: "array",
          items: {
            type: "object",
            properties: {
              frequencyId: { type: "number" },
              frequencyName: { type: "string" },
              frequencyCode: { type: "string" },
            },
            required: ["frequencyId", "frequencyName", "frequencyCode"],
          },
        },
      },
      required: ["frequencyJobs", "allFrequencies"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    403: {
      type: "object",
      properties: {
        message: { type: "string" },
      },
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    404: {
      type: "object",
      properties: {
        message: { type: "string" },
      },
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      type: "object",
      properties: {
        message: { type: "string" },
        error: { type: "string" },
      },
    },
  },
};
