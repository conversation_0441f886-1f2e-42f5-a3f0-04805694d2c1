import { getAllFrequencies } from "@lib/frequency/repository"; // Import the function to mock
import { getLatestJobsByFrequency } from "@lib/settlement/repository/settlement-frequency-job";
import { SettlementStatus } from "@lib/settlement/repository/types";
import { vi, describe, it, expect, beforeEach } from "vitest";

import { handler } from "./handler";

import type { PrismaClient } from "@prisma/client";
import type { FastifyBaseLogger, FastifyReply, FastifyRequest } from "fastify";
import type { Mock } from "vitest";

vi.mock("@lib/settlement/repository/settlement-frequency-job");

type MockPrisma = {
  statementFrequency: {
    findMany: Mock;
  };
};

type MockFastifyRequest = FastifyRequest<{ Body: { frequencyId: number } }> & {
  server: {
    prisma: PrismaClient & MockPrisma;
  };
};

type MockFastifyReply = FastifyReply & {
  send: Mock;
  status: Mock;
};

describe("handler", () => {
  let mockRequest: MockFastifyRequest;
  let mockReply: MockFastifyReply;

  beforeEach(() => {
    mockRequest = {
      server: {
        prisma: {
          statementFrequency: {
            findMany: vi.fn(),
          },
          $disconnect: vi.fn(),
        } as unknown as PrismaClient & MockPrisma,
      },
      body: { frequencyId: 1 },
    } as unknown as MockFastifyRequest;

    mockReply = {
      send: vi.fn().mockReturnThis(),
      status: vi.fn().mockReturnThis(),
    } as unknown as MockFastifyReply;

    vi.clearAllMocks();
  });

  it("should return formatted job data for each frequency", async () => {
    // Mock frequency data
    vi.mocked(getAllFrequencies).mockResolvedValue([
      {
        frequencyId: 1,
        frequencyName: "Monthly",
        frequencyCode: "M",
      },
      {
        frequencyId: 2,
        frequencyName: "Semi-Monthly",
        frequencyCode: "SM",
      },
    ]);

    const mockJob = {
      customerSettlementJobId: 123,
      fromDate: new Date("2023-01-01"),
      toDate: new Date("2023-01-31"),
      status: SettlementStatus.SUCCESS,
      frequencyId: 1,
      user: {
        userId: 1,
        userName: "test-user",
      },
      statementFrequency: {
        statementFrequencyName: "Monthly",
      },
      createdAt: new Date("2023-02-01"),
    };
    vi.mock("@lib/frequency/repository", () => ({
      getAllFrequencies: vi.fn(), // Mock the getAllFrequencies function
    }));

    vi.mocked(getLatestJobsByFrequency).mockResolvedValue({
      // eslint-disable-next-line @typescript-eslint/naming-convention
      1: mockJob,
    });

    await handler(mockRequest, mockReply);

    expect(mockReply.send).toHaveBeenCalledWith({
      frequencyJobs: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        Monthly: {
          jobId: 123,
          fromDate: new Date("2023-01-01T00:00:00.000Z"),
          toDate: new Date("2023-01-31T00:00:00.000Z"),
          status: SettlementStatus.SUCCESS,
          userId: 1,
          userName: "test-user",
          frequencyId: "1",
          frequencyName: "Monthly",
          createdAt: new Date("2023-02-01T00:00:00.000Z"),
        },
        "Semi-Monthly": {},
      },
      allFrequencies: [
        {
          frequencyId: 1,
          frequencyName: "Monthly",
          frequencyCode: "M",
        },
        {
          frequencyId: 2,
          frequencyName: "Semi-Monthly",
          frequencyCode: "SM",
        },
      ],
    });
  });

  it("should return a 500 status and error message if getLatestJobsByFrequency throws an error", async () => {
    // Mock frequency data
    vi.mocked(getAllFrequencies).mockResolvedValue([
      {
        frequencyId: 1,
        frequencyName: "Monthly",
        frequencyCode: "M",
      },
    ]);

    const testError = new Error("Test database error");
    vi.mocked(getLatestJobsByFrequency).mockRejectedValue(testError);

    const mockLog: Partial<FastifyBaseLogger> = {
      error: vi.fn(),
      fatal: vi.fn(),
      warn: vi.fn(),
      info: vi.fn(),
      debug: vi.fn(),
      trace: vi.fn(),
    };

    mockRequest.log = mockLog as FastifyBaseLogger;

    const mockReply = {
      code: vi.fn().mockReturnThis(),
      send: vi.fn(),
      log: {
        error: vi.fn(),
      },
    } as unknown as FastifyReply;

    await handler(mockRequest, mockReply);

    expect(mockLog.error).toHaveBeenCalledWith(testError);

    expect(mockReply.code).toHaveBeenCalledWith(500);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: "Test database error",
    });
  });
});
