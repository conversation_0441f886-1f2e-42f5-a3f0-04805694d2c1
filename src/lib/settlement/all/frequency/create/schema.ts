import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Generate Settlement for a Given Frequency and Date Range",
  description:
    "Generate a settlement for the specified frequency and date range.",
  tags: [tags.settlement],
  body: {
    type: "object",
    properties: {
      frequencyId: { type: "number" },
      fromDate: { type: "string", format: "date-time" },
      toDate: { type: "string", format: "date-time" },
      test: {
        type: "boolean",
        description: "Optional flag to indicate if this is a test run",
        default: false,
      },
    },
    required: ["frequencyId", "fromDate", "toDate"],
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      type: "object",
      properties: {
        success: { type: "boolean" },
        message: { type: "string" },
        data: {
          type: "object",
          properties: {
            settlement: {
              type: "object",
              properties: {
                jobId: { type: "number" },
                frequencyName: { type: "string" },
                fromDate: { type: "string", format: "date-time" },
                toDate: { type: "string", format: "date-time" },
              },
              required: ["jobId", "frequencyName", "fromDate", "toDate"],
            },
          },
          required: ["settlement"],
        },
      },
    },

    // eslint-disable-next-line @typescript-eslint/naming-convention
    400: {
      type: "object",
      properties: {
        success: { type: "boolean" },
        message: { type: "string" },
      },
    },

    // eslint-disable-next-line @typescript-eslint/naming-convention
    403: {
      type: "object",
      properties: {
        success: { type: "boolean" },
        message: { type: "string" },
      },
    },

    // eslint-disable-next-line @typescript-eslint/naming-convention
    404: {
      type: "object",
      properties: {
        success: { type: "boolean" },
        message: { type: "string" },
      },
    },

    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      type: "object",
      properties: {
        success: { type: "boolean" },
        message: { type: "string" },
        error: { type: "string" },
      },
    },
  },
};
