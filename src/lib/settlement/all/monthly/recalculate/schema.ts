import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Recalculate Monthly Settlement a Client",
  description: "Recalculate and save monthly settlement for a client.",
  tags: [tags.settlement],
  body: {
    type: "object",
    additionalProperties: false,
    properties: {
      monthlyCustomerSettlementId: { type: "number" },
    },
    required: ["monthlyCustomerSettlementId"],
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful recalculation and update of monthly settlement.",
      type: "object",
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    403: {
      description: "Monthly settlement is too old and cannot be recalculated.",
      type: "object",
      properties: {
        message: { type: "string" },
      },
      required: ["message"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    404: {
      description: "Monthly settlement not found.",
      type: "object",
      properties: {
        message: { type: "string" },
      },
      required: ["message"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Internal server error during recalculation.",
      type: "object",
      properties: {
        error: {
          type: "object",
          properties: {
            message: { type: "string" },
          },
          required: ["message"],
        },
      },
      required: ["error"],
    },
  },
};
