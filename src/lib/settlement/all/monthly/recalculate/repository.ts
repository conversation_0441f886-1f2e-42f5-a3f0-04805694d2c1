import { type PrismaClient } from "@prisma/client";

const getMonthlySettlementById = async (
  prisma: PrismaClient,
  monthlyCustomerSettlementId: number
): Promise<{
  customerCustomerTypeId: number;
  month: number;
  year: number;
  status: "SUCCESS" | "ERROR" | "SKIPPED";
}> => {
  const settlement = await prisma.monthlyCustomerSettlement.findUnique({
    where: {
      monthlyCustomerSettlementId,
    },
    select: {
      customerCustomerTypeId: true,
      month: true,
      year: true,
      status: true,
    },
  });

  return settlement as {
    customerCustomerTypeId: number;
    month: number;
    year: number;
    status: "SUCCESS" | "ERROR" | "SKIPPED";
  };
};

export { getMonthlySettlementById };
