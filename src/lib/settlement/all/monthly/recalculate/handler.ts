import { environment } from "@constants/environment";
import { getCustomers } from "@lib/customer/repository/get-customer";

import { getMonthlySettlementById } from "./repository";
import { type CalculationOptions } from "../../../../../types/settlement";
import { calculateSettlements } from "../../../functions/calculate-settlements";
import { updateMonthlyCustomerSettlement } from "../../../repository/settlement-monthly";

import type { FastifyReply, FastifyRequest } from "fastify";

type RequestBody = {
  monthlyCustomerSettlementId: number;
};

export const handler = async (
  request: FastifyRequest<{ Body: RequestBody }>,
  reply: FastifyReply
) => {
  const { monthlyCustomerSettlementId } = request.body;

  try {
    const record = await getMonthlySettlementById(
      reply.server.prisma,
      monthlyCustomerSettlementId
    );

    if (!record || Object.entries(record).length === 0) {
      return await reply.code(404).send({
        message: "Monthly settlement not found",
      });
    }

    const recordDate = new Date(record.year, record.month - 1);

    const isTooOld = dateIsOlderThanXMonths(recordDate);

    if (isTooOld) {
      return await reply.code(403).send({
        message:
          "Settlements that are " +
          environment.monthsToHistorical +
          " months or older cannot be recalculated.",
      });
    }

    const { customerCustomerTypeId, status } = record;

    // Day 0 of next month gives last day of current month
    const date = new Date(Date.UTC(record.year, record.month, 0));

    const fromDate = { year: record.year, month: record.month, day: 1 };
    const toDate = {
      year: record.year,
      month: record.month,
      day: date.getUTCDate(),
    };

    const options: CalculationOptions = {
      trueUp: {
        rateDeterminingInterval: {
          fromDate,
          toDate,
        },
      },
    };

    const customer = await getCustomers(
      [record.customerCustomerTypeId],
      reply.server.prisma
    );

    const settlement = await calculateSettlements(
      customer,
      { fromDate, toDate },
      reply.server.prisma,
      options
    );

    await updateMonthlyCustomerSettlement(
      monthlyCustomerSettlementId,
      settlement[customerCustomerTypeId]!,
      status,
      reply.server.prisma
    );

    await reply.send({});
  } catch (error) {
    request.log.error(error);

    return reply.code(500).send({
      error: { message: (error as Error).message },
    });
  }
};

// eslint-disable-next-line @typescript-eslint/naming-convention
function dateIsOlderThanXMonths(recordDate: Date) {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const todaysDateXMonthsAgo = new Date();
  todaysDateXMonthsAgo.setMonth(
    todaysDateXMonthsAgo.getMonth() - environment.monthsToHistorical!
  );

  return recordDate < todaysDateXMonthsAgo;
}
