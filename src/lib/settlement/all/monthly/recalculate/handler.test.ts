import { environment } from "@constants/environment";
import * as Customer from "@lib/customer/repository/get-customer";

import { handler } from "./handler";
import * as CalculateSettlements from "../../../functions/calculate-settlements";
import * as UpdateMonthlyCustomerSettlement from "../../../repository/settlement-monthly";

describe("Recalculate Handler", () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  beforeEach(() => {
    const mockDate = new Date(2025, 0, 27); // January 27, 2025
    vi.setSystemTime(mockDate);
  });

  it("Should not recalculate the settlement if it cannot be found", async () => {
    const mockReply = {
      code: vi.fn().mockReturnThis(),
      send: vi.fn(),
      server: {
        prisma: {
          monthlyCustomerSettlement: {
            findUnique: vi.fn().mockResolvedValue(null),
          },
        },
      },
    };

    const mockRequest = { body: { monthlyCustomerSettlementId: 1 } };

    const calculateSettlements = vi
      .spyOn(CalculateSettlements, "calculateSettlements")
      // @ts-expect-error - we are mocking the function
      .mockResolvedValue(null);
    const updateMonthlyCustomerSettlement = vi
      .spyOn(UpdateMonthlyCustomerSettlement, "updateMonthlyCustomerSettlement")
      // @ts-expect-error - we are mocking the function
      .mockResolvedValue(null);

    // @ts-expect-error - we are mocking the request and reply object
    await handler(mockRequest, mockReply);

    expect(mockReply.code).toHaveBeenCalledWith(404);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: "Monthly settlement not found",
    });

    expect(calculateSettlements).not.toHaveBeenCalled();
    expect(updateMonthlyCustomerSettlement).not.toHaveBeenCalled();
  });

  it("Should not recalculate the settlement if it is older than environemnt MONTHS_TO_HISTORICAL months from today", async () => {
    // Change current time to test using vi set system time
    // also test that if fails we do indeed check that the rec-ord date is greater than 15 months
    // in this case

    const mockMonthlyCustomerSettlement = {
      customerCustomerTypeId: 23,
      month: 10, // October 2023
      year: 2023,
    };

    const mockReply = {
      code: vi.fn().mockReturnThis(),
      send: vi.fn(),
      server: {
        prisma: {
          monthlyCustomerSettlement: {
            findUnique: vi
              .fn()
              .mockResolvedValue(mockMonthlyCustomerSettlement),
          },
        },
      },
    };

    const mockRequest = { body: { monthlyCustomerSettlementId: 1 } };

    const calculateSettlements = vi
      .spyOn(CalculateSettlements, "calculateSettlements")
      // @ts-expect-error - we are mocking the function
      .mockResolvedValue(null);

    const updateMonthlyCustomerSettlement = vi
      .spyOn(UpdateMonthlyCustomerSettlement, "updateMonthlyCustomerSettlement")
      // @ts-expect-error - we are mocking the function
      .mockResolvedValue(null);

    // @ts-expect-error - we are mocking the request and reply object
    await handler(mockRequest, mockReply);

    expect(mockReply.code).toHaveBeenCalledWith(403);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: `Settlements that are ${environment.monthsToHistorical} months or older cannot be recalculated.`,
    });

    // Add 15 months to October 2023
    const newDate = new Date(2023, 9 + environment.monthsToHistorical!, 1);
    expect(newDate < new Date()).toBe(true);

    expect(calculateSettlements).not.toHaveBeenCalled();
    expect(updateMonthlyCustomerSettlement).not.toHaveBeenCalled();
  });

  it("Should recalculate the settlement if it is found and not older than environment MONTHS_TO_HISTORICAL months", async () => {
    const mockMonthlyCustomerSettlement = {
      customerCustomerTypeId: 23,
      month: 10, // October 2024
      year: 2024,
      status: "SUCCESS",
    };

    const mockPrisma = {
      monthlyCustomerSettlement: {
        findUnique: vi.fn().mockResolvedValue(mockMonthlyCustomerSettlement),
        findMany: vi
          .fn()
          .mockResolvedValue([{ customerId: 23, customerCustomerTypeId: 23 }]),
      },
    };

    const mockReply = {
      code: vi.fn().mockReturnThis(),
      send: vi.fn(),
      server: {
        prisma: mockPrisma,
      },
    };

    const mockRequest = { body: { monthlyCustomerSettlementId: 1 } };

    const mockSettlement = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      23: {
        status: "SUCCESS",
        adjustmentTotal: 0,
        meta: {
          message: "Settlement recalculated",
          timeTakenInSeconds: 0,
        },
      },
    };

    const calculateSettlements = vi
      .spyOn(CalculateSettlements, "calculateSettlements")
      // @ts-expect-error - we are mocking the function
      .mockResolvedValue(mockSettlement);
    const updateMonthlyCustomerSettlement = vi
      .spyOn(UpdateMonthlyCustomerSettlement, "updateMonthlyCustomerSettlement")
      // @ts-expect-error - we are mocking the function
      .mockResolvedValue(null);
    const getCustomers = vi
      .spyOn(Customer, "getCustomers")
      // @ts-expect-error - we are mocking the function
      .mockResolvedValue([{ customerId: 23, customerCustomerTypeId: 23 }]);

    // @ts-expect-error - we are mocking the request and reply object
    await handler(mockRequest, mockReply);

    expect(mockReply.code).not.toHaveBeenCalledWith(404);
    expect(mockReply.send).toHaveBeenCalledWith({});

    // Add 15 months to October 2024
    const newDate = new Date(2024, 9 + environment.monthsToHistorical!, 1);
    expect(newDate > new Date()).toBe(true);

    expect(calculateSettlements).toHaveBeenCalledWith(
      [{ customerId: 23, customerCustomerTypeId: 23 }],
      {
        fromDate: {
          day: 1,
          month: 10,
          year: 2024,
        },
        toDate: {
          day: 31,
          month: 10,
          year: 2024,
        },
      },
      mockPrisma,
      {
        trueUp: {
          rateDeterminingInterval: {
            fromDate: {
              day: 1,
              month: 10,
              year: 2024,
            },
            toDate: {
              day: 31,
              month: 10,
              year: 2024,
            },
          },
        },
      }
    );
    expect(updateMonthlyCustomerSettlement).toHaveBeenCalledWith(
      1,
      mockSettlement[23],
      "SUCCESS",
      mockPrisma
    );
    expect(getCustomers).toHaveBeenCalledWith([23], mockPrisma);
  });

  it("Should return a 500 error if an error occurs during recalculation", async () => {
    const mockMonthlyCustomerSettlement = {
      customerCustomerTypeId: 23,
      month: 10, // October 2024
      year: 2024,
    };

    const mockPrisma = {
      monthlyCustomerSettlement: {
        findUnique: vi.fn().mockResolvedValue(mockMonthlyCustomerSettlement),
      },
    };

    const mockReply = {
      code: vi.fn().mockReturnThis(),
      send: vi.fn(),
      server: {
        prisma: mockPrisma,
      },
    };

    const mockRequest = {
      body: { monthlyCustomerSettlementId: 1 },
      log: { error: vi.fn() },
    };

    const calculateSettlements = vi
      .spyOn(CalculateSettlements, "calculateSettlements")
      .mockImplementation(() => {
        throw new Error("Test error occurred");
      });
    const updateMonthlyCustomerSettlement = vi
      .spyOn(UpdateMonthlyCustomerSettlement, "updateMonthlyCustomerSettlement")
      // @ts-expect-error - we are mocking the function
      .mockResolvedValue(null);

    // @ts-expect-error - we are mocking the request and reply object
    await handler(mockRequest, mockReply);

    expect(mockRequest.log.error).toHaveBeenCalledWith(
      new Error("Test error occurred")
    );
    expect(mockReply.code).toHaveBeenCalledWith(500);
    expect(mockReply.send).toHaveBeenCalledWith({
      error: {
        message: "Test error occurred",
      },
    });

    // Add 15 months to October 2024
    const newDate = new Date(2024, 9 + environment.monthsToHistorical!, 1);
    expect(newDate > new Date()).toBe(true);

    expect(calculateSettlements).toThrowError("Test error occurred");
    expect(updateMonthlyCustomerSettlement).not.toHaveBeenCalled();
  });
});
