import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Delete Monthly Settlements history by IDs",
  description: "Delete monthly settlement history",
  tags: [tags.settlement],
  body: {
    type: "object",
    additionalProperties: false,
    properties: {
      jobIds: { type: "array" },
      items: { type: "number" },
    },
    required: ["jobIds"],
  },
};
