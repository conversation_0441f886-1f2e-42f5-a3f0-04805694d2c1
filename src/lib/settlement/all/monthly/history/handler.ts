import { getAllMonthlySettlementJob } from "../../../repository/settlement-monthly-job";

import type { FastifyReply, FastifyRequest } from "fastify";

type Query = {
  offset: number;
  limit: number;
};

export const handler = async (
  request: FastifyRequest<{ Querystring: Query }>,
  reply: FastifyReply
) => {
  const { offset, limit } = request.query;

  try {
    const jobs = await getAllMonthlySettlementJob(
      request.server.prisma,
      offset,
      limit
    );

    return await reply.send(jobs);
  } catch (error) {
    request.log.error(error);

    return reply.code(500).send({
      message: "Error fetching monthly settlement jobs, please try again.",
      error: { message: (error as Error).message },
    });
  }
};
