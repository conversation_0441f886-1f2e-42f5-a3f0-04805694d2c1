import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Monthly Settlement Creation for all Clients",
  description: "Create and save monthly settlements for all clients.",
  tags: [tags.settlement],
  body: {
    type: "object",
    additionalProperties: false,
    properties: {
      month: { type: "number" },
      year: { type: "number" },
    },
    required: ["month", "year"],
  },
};
