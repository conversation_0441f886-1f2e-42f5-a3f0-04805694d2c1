/* eslint-disable @typescript-eslint/naming-convention */
import { type Prisma, type PrismaClient } from "@prisma/client";

type MonthlySettlementFilter = {
  customerName?: string;
  serviceNumber?: string;
  month?: number;
  year?: number;
  status?: "SUCCESS" | "ERROR" | "SKIPPED";
};

const getMonthlySettlements = async (
  prisma: PrismaClient,
  offset = 0,
  limit = 20,
  filter?: MonthlySettlementFilter
) => {
  const where = buildWhereClause(filter);

  const [monthlySettlements, totalCount] = await prisma.$transaction(
    async (tx) => {
      const settlements = await tx.monthlyCustomerSettlement.findMany({
        where,
        skip: offset,
        take: limit,
        orderBy: [{ year: "desc" }, { month: "desc" }],
        include: {
          customerCustomerType: {
            include: {
              customer: {
                select: {
                  customerName: true,
                  serviceNumber: true,
                },
              },
            },
          },
        },
      });

      const count = await tx.monthlyCustomerSettlement.count({
        where,
      });

      return [settlements, count];
    }
  );

  // Flatten the result
  const flattenedResult = monthlySettlements.map((settlement) => {
    let message: string | undefined;
    let timeTakenInSeconds: number | undefined;

    // Ensure meta is an object and extract 'message' and 'timeTakenInSeconds' if they exist
    if (typeof settlement.meta === "object" && settlement.meta !== null) {
      if (
        "message" in settlement.meta &&
        typeof settlement.meta["message"] === "string"
      ) {
        message = settlement.meta["message"];
      }

      if (
        "timeTakenInSeconds" in settlement.meta &&
        typeof settlement.meta["timeTakenInSeconds"] === "number"
      ) {
        timeTakenInSeconds = settlement.meta["timeTakenInSeconds"];
      }
    }

    return {
      id: settlement.monthlyCustomerSettlementId,
      customerName: settlement.customerCustomerType?.customer?.customerName,
      serviceNumber: settlement.customerCustomerType?.customer?.serviceNumber,
      month: settlement.month,
      year: settlement.year,
      status: settlement.status,
      updatedAt: settlement.updatedAt,
      adjustmentTotal: settlement?.adjustmentTotal?.toNumber(),
      timeTakenInSeconds,
      message,
    };
  });

  return {
    monthlySettlements: flattenedResult,
    totalCount,
  };
};

const buildWhereClause = (
  filter?: MonthlySettlementFilter
): Prisma.monthlyCustomerSettlementWhereInput => {
  const where: Prisma.monthlyCustomerSettlementWhereInput = {
    deletedAt: null,
  };

  // Add filters for status, month, and year if provided
  if (filter?.status) {
    where.status = filter.status;
  }

  if (filter?.month) {
    where.month = filter.month;
  }

  if (filter?.year) {
    where.year = filter.year;
  }

  // Only add the OR condition if customerName or serviceNumber exists
  if (filter?.customerName ?? filter?.serviceNumber) {
    const orConditions = [];

    if (filter?.customerName) {
      orConditions.push({
        customerName: {
          contains: filter.customerName,
        },
      });
    }

    if (filter?.serviceNumber) {
      orConditions.push({
        serviceNumber: {
          contains: filter.serviceNumber,
        },
      });
    }

    if (orConditions.length > 0) {
      where.customerCustomerType = {
        customer: {
          OR: orConditions,
        },
      };
    }
  }

  return where;
};

export { getMonthlySettlements, type MonthlySettlementFilter };
