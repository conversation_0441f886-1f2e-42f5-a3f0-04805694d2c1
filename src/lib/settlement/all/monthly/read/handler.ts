import {
  getMonthlySettlements,
  type MonthlySettlementFilter,
} from "./repository";

import type { FastifyReply, FastifyRequest } from "fastify";

type RequestBody = {
  offset: number;
  limit: number;
  customerName?: string;
  serviceNumber?: string;
  month?: number;
  year?: number;
  status?: "SUCCESS" | "ERROR" | "SKIPPED";
};

export const handler = async (
  request: FastifyRequest<{ Body: RequestBody }>,
  reply: FastifyReply
) => {
  const { offset, limit, month, year, status, customerName, serviceNumber } =
    request.body;

  const filter = {
    ...(month ? { month } : {}),
    ...(year ? { year } : {}),
    ...(status ? { status } : {}),
    ...(customerName ? { customerName } : {}),
    ...(serviceNumber ? { serviceNumber } : {}),
  } satisfies MonthlySettlementFilter;

  try {
    const result = await getMonthlySettlements(
      request.server.prisma,
      offset,
      limit,
      filter
    );

    return await reply.send(result);
  } catch (error) {
    request.log.error(error);
    await reply.code(500).send({
      error: { message: (error as Error).message },
    });
  }
};
