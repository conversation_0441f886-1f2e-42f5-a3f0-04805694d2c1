import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Get Monthly Settlements for all Clients",
  description: "Get monthly settlement for all clients.",
  tags: [tags.settlement],
  body: {
    type: "object",
    additionalProperties: false,
    properties: {
      offset: {
        type: "number",
        description: "The starting point for pagination.",
      },
      limit: {
        type: "number",
        description: "The number of records to retrieve.",
      },
      month: {
        type: "number",
        minimum: 1,
        maximum: 12,
        description: "The month for filtering settlements (1-12).",
      },
      year: {
        type: "number",
        minimum: 1900,
        maximum: 2100,
        description: "The year for filtering settlements.",
      },
      status: {
        type: "string",
        enum: ["SUCCESS", "ERROR", "SKIPPED"],
        description: "The status of settlements to filter by.",
      },
      customerName: {
        type: "string",
        description: "The name of the customer.",
      },
      serviceNumber: {
        type: "string",
        description: "The service number associated with the customer.",
      },
    },
    required: [], // All fields are optional
  },
};
