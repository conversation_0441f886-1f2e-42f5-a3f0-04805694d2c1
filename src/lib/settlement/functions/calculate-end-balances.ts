/* eslint-disable no-await-in-loop */

import {
  payInGroup,
  payOutGroup,
  type Platform,
} from "@constants/transactions/platform";
import { getCustomerIdByCustomerCustomerType } from "@lib/customer-customer-type/repository";
import { getWirePayments } from "@lib/customer-wire-in-outs/repository";
import { type PrismaClient } from "@prisma/client";
import { subDaysOnly, type DateOnly } from "@utils/date-only";

import { type SettlementResult } from "./workers/types";
import { getFrequencyPlatformSettlement } from "../repository/settlement-frequency";
import { type FrequencySettlementsResult } from "../repository/types";

const calculateEndBalances = async (
  settlements: FrequencySettlementsResult,
  fromDate: DateOnly,
  toDate: DateOnly,
  prisma: PrismaClient
): Promise<FrequencySettlementsResult> => {
  const settlementsResult: FrequencySettlementsResult = { ...settlements };

  for (const [customerCustomerTypeId, settlement] of Object.entries(
    settlements
  )) {
    const merchantSettlement = settlement.data?.result;

    if (!merchantSettlement) {
      continue;
    }

    const customerCustomerType = await getCustomerIdByCustomerCustomerType(
      Number(customerCustomerTypeId),
      prisma
    );

    if (!customerCustomerType?.customerId) {
      throw new Error(
        `Customer not found for customerCustomerTypeId ${customerCustomerTypeId}`
      );
    }

    // Get end balance from the previous settlement summary
    const previousSettlement = await getFrequencyPlatformSettlement(
      customerCustomerType?.customerId,
      1,
      subDaysOnly(fromDate, 1),
      prisma
    );

    const currentSettlementBalance = await _calculateSettlementBalance(
      settlement.generationType,
      merchantSettlement
    );

    const totalWireAmount = await _calculateTotalWireAmount(
      customerCustomerType?.customerId,
      fromDate,
      toDate,
      prisma
    );

    settlementsResult[Number(customerCustomerTypeId)]!.data!.endBalance =
      (previousSettlement?.endBalance ?? 0) +
      currentSettlementBalance +
      totalWireAmount;
  }

  return settlementsResult;
};

// eslint-disable-next-line no-warning-comments
// TODO: Refactor duplicate end balance calculation logic
// https://gigadatsolutions.atlassian.net/browse/FA-1005
const _calculateSettlementBalance = async (
  generationType: "INITIAL" | "FINAL",
  settlementResult: SettlementResult
): Promise<number> => {
  let balance = 0;

  if (generationType === "INITIAL") {
    return balance;
  }

  for (const [platform, platformSettlement] of Object.entries(
    settlementResult.settlement
  )) {
    if (!platformSettlement.feesBreakdown) {
      throw new Error(
        `Fees breakdown is missing for ${platform} platform settlement`
      );
    }

    const totalFee = Object.values(platformSettlement.totalChargedFees).reduce(
      (accumulator, fee) => accumulator + fee
    );
    const { totalTransactionAmount, totalFailedAmount } =
      platformSettlement.totalTransactionSummary;

    if (payInGroup.includes(platform as Platform)) {
      balance += totalTransactionAmount - totalFee;
    } else if (payOutGroup.includes(platform as Platform)) {
      balance -= totalTransactionAmount + totalFee - totalFailedAmount;
    }
  }

  return balance;
};

const _calculateTotalWireAmount = async (
  customerId: number,
  fromDate: DateOnly,
  toDate: DateOnly,
  prisma: PrismaClient
) => {
  let totalWireAmount = 0;

  const wires = await getWirePayments(customerId, fromDate, toDate, prisma);

  for (const wire of wires) {
    if (wire.transactionType === "I") {
      totalWireAmount += wire.transactionAmount;
    } else {
      totalWireAmount -= wire.transactionAmount;
    }
  }

  return totalWireAmount;
};

export { calculateEndBalances };
