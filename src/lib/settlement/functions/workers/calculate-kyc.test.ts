import { getTierItemForCount } from "@lib/settlement/repository/tier-item";
import { describe, it, expect, beforeEach, vi } from "vitest";

import { calculateKycRecords } from "./calculate-kyc";
import { getKycTransactions } from "../../../../services/blusky/transactions";

import type { TierItem } from "@lib/settlement/repository/tier-item";
import type { PrismaClient } from "@prisma/client";

const getTierItemForCountMock = vi.mocked(getTierItemForCount, true);
const getKycTransactionsMock = vi.mocked(getKycTransactions, true);

vi.mock("@utils/date-only", () => ({
  fromDateOnly: (date: { year: number; month: number; day: number }) =>
    new Date(date.year, date.month - 1, date.day),
}));

vi.mock("@lib/settlement/repository/tier-item");
vi.mock("../../../../services/blusky/transactions");

const createFakePrisma = (): PrismaClient => {
  return {
    customer: { findFirst: vi.fn() },
    merchantPlatform: { findFirst: vi.fn() },
    merchantPlatformKyc: { findMany: vi.fn() },
    kycType: { findMany: vi.fn() },
    customerCustomerTypeServiceNumber: { findMany: vi.fn() },
  } as unknown as PrismaClient;
};

describe("calculateKycRecords", () => {
  let prisma: PrismaClient;
  const token = "fake-token";

  const merchantConfig = {
    customerCustomerTypeId: 1,
    customerId: 1,
    customerName: "Test Merchant",
    customerTradingName: "Test Trading Merchant",
    serviceNumber: "12345",
    isCombineMultipleServices: false,
    isCombineMultipleServicesKyc: false,
    customerType: {
      id: 1,
      isCombineIdpTierSet: false,
      isCombineAchTierSet: false,
      isCombineRtoTierSet: false,
      statementFolderLocation: "folder",
      statementFrequencyId: 1,
      statementFrequencyName: "Monthly",
      statementFrequencyCode: "MONTHLY",
      customerTypeName: "Merchant",
      serviceNumbers: [
        {
          serviceNumber: "12345",
          fromDate: { year: 2025, month: 1, day: 1 },
          toDate: { year: 2025, month: 1, day: 31 },
        },
      ],
      volumeCombination: [],
    },
    platformConfigurations: {},
    firstTransactionDate: { year: 2025, month: 1, day: 1 },
  };

  const interval = {
    fromDate: { year: 2025, month: 2, day: 1 },
    toDate: { year: 2025, month: 2, day: 30 },
  };

  beforeEach(() => {
    prisma = createFakePrisma();

    prisma.customer.findFirst = vi.fn().mockResolvedValue({
      customerId: 1,
      serviceNumber: "12345",
      multipleServiceNoTierSetKyc: false,
      customerCustomerType: [{ customerCustomerTypeId: 1 }],
    });

    prisma.merchantPlatform.findFirst = vi.fn().mockResolvedValue({
      merchantPlatformId: 10,
    });

    prisma.merchantPlatformKyc.findMany = vi
      .fn()
      .mockResolvedValue([
        { kycTypeId: 10, tierSetId: 100, kycType: { kycName: "KYC1" } },
      ]);

    prisma.kycType.findMany = vi
      .fn()
      .mockResolvedValue([{ kycTypeId: 10, kycName: "KYC1" }]);

    prisma.customerCustomerTypeServiceNumber.findMany = vi
      .fn()
      .mockResolvedValue([{ serviceNumber: "12345" }]);

    getKycTransactionsMock.mockResolvedValue([
      {
        serviceNumber: "12345",
        name: "Test",
        email: "<EMAIL>",
        type: "KYC1",
        clientUserId: "user1",
        status: "SUCCESS",
        dispCreated: "2025-06-15",
        dispUpdated: "2025-06-15",
      },
    ]);

    getTierItemForCountMock.mockResolvedValue({
      tierItemId: 200,
      fee: 0,
      transactionFee: 2,
    });
  });

  it("should calculate kyc records correctly", async () => {
    const records = await calculateKycRecords(
      merchantConfig,
      interval,
      token,
      prisma
    );
    expect(records).toHaveLength(1);
    expect(records[0]).toEqual({
      kycTypeId: 10,
      tierItemId: 200,
      transactionCount: 1,
      totalTransactionAmount: 2,
    });
  });

  it("should throw an error if no tier is found", async () => {
    getTierItemForCountMock.mockReturnValueOnce(
      Promise.resolve(undefined as unknown as TierItem)
    );
    await expect(
      calculateKycRecords(merchantConfig, interval, token, prisma)
    ).rejects.toThrow("Tierset for KYC not configured.");
  });

  it("should throw an error if a transaction type has no corresponding KYC type", async () => {
    getKycTransactionsMock.mockResolvedValueOnce([
      {
        serviceNumber: "12345",
        name: "Test",
        email: "<EMAIL>",
        type: "UNKNOWN_TYPE",
        clientUserId: "user1",
        status: "SUCCESS",
        dispCreated: "2025-06-15",
        dispUpdated: "2025-06-15",
      },
    ]);
    await expect(
      calculateKycRecords(merchantConfig, interval, token, prisma)
    ).rejects.toThrow('Missing KYC Type for transaction type "UNKNOWN_TYPE"');
  });

  it("should throw an error if merchant platform is not configured", async () => {
    prisma.merchantPlatform.findFirst = vi.fn().mockResolvedValue(null);
    await expect(
      calculateKycRecords(merchantConfig, interval, token, prisma)
    ).rejects.toThrow(
      `KYC merchant platform is not configured for type "KYC1"`
    );
  });

  it("should throw an error if customer record is not found", async () => {
    prisma.customer.findFirst = vi.fn().mockResolvedValue(null);
    await expect(
      calculateKycRecords(merchantConfig, interval, token, prisma)
    ).rejects.toThrow("Customer with ID 1 not found");
  });
});
