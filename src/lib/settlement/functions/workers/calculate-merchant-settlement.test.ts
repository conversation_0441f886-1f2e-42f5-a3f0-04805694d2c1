import { type RatesWithMeta } from "@lib/settlement/functions/helpers/rates";
import { describe, it, expect } from "vitest";

import { minimumFeeEligibilityCheck } from "./calculate-merchant-settlement";
import { type Transaction } from "../../../../services/blusky/transactions";

describe("minimumFeeEligibilityCheck", () => {
  const txn = { finalAmt: 30 };

  it("returns true when minimumThreshold > 0 and minimumThreshold > finalAmt", () => {
    const ratesWithMeta = {
      rates: {
        minimumThreshold: 50,
        salesFee: 0.0135,
        transactionFee: 0.55,
        minimumCharge: 1,
      },
      meta: {},
    };

    expect(
      minimumFeeEligibilityCheck(
        txn as Transaction,
        ratesWithMeta as RatesWithMeta
      )
    ).toBe(true);
  });

  it("returns false when minimumThreshold > 0 but minimumThreshold <= finalAmt", () => {
    const ratesWithMeta = {
      rates: {
        minimumThreshold: 30,
        salesFee: 0.0135,
        transactionFee: 0.55,
        minimumCharge: 1,
      },
      meta: {},
    };

    expect(
      minimumFeeEligibilityCheck(
        txn as Transaction,
        ratesWithMeta as RatesWithMeta
      )
    ).toBe(false);
  });

  it("falls back to minimumCharge when minimumThreshold is 0", () => {
    const ratesWithMeta = {
      rates: {
        minimumThreshold: 0,
        salesFee: 0.0135,
        transactionFee: 0.55,
        minimumCharge: 0.5,
      },
      meta: {},
    };

    expect(
      minimumFeeEligibilityCheck(
        txn as Transaction,
        ratesWithMeta as RatesWithMeta
      )
    ).toBe(false);
  });

  it("falls back to minimumCharge when minimumThreshold is null", () => {
    const ratesWithMeta = {
      rates: {
        minimumThreshold: null,
        salesFee: 0.0135,
        transactionFee: 0.55,
        minimumCharge: 1,
      },
      meta: {},
    };

    expect(
      minimumFeeEligibilityCheck(
        txn as Transaction,
        ratesWithMeta as unknown as RatesWithMeta
      )
    ).toBe(true);
  });
});
