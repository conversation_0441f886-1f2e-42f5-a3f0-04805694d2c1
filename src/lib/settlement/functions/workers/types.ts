import { type ChargedFeeWithMeta } from "./calculate-merchant-settlement";
import { type CommissionWithBreakdown } from "./calculate-non-merchant-settlement";
import { type ChargedFees } from "../helpers/fees";
import { type TransactionsSummary } from "../helpers/transactions-summary";

type MerchantFeesBreakdown = ChargedFeeWithMeta[];
type NonMerchantFeesBreakdown = CommissionWithBreakdown;

type PlatformSettlementDetail = {
  totalChargedFees: ChargedFees;
  totalTransactionSummary: TransactionsSummary;
  platformId: number;
  platformDescription: string;
  platformDisplaySequence: number;
  isContractChange: boolean;
  feesBreakdown?: MerchantFeesBreakdown | NonMerchantFeesBreakdown;
};

type Settlement = Record<
  string,
  PlatformSettlementDetail & { trueUp?: PlatformSettlementDetail }
>;

type KycRecord = {
  kycTypeId: number;
  tierItemId: number;
  transactionCount: number;
  totalTransactionAmount: number;
};

type SettlementResult = {
  settlement: Settlement;
  kyc?: KycRecord[] | undefined;
};

type KycPlatform = {
  kycTypeId: number;
  tierSetId: number;
  kycName: string;
};

type KycCustomer = {
  serviceNumber: string;
  multipleServiceNoTierSetKyc: true;
  customerCustomerType: Array<{
    customerCustomerTypeId: number;
  }>;
};

type KycType = {
  kycTypeId: number;
  kycName: string;
};

export {
  type PlatformSettlementDetail,
  type SettlementResult,
  type NonMerchantFeesBreakdown,
  type MerchantFeesBreakdown,
  type KycPlatform,
  type KycCustomer,
  type KycType,
  type KycRecord,
};
