import { kycClientPlatformId } from "@constants/settlement";
import {
  type Kyc<PERSON>ustomer,
  type KycPlatform,
  type KycRecord,
  type KycType,
} from "@lib/settlement/functions/workers/types";
import { getTierItemForCount } from "@lib/settlement/repository/tier-item";
import { type DateOnly, fromDateOnly } from "@utils/date-only";

import {
  getKycTransactions,
  type KycTxn,
} from "../../../../services/blusky/transactions";
import { type DeterminedInterval } from "../../../../types/settlement";

import type { MerchantSettlementConfiguration } from "@lib/settlement/repository/types";
import type { PrismaClient } from "@prisma/client";

export const calculateKycRecords = async (
  config: MerchantSettlementConfiguration,
  { fromDate, toDate }: DeterminedInterval,
  token: string,
  prisma: PrismaClient
): Promise<KycRecord[]> => {
  const [customer, kycPlatforms, kycTypes, primaryKycTransactions] =
    await Promise.all([
      getCustomer(prisma, config.customerId),
      getMerchantPlatformKyc(prisma, config.customerId, fromDate),
      getKycTypesMap(prisma),
      getKycTransactions(config.serviceNumber, fromDate, toDate, token),
    ]);

  const primaryKycCounts = buildKycCountMap(
    primaryKycTransactions,
    kycTypes,
    kycPlatforms
  );

  const overallKycCounts = await getKycCountsForMerchantServiceNumbers({
    prisma,
    customer,
    fromDate,
    toDate,
    token,
  });

  return Promise.all(
    kycPlatforms.map(async (platform) => {
      const overallCount = overallKycCounts[platform.kycName] ?? 0;
      const primaryCount = primaryKycCounts[platform.kycName] ?? 0;
      const tier = await getTierItemForCount(
        prisma,
        platform.tierSetId,
        overallCount
      );

      if (!tier) {
        throw new Error("Tierset for KYC not configured.");
      }

      return {
        kycTypeId: platform.kycTypeId,
        tierItemId: tier.tierItemId,
        transactionCount: primaryCount,
        totalTransactionAmount: tier.transactionFee * primaryCount,
      };
    })
  );
};

const getCustomer = async (
  prisma: PrismaClient,
  customerId: number
): Promise<KycCustomer> => {
  const customer = await prisma.customer.findFirst({
    where: { customerId },
    include: {
      customerCustomerType: { select: { customerCustomerTypeId: true } },
    },
  });

  if (!customer) {
    throw new Error(`Customer with ID ${customerId} not found`);
  }

  return customer as KycCustomer;
};

/** Returns active KYC merchant platform configurations. */
async function getMerchantPlatformKyc(
  prisma: PrismaClient,
  customerId: number,
  fromDate: DateOnly
): Promise<KycPlatform[]> {
  const merchantPlatform = await prisma.merchantPlatform.findFirst({
    where: {
      clientCustomerId: customerId,
      clientPlatformId: kycClientPlatformId,
    },
    select: { merchantPlatformId: true },
  });

  if (!merchantPlatform) {
    return [];
  }

  const result = await prisma.merchantPlatformKyc.findMany({
    where: {
      merchantPlatformId: merchantPlatform.merchantPlatformId,
      fromDate: { lte: fromDateOnly(fromDate) },
      // eslint-disable-next-line @typescript-eslint/naming-convention
      OR: [{ toDate: null }, { toDate: { gt: fromDateOnly(fromDate) } }],
    },
    include: {
      kycType: { select: { kycName: true } },
    },
  });

  return result.map((item) => ({
    kycTypeId: item.kycTypeId,
    tierSetId: item.tierSetId,
    kycName: item.kycType.kycName!,
  }));
}

const buildKycCountMap = (
  transactions: KycTxn[],
  kycTypes: Record<string, KycType>,
  kycPlatforms: KycPlatform[]
): Record<string, number> => {
  const countMap: Record<string, number> = {};

  for (const txn of transactions) {
    const txnType = txn.type;
    const kycType = kycTypes[txnType];

    if (!kycType) {
      throw new Error(`Missing KYC Type for transaction type "${txnType}"`);
    }

    const platform = kycPlatforms.find(
      ({ kycTypeId }) => kycTypeId === kycType.kycTypeId
    );

    if (!platform) {
      throw new Error(
        `KYC merchant platform is not configured for type "${txnType}"`
      );
    }

    countMap[txnType] = (countMap[txnType] ?? 0) + 1;
  }

  return countMap;
};

/** Returns unique service numbers for the customer. */
const getAllServiceNumbers = async (
  prisma: PrismaClient,
  customer: KycCustomer,
  fromDate: DateOnly
): Promise<string[]> => {
  if (customer.multipleServiceNoTierSetKyc) {
    const relatedServiceNumbers = await getRelatedServiceNumbers(
      prisma,
      customer,
      fromDate
    );

    return [...new Set([customer.serviceNumber, ...relatedServiceNumbers])];
  }

  return [customer.serviceNumber];
};

const getKycCountsForMerchantServiceNumbers = async ({
  prisma,
  customer,
  fromDate,
  toDate,
  token,
}: {
  prisma: PrismaClient;
  customer: KycCustomer;
  fromDate: DateOnly;
  toDate: DateOnly;
  token: string;
}): Promise<Record<string, number>> => {
  /**
   * A mapping from KYC transaction “type” ---> total number of occurrences
   * across all the merchant’s service numbers
   */
  const overallCountsByTxnType: Record<string, number> = {};

  const serviceNumbers = await getAllServiceNumbers(prisma, customer, fromDate);

  for (const serviceNumber of serviceNumbers) {
    // eslint-disable-next-line no-await-in-loop
    const kycTransactions = await getKycTransactions(
      serviceNumber,
      fromDate,
      toDate,
      token
    );

    for (const txn of kycTransactions) {
      incrementCount(overallCountsByTxnType, txn.type);
    }
  }

  return overallCountsByTxnType;
};

/**
 * Increment the counter for the `type` (starting from 0 if absent).
 */
function incrementCount(counts: Record<string, number>, type: string): void {
  counts[type] = (counts[type] ?? 0) + 1;
}

const getKycTypesMap = async (
  prisma: PrismaClient
): Promise<Record<string, KycType>> => {
  const kycTypesArray = (await prisma.kycType.findMany()) as KycType[];

  return kycTypesArray?.reduce(
    (accumulator: Record<string, KycType>, current) => {
      accumulator[current.kycName] = current;

      return accumulator;
    },
    {}
  );
};

const getRelatedServiceNumbers = async (
  prisma: PrismaClient,
  customer: KycCustomer,
  fromDate: DateOnly
): Promise<string[]> => {
  if (!customer || customer.customerCustomerType.length === 0) {
    throw new Error("No customer type found for the selected customer.");
  }

  const customerTypeId =
    customer.customerCustomerType[0]!.customerCustomerTypeId;

  const serviceNumberResponse =
    await prisma.customerCustomerTypeServiceNumber.findMany({
      select: { serviceNumber: true },
      where: {
        customerCustomerTypeId: customerTypeId,
        fromDate: { lte: fromDateOnly(fromDate) },
        // eslint-disable-next-line @typescript-eslint/naming-convention
        OR: [{ toDate: null }, { toDate: { gt: fromDateOnly(fromDate) } }],
        deletedAt: null,
      },
    });

  return serviceNumberResponse
    .map(({ serviceNumber }) => serviceNumber)
    .filter(Boolean) as string[];
};
