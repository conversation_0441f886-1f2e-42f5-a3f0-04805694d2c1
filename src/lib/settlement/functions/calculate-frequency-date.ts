import { toDateOnlyFromString, type DateOnly } from "../../../utils/date-only";

const calculateFrequencyDates = async (
  frequencyCode: string
): Promise<{ fromDate: DateOnly; toDate: DateOnly }> => {
  let fromDate = "";
  let toDate = "";

  // Hard coded date ranges for now.
  switch (frequencyCode) {
    case "M": {
      fromDate = "2025-01-01";
      toDate = "2025-01-31";
      break;
    }

    case "SM": {
      fromDate = "2025-01-16";
      toDate = "2025-01-31";
      break;
    }

    case "TaW": {
      fromDate = "2025-01-03";
      toDate = "2025-01-06";
      break;
    }

    case "WM": {
      fromDate = "2025-01-06";
      toDate = "2025-01-12";
      break;
    }

    case "WF": {
      fromDate = "2025-01-04";
      toDate = "2025-01-10";
      break;
    }

    default: {
      throw new Error(`Unknown frequency code: ${frequencyCode}`);
    }
  }

  return {
    fromDate: toDateOnlyFromString(fromDate),
    toDate: toDateOnlyFromString(toDate),
  };
};

export { calculateFrequencyDates };
