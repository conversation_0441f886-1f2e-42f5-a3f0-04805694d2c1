/* eslint-disable @typescript-eslint/naming-convention */

import { getCustomerIdByCustomerCustomerType } from "@lib/customer-customer-type/repository";
import { getWirePayments } from "@lib/customer-wire-in-outs/repository";
import { PrismaClient } from "@prisma/client";

import { calculateEndBalances } from "./calculate-end-balances";
import { subDaysOnly } from "../../../utils/date-only";
import { getFrequencyPlatformSettlement } from "../repository/settlement-frequency";
import { type FrequencySettlementsResult } from "../repository/types";

vi.mock("@lib/customer-customer-type/repository", () => ({
  getCustomerIdByCustomerCustomerType: vi.fn(),
}));

vi.mock("../repository/settlement-frequency", () => ({
  getFrequencyPlatformSettlement: vi.fn(),
}));

vi.mock("@lib/customer-wire-in-outs/repository", () => ({
  getWirePayments: vi.fn(),
}));

vi.mock("../../../utils/date-only", () => ({
  subDaysOnly: vi.fn(),
}));

vi.mock("@prisma/client", () => {
  const mockPrismaClient = {};

  return { PrismaClient: vi.fn(() => mockPrismaClient) };
});

const prisma = new PrismaClient();

const customerId = 11;
const customerTypeId = 11;
const scheduleId = 42;
const customerCustomerTypeId = 12;
const platformIds = { ETI: 2, RFM: 3, RTO: 13 };

const fromDate = { year: 2025, month: 1, day: 16 };
const toDate = { year: 2025, month: 1, day: 31 };

const fees = {
  ETI: {
    gatewayFeeTotal: 0,
    transactionFeeTotal: 3071.75,
    rejected1FeeTotal: 0,
    salesFeeTotal: 10_845.267,
    refundFeeTotal: 0,
    minimumFeeTotal: 0,
    partialReturnFeeTotal: 0,
  },
  RFM: {
    gatewayFeeTotal: 0,
    transactionFeeTotal: 2156.55,
    rejected1FeeTotal: 0,
    salesFeeTotal: 7580.34,
    refundFeeTotal: 0,
    minimumFeeTotal: 0,
    partialReturnFeeTotal: 0,
  },
  RTO: {
    gatewayFeeTotal: 0,
    transactionFeeTotal: 3537,
    rejected1FeeTotal: 618.3,
    salesFeeTotal: 0,
    refundFeeTotal: 0,
    minimumFeeTotal: 0,
    partialReturnFeeTotal: 0,
  },
};

const transactionSummaries = {
  ETI: {
    transactionCount: 5585,
    totalTransactionAmount: 409_255.34,
    refundCount: 0,
    totalRefundAmount: 0,
    totalFailedAmount: 0,
    failedCount: 0,
    total_RAmount: 0,
    _RCount: 0,
    rejected1Count: 0,
    totalRejected1Amount: 0,
    minimumAmountCount: 0,
    totalMinimumAmount: 0,
    partialReturnCount: 0,
    totalPartialReturnAmount: 0,
  },
  RFM: {
    transactionCount: 3921,
    totalTransactionAmount: 286_050.57,
    refundCount: 0,
    totalRefundAmount: 0,
    totalFailedAmount: 0,
    failedCount: 0,
    total_RAmount: 0,
    _RCount: 0,
    rejected1Count: 0,
    totalRejected1Amount: 0,
    minimumAmountCount: 0,
    totalMinimumAmount: 0,
    partialReturnCount: 0,
    totalPartialReturnAmount: 0,
  },
  RTO: {
    transactionCount: 1310,
    totalTransactionAmount: 478_769.25,
    refundCount: 0,
    totalRefundAmount: 0,
    totalFailedAmount: 3421.24,
    failedCount: 11,
    total_RAmount: 12_600.83,
    _RCount: 222,
    rejected1Count: 229,
    totalRejected1Amount: 12_995.83,
    minimumAmountCount: 0,
    totalMinimumAmount: 0,
    partialReturnCount: 0,
    totalPartialReturnAmount: 0,
  },
};

const mockSettlementsResult: FrequencySettlementsResult = {
  [customerCustomerTypeId]: {
    status: "SUCCESS" as const,
    generationType: "INITIAL",
    generationStatus: "COMPLETE",
    scheduleId,
    data: {
      result: {
        settlement: {
          ETI: {
            platformId: platformIds.ETI,
            platformDescription: "Interac e-Transfer Pay-In (ETI)",
            platformDisplaySequence: 1,
            totalChargedFees: {
              ...fees.ETI,
            },
            totalTransactionSummary: {
              ...transactionSummaries.ETI,
            },
            isContractChange: false,
            feesBreakdown: [
              {
                meta: {
                  transactionSummary: {
                    ...transactionSummaries.ETI,
                  },
                  rates: {
                    transactionFee: 0.55,
                    reject1Fee: 0.55,
                    salesFee: 0.0265,
                    gatewayFee: 0,
                    refundFee: 6.95,
                    minimumThreshold: 0,
                    minimumCharge: 0,
                    isMinimumFeeApplicable: false,
                    transactionTierItem: {
                      id: 1371,
                      maxAmount: 2_000_000,
                      minAmount: 500_000,
                      transactionFee: 0.55,
                      salesFee: 0.0265,
                    },
                    rejectOneTierItem: {
                      id: 1371,
                      maxAmount: 2_000_000,
                      minAmount: 500_000,
                      transactionFee: 0.55,
                      salesFee: 0.0265,
                    },
                    delayMonths: {
                      isDelayApplicable: false,
                      delayInMonths: 0,
                      tierItem: undefined,
                    },
                  },
                  rateDeterminingProfile: {
                    fromDate: {
                      year: 2024,
                      month: 12,
                      day: 1,
                    },
                    toDate: {
                      year: 2024,
                      month: 12,
                      day: 31,
                    },
                    combineTotal: 1_423_666.55,
                    isCombineIdpTierSet: true,
                    isCombineAchTierSet: false,
                    isCombineRtoTierSet: true,
                    serviceNumbers: [],
                    isCombineMultipleServicesEnabled: false,
                  },
                },
                chargedFees: {
                  ...fees.ETI,
                },
                interval: {
                  fromDate: {
                    year: 2025,
                    month: 1,
                    day: 16,
                  },
                  toDate: {
                    year: 2025,
                    month: 1,
                    day: 31,
                  },
                },
              },
            ],
          },
          RFM: {
            platformId: platformIds.RFM,
            platformDescription: "RFM Platform",
            platformDisplaySequence: 2,
            totalChargedFees: {
              ...fees.RFM,
            },
            totalTransactionSummary: {
              ...transactionSummaries.RFM,
            },
            isContractChange: false,
            feesBreakdown: [
              {
                meta: {
                  transactionSummary: {
                    ...transactionSummaries.RFM,
                  },
                  rates: {
                    transactionFee: 0.55,
                    reject1Fee: 0.55,
                    salesFee: 0.0265,
                    gatewayFee: 0,
                    refundFee: 6.95,
                    minimumThreshold: 0,
                    minimumCharge: 0,
                    isMinimumFeeApplicable: false,
                    transactionTierItem: {
                      id: 1371,
                      maxAmount: 2_000_000,
                      minAmount: 500_000,
                      transactionFee: 0.55,
                      salesFee: 0.0265,
                    },
                    rejectOneTierItem: {
                      id: 1371,
                      maxAmount: 2_000_000,
                      minAmount: 500_000,
                      transactionFee: 0.55,
                      salesFee: 0.0265,
                    },
                    delayMonths: {
                      isDelayApplicable: false,
                      delayInMonths: 0,
                      tierItem: undefined,
                    },
                  },
                  rateDeterminingProfile: {
                    fromDate: {
                      year: 2024,
                      month: 12,
                      day: 1,
                    },
                    toDate: {
                      year: 2024,
                      month: 12,
                      day: 31,
                    },
                    combineTotal: 1_423_666.55,
                    isCombineIdpTierSet: true,
                    isCombineAchTierSet: false,
                    isCombineRtoTierSet: true,
                    serviceNumbers: [],
                    isCombineMultipleServicesEnabled: false,
                  },
                },
                chargedFees: {
                  ...fees.RFM,
                },
                interval: {
                  fromDate: {
                    year: 2025,
                    month: 1,
                    day: 16,
                  },
                  toDate: {
                    year: 2025,
                    month: 1,
                    day: 31,
                  },
                },
              },
            ],
          },
          RTO: {
            platformId: platformIds.RTO,
            platformDescription: "RTO Platform",
            platformDisplaySequence: 3,
            totalChargedFees: {
              ...fees.RTO,
            },
            totalTransactionSummary: {
              ...transactionSummaries.RTO,
            },
            isContractChange: false,
            feesBreakdown: [
              {
                meta: {
                  transactionSummary: {
                    ...transactionSummaries.RTO,
                  },
                  rates: {
                    transactionFee: 2.7,
                    reject1Fee: 2.7,
                    salesFee: 0,
                    gatewayFee: 0,
                    refundFee: 0,
                    minimumThreshold: 0,
                    minimumCharge: 0,
                    isMinimumFeeApplicable: false,
                    transactionTierItem: {
                      id: 1276,
                      maxAmount: 1_500_000,
                      minAmount: 750_000,
                      transactionFee: 2.7,
                      salesFee: 0,
                    },
                    rejectOneTierItem: {
                      id: 1276,
                      maxAmount: 1_500_000,
                      minAmount: 750_000,
                      transactionFee: 2.7,
                      salesFee: 0,
                    },
                    delayMonths: {
                      isDelayApplicable: false,
                      delayInMonths: 0,
                      tierItem: undefined,
                    },
                  },
                  rateDeterminingProfile: {
                    fromDate: {
                      year: 2024,
                      month: 12,
                      day: 1,
                    },
                    toDate: {
                      year: 2024,
                      month: 12,
                      day: 31,
                    },
                    combineTotal: 910_852.41,
                    isCombineIdpTierSet: true,
                    isCombineAchTierSet: false,
                    isCombineRtoTierSet: true,
                    serviceNumbers: [],
                    isCombineMultipleServicesEnabled: false,
                  },
                },
                chargedFees: {
                  ...fees.RTO,
                },
                interval: {
                  fromDate: {
                    year: 2025,
                    month: 1,
                    day: 16,
                  },
                  toDate: {
                    year: 2025,
                    month: 1,
                    day: 31,
                  },
                },
              },
            ],
          },
        },
      },
      timeTakenInSeconds: 24,
    },
  },
};

describe("calculateEndBalances", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should skip calculating end balance if settlement was not success", async () => {
    const mockSettlementsResultWithoutSuccess: FrequencySettlementsResult = {
      12: {
        status: "SKIPPED" as const,
        generationType: "INITIAL",
        generationStatus: "SKIPPED",
        scheduleId,
        message: "No transactions found",
      },
      13: {
        status: "ERROR" as const,
        generationType: "INITIAL",
        generationStatus: "ERROR",
        scheduleId,
        message: "Body is unusable: Body has already been read",
      },
    };

    const result = await calculateEndBalances(
      mockSettlementsResultWithoutSuccess,
      fromDate,
      toDate,
      prisma
    );

    expect(result).toEqual(mockSettlementsResultWithoutSuccess);
  });

  it("should throw an error if customer is not found", async () => {
    await expect(
      calculateEndBalances(mockSettlementsResult, fromDate, toDate, prisma)
    ).rejects.toThrow(
      `Customer not found for customerCustomerTypeId ${customerCustomerTypeId}`
    );
  });

  it("should calculate end balances correctly if generation type is INITIAL", async () => {
    vi.mocked(getCustomerIdByCustomerCustomerType).mockResolvedValue({
      customerId,
      customerTypeId,
    });
    vi.mocked(getFrequencyPlatformSettlement).mockResolvedValue({
      settlementId: 1,
      endBalance: 500,
    });
    vi.mocked(getWirePayments).mockResolvedValue([
      { id: 4, transactionType: "I", transactionAmount: 200 },
      { id: 5, transactionType: "O", transactionAmount: 50 },
    ]);
    vi.mocked(subDaysOnly).mockReturnValue({
      year: 2025,
      month: 1,
      day: 15,
    });

    const result = await calculateEndBalances(
      mockSettlementsResult,
      fromDate,
      toDate,
      prisma
    );

    expect(subDaysOnly).toHaveBeenCalledWith(fromDate, 1);
    expect(result[customerCustomerTypeId]?.data?.endBalance).toBe(650);
  });

  it("should calculate end balances correctly if generation type is FINAL", async () => {
    vi.mocked(getCustomerIdByCustomerCustomerType).mockResolvedValue({
      customerId,
      customerTypeId,
    });
    vi.mocked(getFrequencyPlatformSettlement).mockResolvedValue({
      settlementId: 1,
      endBalance: 500,
    });
    vi.mocked(getWirePayments).mockResolvedValue([
      { id: 4, transactionType: "I", transactionAmount: 200 },
      { id: 5, transactionType: "O", transactionAmount: 50 },
    ]);
    vi.mocked(subDaysOnly).mockReturnValue({
      year: 2025,
      month: 1,
      day: 15,
    });

    const mockSettlementsResultFinal = {
      [customerCustomerTypeId]: {
        ...mockSettlementsResult[customerCustomerTypeId]!,
        generationType: "FINAL" as const,
      },
    };

    const result = await calculateEndBalances(
      mockSettlementsResultFinal,
      fromDate,
      toDate,
      prisma
    );

    let totalFees = 0;

    for (const platform of Object.keys(fees)) {
      for (const fee of Object.values(fees[platform as keyof typeof fees])) {
        totalFees += fee;
      }
    }

    const expectedEndBalance =
      650 -
      totalFees +
      transactionSummaries.ETI.totalTransactionAmount +
      transactionSummaries.RFM.totalTransactionAmount -
      transactionSummaries.RTO.totalTransactionAmount +
      transactionSummaries.RTO.totalFailedAmount;

    expect(result[customerCustomerTypeId]?.data?.endBalance?.toFixed(2)).toBe(
      expectedEndBalance.toFixed(2)
    );
  });

  it("should calculate end balances correctly without previous settlement", async () => {
    vi.mocked(getCustomerIdByCustomerCustomerType).mockResolvedValue({
      customerId,
      customerTypeId,
    });
    vi.mocked(getWirePayments).mockResolvedValue([
      { id: 4, transactionType: "I", transactionAmount: 200 },
      { id: 5, transactionType: "O", transactionAmount: 50 },
    ]);
    vi.mocked(subDaysOnly).mockReturnValue({
      year: 2025,
      month: 1,
      day: 15,
    });

    const result = await calculateEndBalances(
      mockSettlementsResult,
      fromDate,
      toDate,
      prisma
    );

    expect(result[customerCustomerTypeId]?.data?.endBalance).toBe(150);
  });

  it("should calculate end balances correctly without previous settlement and no wires", async () => {
    vi.mocked(getCustomerIdByCustomerCustomerType).mockResolvedValue({
      customerId,
      customerTypeId,
    });
    vi.mocked(getWirePayments).mockResolvedValue([]);
    vi.mocked(subDaysOnly).mockReturnValue({
      year: 2025,
      month: 1,
      day: 15,
    });

    const mockSettlementsResultFinal = {
      [customerCustomerTypeId]: {
        ...mockSettlementsResult[customerCustomerTypeId]!,
        generationType: "FINAL" as const,
      },
    };

    const result = await calculateEndBalances(
      mockSettlementsResultFinal,
      fromDate,
      toDate,
      prisma
    );

    let totalFees = 0;

    for (const platform of Object.keys(fees)) {
      for (const fee of Object.values(fees[platform as keyof typeof fees])) {
        totalFees += fee;
      }
    }

    const expectedEndBalance =
      -totalFees +
      transactionSummaries.ETI.totalTransactionAmount +
      transactionSummaries.RFM.totalTransactionAmount -
      transactionSummaries.RTO.totalTransactionAmount +
      transactionSummaries.RTO.totalFailedAmount;

    expect(result[customerCustomerTypeId]?.data?.endBalance?.toFixed(2)).toBe(
      expectedEndBalance.toFixed(2)
    );
  });
});
