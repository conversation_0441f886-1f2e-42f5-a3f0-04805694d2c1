import { format } from "date-fns";

import {
  type DateOnly,
  isDateBeforeOrEqual,
  isDayBefore,
  isSameDay,
  fromDateOnly,
  toDateOnly,
} from "../../../../utils/date-only";

/**
 * Validates an array of intervals to ensure that:
 * 1. No `fromDate` is undefined.
 * 2. At most one `toDate` is undefined or null.
 *
 * @param {Array<{ fromDate: DateOnly | undefined; toDate: DateOnly | undefined }>} intervals -
 * The array of intervals to validate. Each interval contains a `fromDate` and a `toDate`.
 *
 * @returns {boolean} - Returns `true` if the intervals are valid; otherwise, returns `false`.
 */
const areIntervalsValid = (
  intervals: Array<{
    fromDate: DateOnly | undefined;
    toDate: DateOnly | undefined;
  }>
): boolean => {
  let undefinedToDateCount = 0;

  for (const interval of intervals) {
    if (!interval?.fromDate) {
      return false;
    }

    if (!interval?.toDate) {
      undefinedToDateCount++;

      if (undefinedToDateCount > 1) {
        return false;
      }
    }
  }

  return true;
};

const areIntervalsContinuous = (
  intervals: Array<{ fromDate: DateOnly; toDate: DateOnly | undefined }>
): boolean => {
  if (intervals.length === 1) {
    const interval = intervals[0];

    return (
      !interval?.toDate ||
      isDateBeforeOrEqual(interval.fromDate, interval.toDate)
    );
  }

  const sortedIntervals = intervals
    .map(({ fromDate, toDate }) => ({
      from: fromDateOnly(fromDate),
      to: toDate ? fromDateOnly(toDate) : null,
    }))
    .sort((a, b) => a.from.getTime() - b.from.getTime());

  for (let index = 0; index < sortedIntervals.length - 1; index++) {
    const current = sortedIntervals[index];
    const next = sortedIntervals[index + 1];

    if (!current?.to || !next?.from) {
      return false;
    }

    if (isSameDay(toDateOnly(current.from), toDateOnly(next.from))) {
      return false;
    }

    const currentToDate = current.to;
    const nextFromDate = next.from;

    const isContinuous = isDayBefore(
      toDateOnly(currentToDate),
      toDateOnly(nextFromDate)
    );

    if (!isContinuous) {
      return false;
    }
  }

  return true;
};

/**
 * Determines whether a customer is considered new based on the comparison of their
 * 'firstTxnEverDate'  with the reference 'startDate' which is generally start of a
 * settlement period. A customer is considered new if their first transaction occurred
 * within the same month as the 'startDate'.
 *
 * @param {Date} firstTxnEverDate - The date of the customer's first transaction.
 * @param {Date} startDate - The reference date for determining customer status.
 * @returns {boolean} - True if the customer is new, false otherwise.
 */
function isCustomerNew(firstTxnEverDate: Date, startDate: Date): boolean {
  const firstTxnMonth = format(new Date(firstTxnEverDate), "yyyy-MM");
  const referenceMonth = format(new Date(startDate), "yyyy-MM");

  return firstTxnMonth === referenceMonth;
}

export { areIntervalsContinuous, isCustomerNew, areIntervalsValid };
