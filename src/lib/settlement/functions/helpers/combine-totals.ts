import {
  idpGroup,
  platforms,
  rtoGroup,
  achGroup,
  type Platform,
} from "../../../../constants/transactions/platform";
import { getTransactionsTotalAmount } from "../../../../services/blusky/transactions";
import { type DateOnly } from "../../../../utils/date-only";

type Options = {
  legacy: {
    isCombineIdpTierSet: boolean;
    isCombineRtoTierSet: boolean;
    isCombineAchTierSet: boolean;
  };
  payInCombinations: Record<Platform, boolean> | Record<string, unknown>;
  payOutCombinations: Record<Platform, boolean> | Record<string, unknown>;
};

const combineTotals = async (
  serviceNumbers: string[],
  interval: { fromDate: DateOnly; toDate: DateOnly },
  token: string,
  options: Options
): Promise<Record<string, number>> => {
  const { fromDate, toDate } = interval;
  const transactionTotals = await getTransactionsTotalAmount(
    serviceNumbers,
    fromDate,
    toDate,
    token
  );
  // Initialize the total object with all platforms set to 0, then merge with transactionTotals
  let total: Record<Platform, number> = {
    ...Object.fromEntries(platforms.map((platform) => [platform, 0])),
    ...transactionTotals,
  };

  if (
    Object.keys(options.payInCombinations).length > 0 &&
    Object.keys(options.payOutCombinations).length > 0
  ) {
    // Combining all pay ins
    const combinedPayInMembers: Platform[] = [];

    for (const platform of Object.keys(options.payInCombinations)) {
      if (
        (options.payInCombinations as Record<Platform, boolean>)[
          platform as Platform
        ]
      ) {
        combinedPayInMembers.push(platform as Platform);
      }
    }

    const combinedPayInTotal = combinedPayInMembers.reduce(
      (accumulator, key) => accumulator + total[key],
      0
    );

    for (const member of combinedPayInMembers) {
      total[member] = combinedPayInTotal;
    }

    // Combining all payouts
    const combinedPayOutMembers: Platform[] = [];

    for (const platform of Object.keys(options.payOutCombinations)) {
      if (
        (options.payOutCombinations as Record<Platform, boolean>)[
          platform as Platform
        ]
      ) {
        combinedPayOutMembers.push(platform as Platform);
      }
    }

    const combinedPayOutTotal = combinedPayOutMembers.reduce(
      (accumulator, key) => accumulator + total[key],
      0
    );

    for (const member of combinedPayOutMembers) {
      total[member] = combinedPayOutTotal;
    }
  } else {
    total = legacyVolumeCombination(options, total);
  }

  return total;
};

function legacyVolumeCombination(
  options: Options,
  total: Record<Platform, number>
) {
  const idpCoreMembers = idpGroup.coreMembers ?? [];

  const idpCoreMembersTotal = idpCoreMembers.reduce(
    (accumulator, key) => accumulator + total[key],
    0
  );

  for (const member of idpCoreMembers) {
    total[member] = idpCoreMembersTotal;
  }

  if (options.legacy.isCombineIdpTierSet) {
    const nonCoreMembers = idpGroup.members.filter(
      (member) => !idpCoreMembers.includes(member)
    );
    const nonCoreMembersTotal = nonCoreMembers.reduce(
      (accumulator, key) => accumulator + total[key],
      0
    );
    const allMembersTotal = nonCoreMembersTotal + idpCoreMembersTotal;

    for (const member of idpGroup.members) {
      total[member] = allMembersTotal;
    }
  }

  if (options.legacy.isCombineRtoTierSet) {
    const rtoMembersTotal = rtoGroup.members.reduce(
      (accumulator, key) => accumulator + total[key],
      0
    );

    for (const member of rtoGroup.members) {
      total[member] = rtoMembersTotal;
    }
  }

  if (options.legacy.isCombineAchTierSet) {
    const achMembersTotal = achGroup.members.reduce(
      (accumulator, key) => accumulator + total[key],
      0
    );

    for (const member of achGroup.members) {
      total[member] = achMembersTotal;
    }
  }

  return total;
}

export { combineTotals, type Options };
