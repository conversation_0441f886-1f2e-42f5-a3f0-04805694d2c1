import { getFirstTransactionEver as getFirstTransactionEverFromBlusky } from "../../../../services/blusky/transactions";
import { type DateOnly, fromDateOnly } from "../../../../utils/date-only";

import type { MerchantSettlementConfiguration } from "@lib/settlement/repository/types";
import type { PrismaClient } from "@prisma/client";

export const getFirstTransactionEverDate = async (
  prisma: PrismaClient,
  merchantConfig: MerchantSettlementConfiguration,
  token: string
): Promise<DateOnly> => {
  let firstTransactionEverDate: DateOnly | undefined =
    merchantConfig.firstTransactionDate;

  if (!firstTransactionEverDate) {
    firstTransactionEverDate = await getFirstTransactionEverFromBlusky(
      merchantConfig.serviceNumber,
      token
    );

    if (firstTransactionEverDate) {
      await prisma.customer.update({
        where: { customerId: merchantConfig.customerId },
        data: { firstTransactionDate: fromDateOnly(firstTransactionEverDate) },
      });
    }
  }

  return firstTransactionEverDate!;
};
