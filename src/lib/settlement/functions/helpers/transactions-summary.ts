/* eslint-disable @typescript-eslint/naming-convention */

import { type RatesWithMeta } from "./rates";
import {
  type Transaction,
  transactionStatus,
} from "../../../../services/blusky/transactions";
import { isDateInRange } from "../../../../utils/date";
import { type DateOnly, fromDateOnly } from "../../../../utils/date-only";
import { roundToDecimalPlaces } from "../../../../utils/math";

type TransactionsSummary = {
  transactionCount: number;
  totalTransactionAmount: number;
  totalFailedAmount: number;
  failedCount: number;
  refundCount: number;
  totalRefundAmount: number;
  total_RAmount: number;
  _RCount: number;
  rejected1Count: number;
  totalRejected1Amount: number;
  minimumAmountCount: number;
  totalMinimumAmount: number;
  partialReturnCount: number;
  totalPartialReturnAmount: number;
};

type MinimumFeeEligibilityCheckFunction = (
  record: Transaction,
  ratesWithMeta: RatesWithMeta
) => boolean;

const calculatePayOutTransactionsSummary = (
  interval: { fromDate: DateOnly; toDate: DateOnly },
  transactions: Transaction[]
): TransactionsSummary => {
  let transactionCount = 0;
  let totalTransactionAmount = 0;
  let totalFailedAmount = 0;
  let failedCount = 0;
  let total_RAmount = 0;
  let _RCount = 0;
  let rejected1Count = 0;
  let totalRejected1Amount = 0;
  let partialReturnCount = 0;
  let totalPartialReturnAmount = 0;

  const fromDate = fromDateOnly(interval.fromDate);
  const toDate = fromDateOnly(interval.toDate);

  for (const record of transactions) {
    if (!record?.finalAmt) {
      continue;
    }

    // Remove the noise of _R from the outgoing transaction metrics
    if (record.transactionID.endsWith("_R")) {
      if (isDateInRange(new Date(record.createdDate), fromDate, toDate)) {
        _RCount++;
        total_RAmount += record.finalAmt;
      }
      // Remove the noise of _P from the outgoing transaction metrics
    } else if (record.transactionID.endsWith("_P")) {
      if (isDateInRange(new Date(record.createdDate), fromDate, toDate)) {
        partialReturnCount++;
        totalPartialReturnAmount += record.finalAmt;
      }
    } else {
      // Remove the noise of rejected1 from the outgoing transaction metrics
      if (record.status === transactionStatus.rejected1) {
        if (isDateInRange(new Date(record.updatedDate), fromDate, toDate)) {
          rejected1Count++;
          totalRejected1Amount += record.finalAmt;
        }
      } else if (
        isDateInRange(new Date(record.createdDate), fromDate, toDate)
      ) {
        transactionCount++;
        totalTransactionAmount += Number(record.finalAmt);
      }

      if (
        isDateInRange(new Date(record.updatedDate), fromDate, toDate) &&
        record.status === transactionStatus.failed
      ) {
        totalFailedAmount += Number(record.finalAmt);
        failedCount++;
      }
    }
  }

  return {
    transactionCount,
    totalTransactionAmount: roundToDecimalPlaces(totalTransactionAmount),
    failedCount,
    totalFailedAmount: roundToDecimalPlaces(totalFailedAmount),
    refundCount: 0,
    totalRefundAmount: 0,
    _RCount,
    total_RAmount: roundToDecimalPlaces(total_RAmount),
    rejected1Count,
    totalRejected1Amount: roundToDecimalPlaces(totalRejected1Amount),
    minimumAmountCount: 0,
    totalMinimumAmount: 0,
    partialReturnCount,
    totalPartialReturnAmount: roundToDecimalPlaces(totalPartialReturnAmount),
  } satisfies TransactionsSummary;
};

const calculatePayInTransactionsSummary = (
  interval: { fromDate: DateOnly; toDate: DateOnly },
  transactions: Transaction[],
  ratesWithMeta: RatesWithMeta,
  minimumFeeEligibilityCheck?: MinimumFeeEligibilityCheckFunction
): TransactionsSummary => {
  let transactionCount = 0;
  let totalTransactionAmount = 0;

  let refundCount = 0;
  let totalRefundAmount = 0;

  let rejected1Count = 0;
  let totalRejected1Amount = 0;
  let minimumAmountCount = 0;
  let totalminimumAmount = 0;

  const fromDate = fromDateOnly(interval.fromDate);
  const toDate = fromDateOnly(interval.toDate);

  for (const record of transactions) {
    if (isDateInRange(new Date(record.updatedDate), fromDate, toDate)) {
      if (record.status === transactionStatus.rejected1) {
        rejected1Count++;
        totalRejected1Amount += record.finalAmt;
        continue;
      }

      if (record.billable) {
        switch (record.status) {
          case transactionStatus.success: {
            const finalAmount = record.finalAmt;

            // eslint-disable-next-line max-depth
            if (finalAmount === 0) {
              break;
            }

            const isEligibleForMinimum = minimumFeeEligibilityCheck?.(
              record,
              ratesWithMeta
            );

            // eslint-disable-next-line max-depth
            if (isEligibleForMinimum) {
              minimumAmountCount++;
              totalminimumAmount += finalAmount;
            } else {
              transactionCount++;
              totalTransactionAmount += finalAmount;
            }

            break;
          }

          case transactionStatus.refund:
          // Falls through
          case transactionStatus.fRefund:

          // Falls through
          case transactionStatus.refunded: {
            refundCount += 1;
            totalRefundAmount += record.refundAmt;

            break;
          }

          default: {
            // Do nothing
            break;
          }
        }
      }
    }
  }

  return {
    transactionCount,
    totalTransactionAmount: roundToDecimalPlaces(totalTransactionAmount),
    failedCount: 0,
    totalFailedAmount: 0,
    refundCount,
    totalRefundAmount: -roundToDecimalPlaces(totalRefundAmount),
    _RCount: 0,
    total_RAmount: 0,
    rejected1Count,
    totalRejected1Amount: roundToDecimalPlaces(totalRejected1Amount),
    minimumAmountCount,
    totalMinimumAmount: roundToDecimalPlaces(totalminimumAmount),
    partialReturnCount: 0,
    totalPartialReturnAmount: 0,
  } satisfies TransactionsSummary;
};

function sumTransactionSummaries(
  summaries: TransactionsSummary[]
): TransactionsSummary {
  const result: TransactionsSummary = {
    transactionCount: 0,
    totalTransactionAmount: 0,
    totalFailedAmount: 0,
    failedCount: 0,
    refundCount: 0,
    totalRefundAmount: 0,
    total_RAmount: 0,
    _RCount: 0,
    rejected1Count: 0,
    totalRejected1Amount: 0,
    minimumAmountCount: 0,
    totalMinimumAmount: 0,
    partialReturnCount: 0,
    totalPartialReturnAmount: 0,
  };

  for (const summary of summaries) {
    result.transactionCount += summary.transactionCount;
    result.totalTransactionAmount += summary.totalTransactionAmount;
    result.totalFailedAmount += summary.totalFailedAmount;
    result.failedCount += summary.failedCount;
    result.refundCount += summary.refundCount;
    result.totalRefundAmount += summary.totalRefundAmount;
    result.total_RAmount += summary.total_RAmount;
    result._RCount += summary._RCount;
    result.rejected1Count += summary.rejected1Count;
    result.totalRejected1Amount += summary.totalRejected1Amount;
    result.minimumAmountCount += summary.minimumAmountCount;
    result.totalMinimumAmount += summary.totalMinimumAmount;
    result.partialReturnCount += summary.partialReturnCount;
    result.totalPartialReturnAmount += summary.totalPartialReturnAmount;
  }

  return result;
}

export {
  calculatePayOutTransactionsSummary,
  calculatePayInTransactionsSummary,
  sumTransactionSummaries,
  type TransactionsSummary,
  type MinimumFeeEligibilityCheckFunction,
};
