import { addDays, endOfMonth } from "date-fns";

import { validateFrequency } from "./frequency-validator";

type FrequencyCode = "WM" | "WF" | "M" | "SM" | "TaW";

describe("validateFrequency", () => {
  it("should validate Weekly Monday (WM) frequency", () => {
    const fromDate = new Date("2025-01-06"); // Monday
    const toDate = addDays(fromDate, 6); // Sunday
    expect(validateFrequency(fromDate, toDate, "WM")).toBe(true);

    const invalidToDate = addDays(fromDate, 5); // Not a full week
    expect(validateFrequency(fromDate, invalidToDate, "WM")).toBe(false);
  });

  it("should validate Weekly Friday (WF) frequency", () => {
    const fromDate = new Date("2025-01-10"); // Friday
    const toDate = addDays(fromDate, 6); // Thursday
    expect(validateFrequency(fromDate, toDate, "WF")).toBe(true);

    const invalidToDate = addDays(fromDate, 5); // Not a full week
    expect(validateFrequency(fromDate, invalidToDate, "WF")).toBe(false);
  });

  it("should validate Monthly (M) frequency", () => {
    const fromDate = new Date("2025-01-01"); // Start of the month
    const toDate = endOfMonth(fromDate); // End of the month
    expect(validateFrequency(fromDate, toDate, "M")).toBe(true);

    const invalidFromDate = new Date("2025-01-02"); // Not the first day
    expect(validateFrequency(invalidFromDate, toDate, "M")).toBe(false);
  });

  it("should validate Semi-Monthly (SM) frequency", () => {
    const fromDate1 = new Date("2025-01-01"); // First half of the month
    const toDate1 = new Date("2025-01-15");
    expect(validateFrequency(fromDate1, toDate1, "SM")).toBe(true);

    const fromDate2 = new Date("2025-01-16"); // Second half of the month
    const toDate2 = endOfMonth(fromDate2);
    expect(validateFrequency(fromDate2, toDate2, "SM")).toBe(true);

    const invalidFromDate = new Date("2025-01-10"); // Not the 1st or 16th
    expect(validateFrequency(invalidFromDate, toDate1, "SM")).toBe(false);
  });

  it("should validate Twice-a-Week (TaW) frequency", () => {
    const fromDateFriday = new Date("2025-01-10"); // Friday
    const toDateFriday = addDays(fromDateFriday, 3); // Monday
    expect(validateFrequency(fromDateFriday, toDateFriday, "TaW")).toBe(true);

    const fromDateTuesday = new Date("2025-01-07"); // Tuesday
    const toDateTuesday = addDays(fromDateTuesday, 2); // Thursday
    expect(validateFrequency(fromDateTuesday, toDateTuesday, "TaW")).toBe(true);

    const invalidFromDate = new Date("2025-01-08"); // Not Friday or Tuesday
    expect(validateFrequency(invalidFromDate, toDateTuesday, "TaW")).toBe(
      false
    );
  });

  it("should return false for unsupported frequency codes", () => {
    const fromDate = new Date("2025-01-01");
    const toDate = new Date("2025-01-15");
    expect(
      validateFrequency(fromDate, toDate, "INVALID" as FrequencyCode)
    ).toBe(false);
  });
});
