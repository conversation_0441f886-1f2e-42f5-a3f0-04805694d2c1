import {
  isMonday,
  isFriday,
  isTuesday,
  addDays,
  endOfMonth,
  getDate,
  isSameDay,
} from "date-fns";

type FrequencyCode = "WM" | "WF" | "M" | "SM" | "TaW";

export function validateFrequency(
  fromDate: Date,
  toDate: Date,
  frequencyCode: FrequencyCode
): boolean {
  // Normalize dates by creating new Date objects (avoids timezone issues)
  const from = new Date(fromDate);
  from.setHours(0, 0, 0, 0);

  const to = new Date(toDate);
  to.setHours(23, 59, 59, 999);

  if (frequencyCode === "WM") {
    return isMonday(from) && isSameDay(to, addDays(from, 6));
  }

  if (frequencyCode === "WF") {
    return isFriday(from) && isSameDay(to, addDays(from, 6));
  }

  if (frequencyCode === "M") {
    return getDate(from) === 1 && isSameDay(to, endOfMonth(from));
  }

  if (frequencyCode === "SM") {
    const fromDay = getDate(from);

    if (fromDay === 1 || fromDay === 16) {
      const expectedToDate =
        fromDay === 1
          ? new Date(from.getFullYear(), from.getMonth(), 15)
          : endOfMonth(from);

      return isSameDay(to, expectedToDate);
    }

    return false;
  }

  if (frequencyCode === "TaW") {
    return (
      (isFriday(from) && isSameDay(to, addDays(from, 3))) ||
      (isTuesday(from) && isSameDay(to, addDays(from, 2)))
    );
  }

  return false;
}
