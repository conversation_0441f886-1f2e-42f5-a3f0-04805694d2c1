/* eslint-disable @typescript-eslint/naming-convention */
import { type Rates } from "./rates";
import { type TransactionsSummary } from "./transactions-summary";
import { roundToDecimalPlaces } from "../../../../utils/math";

type ChargedFees = {
  gatewayFeeTotal: number;
  salesFeeTotal: number;
  transactionFeeTotal: number;
  refundFeeTotal: number;
  rejected1FeeTotal: number;
  minimumFeeTotal: number;
  partialReturnFeeTotal: number;
};

const calculateFee = (
  transactionSummary: TransactionsSummary,
  feeRates: Rates
): ChargedFees => {
  let gatewayFeeTotal = 0;
  let salesFeeTotal = 0;
  let transactionFeeTotal = 0;
  let refundFeeTotal = 0;
  let rejected1FeeTotal = 0;
  let minimumFeeTotal = 0;
  let partialReturnFeeTotal = 0;

  transactionFeeTotal = roundToDecimalPlaces(
    transactionSummary.transactionCount * feeRates.transactionFee
  );

  gatewayFeeTotal = roundToDecimalPlaces(
    transactionSummary.totalTransactionAmount * feeRates.gatewayFee
  );

  refundFeeTotal = roundToDecimalPlaces(
    transactionSummary.refundCount * feeRates.refundFee
  );

  salesFeeTotal = roundToDecimalPlaces(
    transactionSummary.totalTransactionAmount * feeRates.salesFee
  );

  rejected1FeeTotal = roundToDecimalPlaces(
    transactionSummary.rejected1Count * feeRates.reject1Fee
  );

  minimumFeeTotal = roundToDecimalPlaces(
    transactionSummary.minimumAmountCount * feeRates.minimumCharge
  );

  partialReturnFeeTotal = roundToDecimalPlaces(
    transactionSummary.partialReturnCount * feeRates.transactionFee
  );

  return {
    gatewayFeeTotal,
    salesFeeTotal,
    transactionFeeTotal,
    refundFeeTotal,
    rejected1FeeTotal,
    minimumFeeTotal,
    partialReturnFeeTotal,
  } satisfies ChargedFees;
};

/**
 * Calculates charged fees from a partial transaction summary.
 *
 * This function accepts a partial transaction summary containing only the
 * `transactionCount` and `totalTransactionAmount`. It constructs a complete
 * `TransactionsSummary` by setting default values (zero) for all other fields,
 * then calculates and returns the charged fees using the provided fee rates.
 *
 */
const calculateFeeFromPartial = (
  partialSummary: Pick<
    TransactionsSummary,
    "transactionCount" | "totalTransactionAmount"
  >,
  feeRates: Rates
): ChargedFees => {
  const completeTransactionSummary: TransactionsSummary = {
    transactionCount: partialSummary.transactionCount,
    totalTransactionAmount: partialSummary.totalTransactionAmount,
    totalFailedAmount: 0,
    failedCount: 0,
    refundCount: 0,
    totalRefundAmount: 0,
    total_RAmount: 0,
    _RCount: 0,
    rejected1Count: 0,
    totalRejected1Amount: 0,
    minimumAmountCount: 0,
    totalMinimumAmount: 0,
    partialReturnCount: 0,
    totalPartialReturnAmount: 0,
  };

  return calculateFee(completeTransactionSummary, feeRates);
};

function sumChargedFees(chargedFeesArray: ChargedFees[]): ChargedFees {
  const totalFees: ChargedFees = {
    gatewayFeeTotal: 0,
    salesFeeTotal: 0,
    transactionFeeTotal: 0,
    refundFeeTotal: 0,
    rejected1FeeTotal: 0,
    minimumFeeTotal: 0,
    partialReturnFeeTotal: 0,
  };

  for (const fees of chargedFeesArray) {
    totalFees.gatewayFeeTotal += fees.gatewayFeeTotal;
    totalFees.salesFeeTotal += fees.salesFeeTotal;
    totalFees.transactionFeeTotal += fees.transactionFeeTotal;
    totalFees.refundFeeTotal += fees.refundFeeTotal;
    totalFees.rejected1FeeTotal += fees.rejected1FeeTotal;
    totalFees.minimumFeeTotal += fees.minimumFeeTotal;
    totalFees.partialReturnFeeTotal += fees.partialReturnFeeTotal;
  }

  // Round each total to 2 decimal places
  totalFees.gatewayFeeTotal = roundToDecimalPlaces(totalFees.gatewayFeeTotal);
  totalFees.salesFeeTotal = roundToDecimalPlaces(totalFees.salesFeeTotal);
  totalFees.transactionFeeTotal = roundToDecimalPlaces(
    totalFees.transactionFeeTotal
  );
  totalFees.refundFeeTotal = roundToDecimalPlaces(totalFees.refundFeeTotal);
  totalFees.rejected1FeeTotal = roundToDecimalPlaces(
    totalFees.rejected1FeeTotal
  );
  totalFees.minimumFeeTotal = roundToDecimalPlaces(totalFees.minimumFeeTotal);
  totalFees.partialReturnFeeTotal = roundToDecimalPlaces(
    totalFees.partialReturnFeeTotal
  );

  return totalFees;
}

export {
  calculateFee,
  calculateFeeFromPartial,
  sumChargedFees,
  type ChargedFees,
};
