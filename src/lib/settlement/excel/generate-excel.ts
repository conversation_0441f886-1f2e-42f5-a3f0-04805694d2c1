import { generateNonMerchantExcel } from "./helpers/generate-non-merchant-excel";

import type { SettlementResult } from "../functions/workers/types";
import type { Customer } from "@lib/customer/repository/types";

export const generateExcel = async (
  settlementResult: SettlementResult,
  customer: Customer,
  fileName: string
) => {
  if (customer.customerTypeName === "Merchant") {
    // Generate Excel for Merchant
  } else {
    await generateNonMerchantExcel(settlementResult, customer, fileName);
  }
};
