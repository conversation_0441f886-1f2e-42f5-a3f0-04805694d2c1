/**
 * Validates the name of an Excel worksheet.
 *
 * @param name The name of the worksheet to validate.
 * @throws Will throw an error if the name is empty,
 *         exceeds the maximum length, or contains invalid characters.
 */
export const validateWorksheetName = (name: string): void => {
  if (!name) {
    throw new Error("Worksheet name cannot be empty.");
  }

  // Character limit of Excel worksheets
  const maxLength = 31;

  // Invalid characters in Excel worksheet names: * / : ? [ \ ]
  const invalidChars = /[*/:?[\\\]]/;

  if (name.length > maxLength) {
    throw new Error(
      `Worksheet name '${name}' exceeds maximum length of ${maxLength} characters.`
    );
  }

  if (invalidChars.test(name)) {
    throw new Error(`Worksheet name '${name}' contains invalid characters.`);
  }
};
