import { existsSync, mkdirSync } from "node:fs";
import path from "node:path";

import { baseDirectoryName } from "@constants/filesystem";

import type ExcelJS from "exceljs";

export const writeExcelFile = async (
  workbook: ExcelJS.Workbook,
  folderLocation: string,
  fileName: string
): Promise<void> => {
  const fullFolderPath = path.join(baseDirectoryName, folderLocation);

  if (!existsSync(fullFolderPath)) {
    mkdirSync(fullFolderPath, {
      recursive: true,
    });
  }

  const filePath = path.join(fullFolderPath, `${fileName}.xlsx`);
  await workbook.xlsx.writeFile(filePath);
};
