import { transactionColumns } from "@constants/settlement";
import { type ExcelConfig } from "@lib/settlement/approve/types";
import { isDateInRange } from "@utils/date";

import { type Transaction } from "../../../../../services/blusky/transactions";
import { createTransactionRow } from "../create-transaction-excel-row";

import type ExcelJS from "exceljs";

export const createSuccessAndRefundsTransactionsSheets = (
  workbook: ExcelJS.Workbook,
  config: ExcelConfig,
  platformTransactions?: Transaction[]
): void => {
  const transactionsWorksheet = workbook.addWorksheet("Transactions");
  transactionsWorksheet.columns = transactionColumns;

  const refundsWorksheet = workbook.addWorksheet("Refunds");
  refundsWorksheet.columns = transactionColumns;

  if (!platformTransactions) {
    return;
  }

  let rowNumberTransactions = 2;
  let rowNumberRefunds = 2;

  for (const platformTransaction of platformTransactions) {
    if (
      isDateInRange(
        new Date(platformTransaction.updatedDate),
        config.period.fromDate,
        config.period.toDate
      ) &&
      platformTransaction.status === "STATUS_SUCCESS"
    ) {
      createTransactionRow(
        transactionsWorksheet,
        platformTransaction,
        rowNumberTransactions
      );
      rowNumberTransactions++;
    } else if (
      isDateInRange(
        new Date(platformTransaction.updatedDate),
        config.period.fromDate,
        config.period.toDate
      ) &&
      platformTransaction.status === "STATUS_REFUNDED"
    ) {
      createTransactionRow(
        refundsWorksheet,
        platformTransaction,
        rowNumberRefunds
      );
      rowNumberRefunds++;
    }
  }
};
