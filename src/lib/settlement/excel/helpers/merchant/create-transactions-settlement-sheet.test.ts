import { isDateInRange } from "@utils/date";
import { beforeEach, describe, expect, it, vi } from "vitest";

import { createSuccessAndRefundsTransactionsSheets } from "./create-transactions-settlement-sheet";
import { createTransactionRow } from "../create-transaction-excel-row";

import type { Transaction } from "../../../../../services/blusky/transactions";
import type { ExcelConfig } from "@lib/settlement/approve/types";
import type ExcelJS from "exceljs";

vi.mock("../create-transaction-excel-row", () => ({
  createTransactionRow: vi.fn(),
}));

vi.mock("@constants/settlement", () => ({
  transactionColumns: [
    { header: "Created_Date", key: "Created_Date", width: 25 },
    { header: "Updated_Date", key: "Updated_Date", width: 25 },
    { header: "Service_Number", key: "Service_Number", width: 10 },
  ],
}));

vi.mock("@utils/date", () => ({
  isDateInRange: vi.fn(),
}));

describe("createSuccessAndRefundsTransactionsSheets", () => {
  let mockWorkbook: ExcelJS.Workbook;
  let mockTransactionsWorksheet: ExcelJS.Worksheet;
  let mockRefundsWorksheet: ExcelJS.Worksheet;
  let mockConfig: ExcelConfig;

  beforeEach(() => {
    mockTransactionsWorksheet = {
      columns: undefined,
    } as unknown as ExcelJS.Worksheet;

    mockRefundsWorksheet = {
      columns: undefined,
    } as unknown as ExcelJS.Worksheet;

    mockWorkbook = {
      addWorksheet: vi
        .fn()
        .mockReturnValueOnce(mockTransactionsWorksheet)
        .mockReturnValueOnce(mockRefundsWorksheet),
    } as unknown as ExcelJS.Workbook;

    mockConfig = {
      platformCode: "ETI",
      customerTradingName: "Test Customer",
      period: {
        fromDate: new Date("2024-01-01"),
        toDate: new Date("2024-01-31"),
      },
      fileName: "test-file.xlsx",
      folderPath: "/test/path",
      sheetName: "Test Sheet",
      data: {
        labelName: "Test Label",
        transactionCount: 10,
        totalTransactionAmount: 1000,
        refundCount: 2,
        totalRefundAmount: 200,
        gatewayFee: 50,
        transactionFee: 30,
        salesFee: 20,
        refundFee: 10,
        totalFailedAmount: 0,
        endBalance: 800,
        total2FaRejectAmount: 0,
        total2FaRejectCount: 0,
        txnAmountRTO_R: 0,
        txnCountETI_R1: 0,
        minimumFeeTotal: 0,
        minimumFeeCount: 0,
        totalMinimumAmount: 0,
        partialReturnAmountRTO: 0,
        partialReturnCountRTO: 0,
        isAdjusted: false,
        adjustments: [],
      },
    };
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("should create both Transactions and Refunds worksheets", () => {
    createSuccessAndRefundsTransactionsSheets(mockWorkbook, mockConfig);

    expect(mockWorkbook.addWorksheet).toHaveBeenCalledTimes(2);
    expect(mockWorkbook.addWorksheet).toHaveBeenNthCalledWith(
      1,
      "Transactions"
    );
    expect(mockWorkbook.addWorksheet).toHaveBeenNthCalledWith(2, "Refunds");
  });

  it("should set columns for both worksheets", () => {
    createSuccessAndRefundsTransactionsSheets(mockWorkbook, mockConfig);

    expect(mockTransactionsWorksheet.columns).toEqual([
      { header: "Created_Date", key: "Created_Date", width: 25 },
      { header: "Updated_Date", key: "Updated_Date", width: 25 },
      { header: "Service_Number", key: "Service_Number", width: 10 },
    ]);
    expect(mockRefundsWorksheet.columns).toEqual([
      { header: "Created_Date", key: "Created_Date", width: 25 },
      { header: "Updated_Date", key: "Updated_Date", width: 25 },
      { header: "Service_Number", key: "Service_Number", width: 10 },
    ]);
  });

  it("should return early if no platformTransactions provided", () => {
    createSuccessAndRefundsTransactionsSheets(mockWorkbook, mockConfig);

    expect(mockWorkbook.addWorksheet).toHaveBeenCalledTimes(2);
    expect(createTransactionRow).not.toHaveBeenCalled();
  });

  it("should return early if empty platformTransactions array provided", () => {
    createSuccessAndRefundsTransactionsSheets(mockWorkbook, mockConfig, []);

    expect(mockWorkbook.addWorksheet).toHaveBeenCalledTimes(2);
    expect(createTransactionRow).not.toHaveBeenCalled();
  });

  it("should process successful transactions in date range", () => {
    vi.mocked(isDateInRange).mockReturnValue(true);

    const mockTransactions: Transaction[] = [
      {
        createdDate: "2024-01-15T10:30:00Z",
        updatedDate: "2024-01-15T11:45:00Z",
        serviceNumber: "**********",
        originalAmt: 100.5,
        refundAmt: 0,
        finalAmt: 100.5,
        currency: "CAD",
        country: "CA",
        billable: true,
        platform: "ETI",
        custNumber: "CUST123",
        rcode: 200,
        integratorName: "Test Integrator",
        programName: "Test Program",
        billingName: "Test Billing",
        transactionID: "TXN123456",
        receiptID: "RCP789012",
        interacRef: "INT345678",
        fIName: "Test Bank",
        status: "STATUS_SUCCESS",
      },
      {
        createdDate: "2024-01-16T10:30:00Z",
        updatedDate: "2024-01-16T11:45:00Z",
        serviceNumber: "**********",
        originalAmt: 200.75,
        refundAmt: 0,
        finalAmt: 200.75,
        currency: "CAD",
        country: "CA",
        billable: true,
        platform: "ETI",
        custNumber: "CUST124",
        rcode: 200,
        integratorName: "Test Integrator",
        programName: "Test Program",
        billingName: "Test Billing",
        transactionID: "TXN123457",
        receiptID: "RCP789013",
        interacRef: "INT345679",
        fIName: "Test Bank",
        status: "STATUS_SUCCESS",
      },
    ];

    createSuccessAndRefundsTransactionsSheets(
      mockWorkbook,
      mockConfig,
      mockTransactions
    );

    expect(isDateInRange).toHaveBeenCalledTimes(2);
    expect(isDateInRange).toHaveBeenNthCalledWith(
      1,
      new Date("2024-01-15T11:45:00Z"),
      mockConfig.period.fromDate,
      mockConfig.period.toDate
    );
    expect(isDateInRange).toHaveBeenNthCalledWith(
      2,
      new Date("2024-01-16T11:45:00Z"),
      mockConfig.period.fromDate,
      mockConfig.period.toDate
    );

    expect(createTransactionRow).toHaveBeenCalledTimes(2);
    expect(createTransactionRow).toHaveBeenNthCalledWith(
      1,
      mockTransactionsWorksheet,
      mockTransactions[0],
      2
    );
    expect(createTransactionRow).toHaveBeenNthCalledWith(
      2,
      mockTransactionsWorksheet,
      mockTransactions[1],
      3
    );
  });

  it("should process refunded transactions in date range", () => {
    vi.mocked(isDateInRange).mockReturnValue(true);

    const mockTransactions: Transaction[] = [
      {
        createdDate: "2024-01-15T10:30:00Z",
        updatedDate: "2024-01-15T11:45:00Z",
        serviceNumber: "**********",
        originalAmt: 100.5,
        refundAmt: 100.5,
        finalAmt: 0,
        currency: "CAD",
        country: "CA",
        billable: true,
        platform: "ETI",
        custNumber: "CUST123",
        rcode: 200,
        integratorName: "Test Integrator",
        programName: "Test Program",
        billingName: "Test Billing",
        transactionID: "TXN123456",
        receiptID: "RCP789012",
        interacRef: "INT345678",
        fIName: "Test Bank",
        status: "STATUS_REFUNDED",
      },
    ];

    createSuccessAndRefundsTransactionsSheets(
      mockWorkbook,
      mockConfig,
      mockTransactions
    );

    expect(isDateInRange).toHaveBeenCalledTimes(2); // Called once for each worksheet
    expect(createTransactionRow).toHaveBeenCalledTimes(1);
    expect(createTransactionRow).toHaveBeenCalledWith(
      mockRefundsWorksheet,
      mockTransactions[0],
      2
    );
  });

  it("should skip transactions outside date range", () => {
    vi.mocked(isDateInRange).mockReturnValue(false);

    const mockTransactions: Transaction[] = [
      {
        createdDate: "2023-12-15T10:30:00Z",
        updatedDate: "2023-12-15T11:45:00Z",
        serviceNumber: "**********",
        originalAmt: 100.5,
        refundAmt: 0,
        finalAmt: 100.5,
        currency: "CAD",
        country: "CA",
        billable: true,
        platform: "ETI",
        custNumber: "CUST123",
        rcode: 200,
        integratorName: "Test Integrator",
        programName: "Test Program",
        billingName: "Test Billing",
        transactionID: "TXN123456",
        receiptID: "RCP789012",
        interacRef: "INT345678",
        fIName: "Test Bank",
        status: "STATUS_SUCCESS",
      },
    ];

    createSuccessAndRefundsTransactionsSheets(
      mockWorkbook,
      mockConfig,
      mockTransactions
    );

    expect(isDateInRange).toHaveBeenCalledTimes(2); // Called once for each worksheet
    expect(createTransactionRow).not.toHaveBeenCalled();
  });

  it("should skip transactions with status other than SUCCESS or REFUNDED", () => {
    vi.mocked(isDateInRange).mockReturnValue(true);

    const mockTransactions: Transaction[] = [
      {
        createdDate: "2024-01-15T10:30:00Z",
        updatedDate: "2024-01-15T11:45:00Z",
        serviceNumber: "**********",
        originalAmt: 100.5,
        refundAmt: 0,
        finalAmt: 100.5,
        currency: "CAD",
        country: "CA",
        billable: true,
        platform: "ETI",
        custNumber: "CUST123",
        rcode: 400,
        integratorName: "Test Integrator",
        programName: "Test Program",
        billingName: "Test Billing",
        transactionID: "TXN123456",
        receiptID: "RCP789012",
        interacRef: "INT345678",
        fIName: "Test Bank",
        status: "STATUS_FAILED",
      },
    ];

    createSuccessAndRefundsTransactionsSheets(
      mockWorkbook,
      mockConfig,
      mockTransactions
    );

    expect(isDateInRange).toHaveBeenCalledTimes(2); // Called once for each worksheet
    expect(createTransactionRow).not.toHaveBeenCalled();
  });

  it("should handle mixed transaction types correctly", () => {
    vi.mocked(isDateInRange).mockReturnValue(true);

    const mockTransactions: Transaction[] = [
      {
        createdDate: "2024-01-15T10:30:00Z",
        updatedDate: "2024-01-15T11:45:00Z",
        serviceNumber: "**********",
        originalAmt: 100.5,
        refundAmt: 0,
        finalAmt: 100.5,
        currency: "CAD",
        country: "CA",
        billable: true,
        platform: "ETI",
        custNumber: "CUST123",
        rcode: 200,
        integratorName: "Test Integrator",
        programName: "Test Program",
        billingName: "Test Billing",
        transactionID: "TXN123456",
        receiptID: "RCP789012",
        interacRef: "INT345678",
        fIName: "Test Bank",
        status: "STATUS_SUCCESS",
      },
      {
        createdDate: "2024-01-16T10:30:00Z",
        updatedDate: "2024-01-16T11:45:00Z",
        serviceNumber: "**********",
        originalAmt: 200.75,
        refundAmt: 200.75,
        finalAmt: 0,
        currency: "CAD",
        country: "CA",
        billable: true,
        platform: "ETI",
        custNumber: "CUST124",
        rcode: 200,
        integratorName: "Test Integrator",
        programName: "Test Program",
        billingName: "Test Billing",
        transactionID: "TXN123457",
        receiptID: "RCP789013",
        interacRef: "INT345679",
        fIName: "Test Bank",
        status: "STATUS_REFUNDED",
      },
      {
        createdDate: "2024-01-17T10:30:00Z",
        updatedDate: "2024-01-17T11:45:00Z",
        serviceNumber: "**********",
        originalAmt: 300.25,
        refundAmt: 0,
        finalAmt: 300.25,
        currency: "CAD",
        country: "CA",
        billable: true,
        platform: "ETI",
        custNumber: "CUST125",
        rcode: 400,
        integratorName: "Test Integrator",
        programName: "Test Program",
        billingName: "Test Billing",
        transactionID: "TXN123458",
        receiptID: "RCP789014",
        interacRef: "INT345680",
        fIName: "Test Bank",
        status: "STATUS_FAILED",
      },
    ];

    createSuccessAndRefundsTransactionsSheets(
      mockWorkbook,
      mockConfig,
      mockTransactions
    );

    expect(createTransactionRow).toHaveBeenCalledTimes(2);
    expect(createTransactionRow).toHaveBeenNthCalledWith(
      1,
      mockTransactionsWorksheet,
      mockTransactions[0],
      2
    );
    expect(createTransactionRow).toHaveBeenNthCalledWith(
      2,
      mockRefundsWorksheet,
      mockTransactions[1],
      2
    );
  });
});
