import { readFileSync } from "node:fs";

import { beforeEach, describe, expect, it, vi } from "vitest";

import { createSummarySheet } from "./create-summary-settlement-sheet";

import type { ExcelConfig } from "@lib/settlement/approve/types";
import type ExcelJS from "exceljs";

vi.mock("node:fs", () => ({
  readFileSync: vi.fn(),
}));

vi.mock("@constants/filesystem", () => ({
  baseDirectoryName: "/test/base/directory",
}));

vi.mock("@constants/settlement", () => ({
  excelNumberFormat: '_($* #,##0.00_);_($* (#,##0.00);_($* "-"??_);_(@_)',
  summaryColumns: [
    { header: "A", key: "A", width: 10 },
    { header: "B", key: "B", width: 20 },
    { header: "C", key: "C", width: 30 },
    { header: "D", key: "D", width: 40 },
  ],
}));

vi.mock("date-fns", () => ({
  format: vi.fn().mockImplementation((date: Date, formatString: string) => {
    if (formatString === "MMMM d") {
      return "January 1";
    }

    return "January 1";
  }),
}));

describe("createSummarySheet", () => {
  let mockWorkbook: ExcelJS.Workbook;
  let mockWorksheet: ExcelJS.Worksheet;
  let mockColumn: { numFmt: string | undefined };
  let cellValues: Record<string, unknown>;
  let cellProperties: Record<
    string,
    { alignment?: unknown; font?: unknown; border?: unknown }
  >;
  let mockConfig: ExcelConfig;

  beforeEach(() => {
    cellValues = {};
    cellProperties = {};



    mockColumn = {
      numFmt: undefined,
    };

    mockWorksheet = {
      columns: undefined,
      addImage: vi.fn(),
      mergeCells: vi.fn(),
      getCell: vi
        .fn()
        .mockImplementation((cellReference: string) =>
          createMockCell(`cell-${cellReference}`)
        ),
      getColumn: vi.fn().mockReturnValue(mockColumn),
      getRow: vi.fn().mockImplementation((rowNumber: number) => ({
        getCell: vi
          .fn()
          .mockImplementation((cellNumber: number) =>
            createMockCell(`row-${rowNumber}-cell-${cellNumber}`)
          ),
      })),
    } as unknown as ExcelJS.Worksheet;

    mockWorkbook = {
      addWorksheet: vi.fn().mockReturnValue(mockWorksheet),
      addImage: vi.fn().mockReturnValue("image-id-123"),
    } as unknown as ExcelJS.Workbook;

    mockConfig = {
      platformCode: "ETI",
      customerTradingName: "Test Customer Trading",
      period: {
        fromDate: new Date("2024-01-01"),
        toDate: new Date("2024-01-31"),
      },
      fileName: "test-file.xlsx",
      folderPath: "/test/path",
      sheetName: "Test Sheet",
      data: {
        labelName: "Test Label Name",
        transactionCount: 100,
        totalTransactionAmount: 10_000.5,
        refundCount: 5,
        totalRefundAmount: 500.25,
        gatewayFee: 50,
        transactionFee: 30,
        salesFee: 20,
        refundFee: 2.5,
        totalFailedAmount: 0,
        endBalance: 9000,
        total2FaRejectAmount: 0,
        total2FaRejectCount: 0,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        txnAmountRTO_R: 0,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        txnCountETI_R1: 0,
        minimumFeeTotal: 0,
        minimumFeeCount: 2,
        totalMinimumAmount: 10,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        partialReturnAmountRTO: 0,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        partialReturnCountRTO: 0,
        isAdjusted: false,
        adjustments: [],
      },
    };

    vi.mocked(readFileSync).mockReturnValue(Buffer.from("fake-image-data"));
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("should create a SUMMARY worksheet with correct configuration", () => {
    createSummarySheet(mockWorkbook, mockConfig);

    expect(mockWorkbook.addWorksheet).toHaveBeenCalledWith("SUMMARY", {
      views: [{ showGridLines: false }],
    });
  });

  it("should set worksheet columns from summaryColumns constant", () => {
    createSummarySheet(mockWorkbook, mockConfig);

    expect(mockWorksheet.columns).toEqual([
      { header: "A", key: "A", width: 10 },
      { header: "B", key: "B", width: 20 },
      { header: "C", key: "C", width: 30 },
      { header: "D", key: "D", width: 40 },
    ]);
  });

  it("should read and add logo image", () => {
    createSummarySheet(mockWorkbook, mockConfig);

    expect(readFileSync).toHaveBeenCalledWith(
      "/test/base/directory/logo-gigadat.png"
    );
    expect(mockWorkbook.addImage).toHaveBeenCalledWith({
      buffer: Buffer.from("fake-image-data"),
      extension: "png",
    });
    expect(mockWorksheet.addImage).toHaveBeenCalledWith(
      "image-id-123",
      "B1:C4"
    );
  });

  it("should set number format for column D", () => {
    createSummarySheet(mockWorkbook, mockConfig);

    expect(mockWorksheet.getColumn).toHaveBeenCalledWith("D");
    expect(mockColumn.numFmt).toBe(
      '_($* #,##0.00_);_($* (#,##0.00);_($* "-"??_);_(@_)'
    );
  });

  it("should merge cells and set customer trading name", () => {
    createSummarySheet(mockWorkbook, mockConfig);

    expect(mockWorksheet.mergeCells).toHaveBeenCalledWith("B6:D6");
    expect(mockWorksheet.getCell).toHaveBeenCalledWith("B6");
    expect(cellProperties["cell-B6"]?.alignment).toEqual({
      horizontal: "center",
    });
    expect(cellProperties["cell-B6"]?.font).toEqual({ bold: true, size: 11 });
    expect(mockWorksheet.getRow).toHaveBeenCalledWith(6);
    expect(cellValues["row-6-cell-2"]).toBe("Test Customer Trading");
  });

  it("should set label name in row 7", () => {
    createSummarySheet(mockWorkbook, mockConfig);

    expect(mockWorksheet.mergeCells).toHaveBeenCalledWith("B7:D7");
    expect(mockWorksheet.getCell).toHaveBeenCalledWith("B7");
    expect(mockWorksheet.getRow).toHaveBeenCalledWith(7);
    // Row 7, cell 2 should have the label name
    expect(cellValues["row-7-cell-2"]).toBe("Test Label Name");
  });

  it("should format and set date range in row 9", () => {
    createSummarySheet(mockWorkbook, mockConfig);

    expect(mockWorksheet.mergeCells).toHaveBeenCalledWith("B9:D9");
    expect(mockWorksheet.getRow).toHaveBeenCalledWith(9);
    // Just check that the row was accessed - the exact value depends on date formatting
    expect(cellValues["row-9-cell-2"]).toBeDefined();
  });

  it("should set transaction count and amount correctly (excluding minimum fees)", () => {
    createSummarySheet(mockWorkbook, mockConfig);

    // Check transaction count (100 - 2 = 98)
    expect(mockWorksheet.getRow).toHaveBeenCalledWith(13);
    expect(cellValues["row-13-cell-3"]).toBe(98);

    // Check transaction amount (10000.50 - 10.00 = 9990.50)
    expect(cellValues["row-13-cell-4"]).toBe(9990.5);
  });

  it("should set fee values correctly", () => {
    createSummarySheet(mockWorkbook, mockConfig);

    // Verify that fee rows are accessed
    expect(mockWorksheet.getRow).toHaveBeenCalledWith(15); // Gateway Fee
    expect(mockWorksheet.getRow).toHaveBeenCalledWith(16); // Transaction Fee
    expect(mockWorksheet.getRow).toHaveBeenCalledWith(17); // Sales Fee
  });

  it("should handle non-IDP platform (ETI) refund logic correctly", () => {
    createSummarySheet(mockWorkbook, mockConfig);

    // Verify that refund-related rows are accessed for non-IDP platforms
    expect(mockWorksheet.getRow).toHaveBeenCalledWith(18); // Return Fee row
    expect(mockWorksheet.getRow).toHaveBeenCalledWith(19); // Returns row
  });

  it("should handle IDP platform refund logic correctly", () => {
    const idpConfig = {
      ...mockConfig,
      platformCode: "IDP",
    };

    createSummarySheet(mockWorkbook, idpConfig);

    // Verify that refund-related rows are accessed for IDP platforms
    expect(mockWorksheet.getRow).toHaveBeenCalledWith(18); // Refund Fee row
    expect(mockWorksheet.getRow).toHaveBeenCalledWith(19); // Refunds row for IDP
  });

  it("should set TOTAL COSTS formula correctly", () => {
    createSummarySheet(mockWorkbook, mockConfig);

    // For non-IDP, TOTAL COSTS should be at row 20
    expect(mockWorksheet.getRow).toHaveBeenCalledWith(20);
  });

  it("should set Total Payable formula correctly", () => {
    createSummarySheet(mockWorkbook, mockConfig);

    // For non-IDP, Total Payable should be at row 22
    expect(mockWorksheet.getRow).toHaveBeenCalledWith(22);
  });

  it("should apply borders correctly for non-IDP platforms", () => {
    createSummarySheet(mockWorkbook, mockConfig);

    // Check that TOTAL COSTS row is accessed
    expect(mockWorksheet.getRow).toHaveBeenCalledWith(20);
  });

  it("should handle IDP platform correctly", () => {
    const idpConfig = {
      ...mockConfig,
      platformCode: "IDP",
    };

    createSummarySheet(mockWorkbook, idpConfig);

    // For IDP, TOTAL COSTS should be at row 21
    expect(mockWorksheet.getRow).toHaveBeenCalledWith(21);
  });
});
