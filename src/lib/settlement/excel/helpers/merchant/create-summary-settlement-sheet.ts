import { readFileSync } from "node:fs";

import { baseDirectoryName } from "@constants/filesystem";
import { excelNumberFormat, summaryColumns } from "@constants/settlement";
import { type ExcelConfig } from "@lib/settlement/approve/types";
import { format } from "date-fns";

import type ExcelJS from "exceljs";

export const createSummarySheet = (
  workbook: ExcelJS.Workbook,
  config: ExcelConfig
): void => {
  const {
    customerTradingName,
    platformCode,
    data: {
      labelName,
      transactionCount,
      totalTransactionAmount,
      refundCount,
      totalRefundAmount,
      gatewayFee,
      transactionFee,
      salesFee,
      refundFee,
      minimumFeeCount,
      totalMinimumAmount,
    },
    period: { fromDate, toDate },
  } = config;

  const summaryWorksheet = workbook.addWorksheet("SUMMARY", {
    views: [{ showGridLines: false }],
  });

  summaryWorksheet.columns = summaryColumns;

  const gigadatLogo = baseDirectoryName + "/logo-gigadat.png";
  const imageBuffer = readFileSync(gigadatLogo);

  const imageId = workbook.addImage({
    buffer: imageBuffer,
    extension: "png",
  });

  summaryWorksheet.addImage(imageId, "B1:C4");
  summaryWorksheet.getColumn("D").numFmt = excelNumberFormat;

  summaryWorksheet.mergeCells("B6:D6");
  summaryWorksheet.getCell("B6").alignment = { horizontal: "center" };
  summaryWorksheet.getCell("B6").font = { bold: true, size: 11 };
  summaryWorksheet.getRow(6).getCell(2).value = customerTradingName;

  summaryWorksheet.mergeCells("B7:D7");
  summaryWorksheet.getCell("B7").alignment = { horizontal: "center" };
  summaryWorksheet.getCell("B7").font = { bold: true, size: 11 };
  summaryWorksheet.getRow(7).getCell(2).value = labelName;

  summaryWorksheet.mergeCells("B9:D9");
  summaryWorksheet.getCell("B9").alignment = { horizontal: "center" };
  summaryWorksheet.getCell("B9").font = {
    bold: true,
  };
  summaryWorksheet.getRow(9).getCell(2).value =
    `${format(fromDate, "MMMM d")} - ${format(toDate, "MMMM d")}`;

  let rowNumber = 12;

  summaryWorksheet.getCell(`B${rowNumber}`).border = {
    bottom: { style: "thick" },
  };
  summaryWorksheet.getCell(`C${rowNumber}`).alignment = {
    horizontal: "center",
  };
  summaryWorksheet.getCell(`C${rowNumber}`).font = { bold: true };
  summaryWorksheet.getCell(`C${rowNumber}`).border = {
    bottom: { style: "thick" },
  };
  summaryWorksheet.getCell(`D${rowNumber}`).alignment = {
    horizontal: "right",
  };
  summaryWorksheet.getCell(`D${rowNumber}`).font = { bold: true };
  summaryWorksheet.getCell(`D${rowNumber}`).border = {
    bottom: { style: "thick" },
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).value = "# of Transactions";
  summaryWorksheet.getRow(rowNumber).getCell(4).value = "CAD";

  rowNumber++;

  summaryWorksheet.getRow(rowNumber).getCell(2).value = labelName;
  summaryWorksheet.getRow(rowNumber).getCell(3).value =
    transactionCount - minimumFeeCount;
  summaryWorksheet.getRow(rowNumber).getCell(4).value =
    totalTransactionAmount - totalMinimumAmount;
  summaryWorksheet.getRow(rowNumber).getCell(4).border = {
    top: { style: "thin" },
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).border = {
    top: { style: "thin" },
  };

  rowNumber += 2;
  summaryWorksheet.getRow(rowNumber).getCell(3).alignment = {
    horizontal: "right",
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).font = {
    bold: false,
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).value = "Gateway Fee";
  summaryWorksheet.getRow(rowNumber).getCell(4).value = gatewayFee;

  rowNumber++;
  summaryWorksheet.getRow(rowNumber).getCell(3).alignment = {
    horizontal: "right",
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).font = {
    bold: false,
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).value = "Transaction Fee";
  summaryWorksheet.getRow(rowNumber).getCell(4).value = transactionFee;

  rowNumber++;
  summaryWorksheet.getRow(rowNumber).getCell(3).alignment = {
    horizontal: "right",
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).font = {
    bold: false,
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).value = "Sales Fee";
  summaryWorksheet.getRow(rowNumber).getCell(4).value = salesFee;

  rowNumber++;

  summaryWorksheet.getRow(rowNumber).getCell(3).alignment = {
    horizontal: "right",
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).font = {
    bold: false,
  };

  if (platformCode === "IDP") {
    summaryWorksheet.getRow(rowNumber).getCell(3).value = "Refund Fee";
    summaryWorksheet.getRow(rowNumber).getCell(4).value =
      refundCount * refundFee;
    rowNumber++;
    summaryWorksheet.getRow(rowNumber).getCell(3).alignment = {
      horizontal: "right",
    };
    summaryWorksheet.getRow(rowNumber).getCell(3).value = "Refunds";
    summaryWorksheet.getRow(rowNumber).getCell(4).value = totalRefundAmount;
    rowNumber += 2;
  } else {
    summaryWorksheet.getRow(rowNumber).getCell(3).value = "Return Fee";
    summaryWorksheet.getRow(rowNumber).getCell(4).value =
      refundCount * refundFee;
    rowNumber++;
    summaryWorksheet.getRow(rowNumber).getCell(3).alignment = {
      horizontal: "right",
    };
    summaryWorksheet.getRow(rowNumber).getCell(3).value = "Returns";
    summaryWorksheet.getRow(rowNumber).getCell(4).value = {
      formula: "-(SUM(Refunds!E:E))",
    };
    rowNumber++;
  }

  summaryWorksheet.getRow(rowNumber).getCell(3).font = {
    bold: false,
  };

  summaryWorksheet.getRow(rowNumber).getCell(3).alignment = {
    horizontal: "right",
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).font = { bold: true };
  summaryWorksheet.getRow(rowNumber).getCell(3).value = "TOTAL COSTS";
  const rfmTotalCostsFormula = `SUM(D15:D${rowNumber - 1})`;
  summaryWorksheet.getRow(rowNumber).getCell(4).value = {
    formula: rfmTotalCostsFormula,
  };

  if (platformCode !== "IDP") {
    summaryWorksheet.getRow(rowNumber).getCell(4).border = {
      top: { style: "thin" },
    };
    summaryWorksheet.getRow(rowNumber).getCell(3).border = {
      top: { style: "thin" },
    };
  }

  rowNumber += 2;

  summaryWorksheet.getRow(rowNumber).getCell(3).alignment = {
    horizontal: "right",
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).font = { bold: true };
  summaryWorksheet.getRow(rowNumber).getCell(3).value = "Total Payable in CAD";
  const totalPayableFormula = `D13-D${rowNumber - 2}`;
  summaryWorksheet.getRow(rowNumber).getCell(4).value = {
    formula: totalPayableFormula,
  };
  summaryWorksheet.getRow(rowNumber).getCell(4).border = {
    top: { style: "thin" },
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).border = {
    top: { style: "thin" },
  };
};
