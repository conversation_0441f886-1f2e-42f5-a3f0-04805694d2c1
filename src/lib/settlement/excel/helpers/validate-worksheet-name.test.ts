import { validateWorksheetName } from "./validate-worksheet-name";

describe("validateWorksheetName", () => {
  it("should throw for empty string", () => {
    expect(() => {
      validateWorksheetName("");
    }).toThrow(/cannot be empty/);
  });

  it("should not throw for a valid worksheet name", () => {
    expect(() => {
      validateWorksheetName("Sheet1");
    }).not.toThrow();
  });

  it("should throw if name exceeds 31 characters", () => {
    const longName = "a".repeat(32);
    expect(() => {
      validateWorksheetName(longName);
    }).toThrow(/exceeds maximum length/);
  });

  it("should not throw if name is exactly 31 characters", () => {
    const validName = "a".repeat(31);
    expect(() => {
      validateWorksheetName(validName);
    }).not.toThrow();
  });

  it("should throw if name contains *", () => {
    expect(() => {
      validateWorksheetName("Sheet*1");
    }).toThrow(/invalid characters/);
  });

  it("should throw if name contains /", () => {
    expect(() => {
      validateWorksheetName("Sheet/1");
    }).toThrow(/invalid characters/);
  });

  it("should throw if name contains :", () => {
    expect(() => {
      validateWorksheetName("Sheet:1");
    }).toThrow(/invalid characters/);
  });

  it("should throw if name contains ?", () => {
    expect(() => {
      validateWorksheetName("Sheet?1");
    }).toThrow(/invalid characters/);
  });

  it("should throw if name contains [", () => {
    expect(() => {
      validateWorksheetName("Sheet[1");
    }).toThrow(/invalid characters/);
  });

  it("should throw if name contains \\ (backslash)", () => {
    expect(() => {
      validateWorksheetName("Sheet\\1");
    }).toThrow(/invalid characters/);
  });

  it("should throw if name contains ]", () => {
    expect(() => {
      validateWorksheetName("Sheet]1");
    }).toThrow(/invalid characters/);
  });

  it("should not throw for name with spaces and valid characters", () => {
    expect(() => {
      validateWorksheetName("My Sheet 1");
    }).not.toThrow();
  });

  it("should throw for name with multiple invalid characters", () => {
    expect(() => {
      validateWorksheetName("Sheet*:1?");
    }).toThrow(/invalid characters/);
  });
});
