/* eslint-disable @typescript-eslint/naming-convention */
import { isSameDay } from "@utils/date-only";

import { checkSettlementDatesMatch } from "./check-settlement-dates";

import type { SettlementResult } from "@lib/settlement/functions/workers/types";

vi.mock("@utils/date-only", () => ({
  isSameDay: vi.fn(),
}));

const fromDate = {
  year: 2025,
  month: 1,
  day: 1,
};
const toDate = {
  year: 2025,
  month: 1,
  day: 31,
};

describe("checkSettlementDatesMatch", () => {
  afterEach(() => {
    vi.resetAllMocks();
  });

  it("throws error if no platforms are present", () => {
    const settlementResult = {
      settlement: {},
    } as unknown as SettlementResult;

    expect(() => checkSettlementDatesMatch(settlementResult)).toThrow(
      "No platforms found in settlement result"
    );
  });

  it("returns true if only one platform", () => {
    const settlementResult = {
      settlement: {
        ETI: {
          feesBreakdown: {
            interval: { fromDate, toDate },
          },
        },
      },
    } as unknown as SettlementResult;

    expect(checkSettlementDatesMatch(settlementResult)).toBe(true);
  });

  it("returns true if all platforms have matching dates", () => {
    const settlementResult = {
      settlement: {
        ETI: {
          feesBreakdown: {
            interval: { fromDate, toDate },
          },
        },
        RFM: {
          feesBreakdown: {
            interval: { fromDate, toDate },
          },
        },
      },
    } as unknown as SettlementResult;

    vi.mocked(isSameDay).mockReturnValue(true);

    expect(checkSettlementDatesMatch(settlementResult)).toBe(true);
  });

  it("returns false if platforms have different fromDate", () => {
    const differentFromDate = {
      year: 2025,
      month: 2,
      day: 1,
    };

    const settlementResult = {
      settlement: {
        ETI: {
          feesBreakdown: {
            interval: { fromDate, toDate },
          },
        },
        RFM: {
          feesBreakdown: {
            interval: { differentFromDate, toDate },
          },
        },
      },
    } as unknown as SettlementResult;

    vi.mocked(isSameDay).mockReturnValueOnce(false);
    vi.mocked(isSameDay).mockReturnValueOnce(true);

    expect(checkSettlementDatesMatch(settlementResult)).toBe(false);
  });

  it("returns false if platforms have different toDate", () => {
    const differentToDate = {
      year: 2025,
      month: 1,
      day: 15,
    };

    const settlementResult = {
      settlement: {
        ETI: {
          feesBreakdown: {
            interval: { fromDate, toDate },
          },
        },
        RFM: {
          feesBreakdown: {
            interval: { fromDate, differentToDate },
          },
        },
      },
    } as unknown as SettlementResult;

    vi.mocked(isSameDay).mockReturnValueOnce(true);
    vi.mocked(isSameDay).mockReturnValueOnce(false);

    expect(checkSettlementDatesMatch(settlementResult)).toBe(false);
  });

  it("returns false if platforms have different fromDate and toDate", () => {
    const differentFromDate = {
      year: 2025,
      month: 2,
      day: 1,
    };
    const differentToDate = {
      year: 2025,
      month: 2,
      day: 28,
    };

    const settlementResult = {
      settlement: {
        ETI: {
          feesBreakdown: {
            interval: { differentFromDate, differentToDate },
          },
        },
        RFM: {
          feesBreakdown: {
            interval: { fromDate, toDate },
          },
        },
      },
    } as unknown as SettlementResult;

    vi.mocked(isSameDay).mockReturnValueOnce(false);
    vi.mocked(isSameDay).mockReturnValueOnce(false);

    expect(checkSettlementDatesMatch(settlementResult)).toBe(false);
  });
});
