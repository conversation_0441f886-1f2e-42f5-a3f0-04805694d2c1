import { isSameDay } from "@utils/date-only";

import type {
  NonMerchantFeesBreakdown,
  SettlementResult,
} from "@lib/settlement/functions/workers/types";

export const checkSettlementDatesMatch = (
  settlementResult: SettlementResult
) => {
  const platformKeys = Object.keys(settlementResult.settlement);

  if (platformKeys.length === 0) {
    throw new Error("No platforms found in settlement result");
  }

  if (platformKeys.length === 1) {
    // Only one platform, no need to check dates
    return true;
  }

  const firstPlatformKey = platformKeys[0]!;

  const firstFeesBreakdown = settlementResult.settlement[firstPlatformKey]!
    .feesBreakdown as NonMerchantFeesBreakdown;

  const { fromDate, toDate } = firstFeesBreakdown.interval;

  return platformKeys.every((platformKey) => {
    if (platformKey === firstPlatformKey) {
      // Skip the first platform as we are using its dates to compare
      return true;
    }

    const feesBreakdown = settlementResult.settlement[platformKey]!
      .feesBreakdown as NonMerchantFeesBreakdown;

    return (
      isSameDay(fromDate, feesBreakdown.interval.fromDate) &&
      isSameDay(toDate, feesBreakdown.interval.toDate)
    );
  });
};
