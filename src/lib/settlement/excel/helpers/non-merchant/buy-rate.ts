/* eslint-disable @typescript-eslint/naming-convention */
import { excelNumberFormat } from "@constants/settlement";
import { type DateOnly, fromDateOnlyToStringLong } from "@utils/date-only";

import { validateWorksheetName } from "../validate-worksheet-name";

import type { Platform } from "@constants/transactions/platform";
import type { MerchantPlatformCommission } from "@lib/settlement/functions/workers/calculate-non-merchant-settlement";
import type ExcelJS from "exceljs";

type MerchantSummary = {
  merchantName: string;
  platformCommission: Partial<Record<Platform, number>>;
};

type GroupedMerchantSummary = Record<string, MerchantSummary>;

const generateBuyRateSummarySheet = (
  groupedMerchantSummary: GroupedMerchantSummary,
  customerTradingName: string,
  interval: { fromDate: DateOnly; toDate: DateOnly },
  workbook: ExcelJS.Workbook
): void => {
  const { fromDate, toDate } = interval;

  // Map of platform to column letter in the summary sheet
  const platformColumnMap: Record<Platform, string> = {
    IDP: "G",
    ETI: "H",
    ETO: "I",
    ACH: "J",
    RFM: "K",
    RTO: "L",
    RTX: "M",
    ETF: "N",
    ANR: "O",
    ANX: "P",
  };

  const worksheet = workbook.addWorksheet("SUMMARY", {
    views: [{ showGridLines: false }],
  });

  worksheet.getColumn("A").width = 16;
  worksheet.getColumn("B").width = 30;
  worksheet.getColumn("C").width = 16;
  worksheet.getColumn("D").width = 16;

  worksheet.getColumn("D").numFmt = excelNumberFormat;

  worksheet.mergeCells("B7:D7");
  worksheet.getCell("B7").alignment = { horizontal: "center" };
  worksheet.getCell("B7").font = {
    bold: true,
  };
  worksheet.getCell("B7").value = customerTradingName;

  worksheet.mergeCells("B8:D8");
  worksheet.getCell("B8").alignment = { horizontal: "center" };
  worksheet.getCell("B8").font = {
    bold: true,
  };
  worksheet.getCell("B8").value = "Summary Settlement";

  worksheet.mergeCells("B9:D9");
  worksheet.getCell("B9").alignment = { horizontal: "center" };
  worksheet.getCell("B9").font = {
    bold: true,
  };
  worksheet.getCell("B9").value =
    `${fromDateOnlyToStringLong(fromDate)} - ${fromDateOnlyToStringLong(toDate)}`;

  worksheet.getCell("D11").alignment = { horizontal: "right" };
  worksheet.getCell("D11").font = {
    bold: true,
  };
  worksheet.getCell("D11").value = "CAD";

  // Set up platform columns
  for (const [platform, column] of Object.entries(platformColumnMap)) {
    worksheet.getColumn(column).width = 16;
    worksheet.getColumn(column).numFmt = excelNumberFormat;

    worksheet.getCell(`${column}11`).alignment = { horizontal: "right" };
    worksheet.getCell(`${column}11`).font = {
      bold: true,
    };
    worksheet.getCell(`${column}11`).value = platform;
  }

  const initialRow = 13;
  let currentRow = initialRow;

  // Each row is a merchant's summary
  for (const merchantSummary of Object.values(groupedMerchantSummary)) {
    worksheet.getCell(`B${currentRow}`).font = {
      bold: true,
    };
    worksheet.getCell(`B${currentRow}`).value = merchantSummary.merchantName;

    worksheet.getCell(`D${currentRow}`).value = {
      formula: `SUM(G${currentRow}:P${currentRow})`,
    };

    // Each column is a platform's commission from the merchant
    for (const [platform, commission] of Object.entries(
      merchantSummary.platformCommission
    )) {
      const column = platformColumnMap[platform as Platform];

      worksheet.getCell(`${column}${currentRow}`).value = commission;
    }

    currentRow += 1;
  }

  const finalRow = currentRow + 1;

  worksheet.getCell(`B${finalRow}`).font = { bold: true };
  worksheet.getCell(`B${finalRow}`).value = "Total";

  worksheet.getCell(`D${finalRow}`).font = { bold: true };
  worksheet.getCell(`D${finalRow}`).value = {
    formula: `SUM(D${initialRow}:D${currentRow - 1})`,
  };

  // Populate total commission for each platform
  for (const column of Object.values(platformColumnMap)) {
    worksheet.getCell(`${column}${finalRow}`).alignment = {
      horizontal: "center",
    };
    worksheet.getCell(`${column}${finalRow}`).font = { bold: true };
    worksheet.getCell(`${column}${finalRow}`).value = {
      formula: `SUM(${column}${initialRow}:${column}${currentRow - 1})`,
    };
  }
};

const generateBuyRatePlatformSheet = (
  data: {
    customerTradingName: string;
    fromDate: DateOnly;
    toDate: DateOnly;
    platform: Platform;
    platformDescription: string;
    merchantPlatformCommission: MerchantPlatformCommission & {
      merchantName: string;
      serviceNumber: string;
      rateDeterminingInterval: {
        fromDate: DateOnly;
        toDate: DateOnly;
      };
    };
  },
  workbook: ExcelJS.Workbook,
  options?: {
    includeGatewayFee?: boolean;
  }
) => {
  const {
    customerTradingName,
    fromDate,
    toDate,
    platformDescription,
    platform,
    merchantPlatformCommission,
  } = data;

  const { merchantName } = merchantPlatformCommission;

  const { transactionCount, totalTransactionAmount } =
    merchantPlatformCommission.feeCollectedFromMerchant.meta;

  const { gatewayFee } = merchantPlatformCommission.fees.rates;
  const transactionFee =
    merchantPlatformCommission.fees.rates.salesTierItem?.transactionFee ?? 0;
  const salesFee =
    merchantPlatformCommission.fees.rates.salesTierItem?.salesFee ?? 0;

  const costCollectedFromMerchant =
    merchantPlatformCommission.feeCollectedFromMerchant.totalCollectedFee;

  const worksheetName = `${merchantName} ${platform}`;

  validateWorksheetName(worksheetName);

  const worksheet = workbook.addWorksheet(worksheetName, {
    views: [{ showGridLines: false }],
  });

  worksheet.getColumn("A").width = 16;
  worksheet.getColumn("B").width = 30;
  worksheet.getColumn("C").width = 20;
  worksheet.getColumn("D").width = 15;

  worksheet.getColumn("D").numFmt = excelNumberFormat;

  worksheet.mergeCells("B7:D7");
  worksheet.getCell("B7").alignment = { horizontal: "center" };
  worksheet.getCell("B7").font = { bold: true };
  worksheet.getCell("B7").value = customerTradingName;

  worksheet.mergeCells("B8:D8");
  worksheet.getCell("B8").alignment = { horizontal: "center" };
  worksheet.getCell("B8").font = { bold: true };
  worksheet.getCell("B8").value = `${platformDescription} Settlement`;

  worksheet.mergeCells("B10:D10");
  worksheet.getCell("B10").alignment = { horizontal: "center" };
  worksheet.getCell("B10").font = { bold: true };
  worksheet.getCell("B10").value =
    `${fromDateOnlyToStringLong(fromDate)} - ${fromDateOnlyToStringLong(toDate)}`;

  worksheet.getCell("C12").alignment = { horizontal: "center" };
  worksheet.getCell("C12").font = {
    bold: true,
  };
  worksheet.getCell("C12").value = "# of Transactions";

  worksheet.getCell("D12").alignment = { horizontal: "center" };
  worksheet.getCell("D12").font = {
    bold: true,
  };
  worksheet.getCell("D12").value = "CAD";

  worksheet.getCell("B13").value = platformDescription;

  worksheet.getCell("C13").alignment = { horizontal: "center" };
  worksheet.getCell("C13").border = { bottom: { style: "thick" } };
  worksheet.getCell("C13").font = { bold: true };
  worksheet.getCell("C13").value = transactionCount;

  worksheet.getCell("D13").alignment = { horizontal: "center" };
  worksheet.getCell("D13").border = { bottom: { style: "thick" } };
  worksheet.getCell("D13").font = { bold: true };
  worksheet.getCell("D13").value = totalTransactionAmount;

  if (options?.includeGatewayFee) {
    worksheet.getCell("C15").value = "Gateway Fee";

    // Total gateway fee charged to the non-merchant
    worksheet.getCell("D15").value = {
      formula: `D13*${gatewayFee}`,
    };
  }

  worksheet.getCell("C16").value = "Transaction Fee";

  // Total transaction fee charged to the non-merchant
  worksheet.getCell("D16").value = {
    formula: `C13*${transactionFee}`,
  };

  worksheet.getCell("C17").value = "Sales Fee";

  // Total sales fee charged to the non-merchant
  worksheet.getCell("D17").value = {
    formula: `D13*${salesFee}`,
  };

  worksheet.getCell("C18").font = { bold: true };
  worksheet.getCell("C18").value = "TOTAL COSTS";

  // Total cost charged to the non-merchant
  worksheet.getCell("D18").value = {
    formula: `SUM(D15:D17)`,
  };

  worksheet.getCell("D18").border = {
    bottom: { style: "thin" },
    top: { style: "thin" },
  };

  worksheet.getCell("B20").alignment = { horizontal: "right" };
  worksheet.getCell("B20").value =
    `COSTS COLLECTED FROM ${merchantName.toUpperCase()}`;

  worksheet.getCell("D20").value = costCollectedFromMerchant;

  worksheet.getCell("B22").alignment = { horizontal: "right" };
  worksheet.getCell("B22").font = { bold: true };
  worksheet.getCell("B22").value =
    `DIFFERENCE PAYABLE TO ${customerTradingName}`;

  // Difference payable
  worksheet.getCell("D22").border = {
    bottom: { style: "thin" },
    top: { style: "thin" },
  };
  worksheet.getCell("D22").font = { bold: true };
  worksheet.getCell("D22").value = {
    formula: `D20-D18`,
  };
};

export {
  generateBuyRateSummarySheet,
  generateBuyRatePlatformSheet,
  type GroupedMerchantSummary,
};
