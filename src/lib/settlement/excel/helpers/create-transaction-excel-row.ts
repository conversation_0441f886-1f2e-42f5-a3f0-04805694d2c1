import { type Transaction } from "../../../../services/blusky/transactions";

import type ExcelJS from "exceljs";

export const createTransactionRow = (
  sheetName: ExcelJS.Worksheet,
  platformTransaction: Transaction,
  rowNumber: number
) => {
  sheetName.getRow(rowNumber).getCell(1).value =
    platformTransaction.createdDate;
  sheetName.getRow(rowNumber).getCell(2).value =
    platformTransaction.updatedDate;
  sheetName.getRow(rowNumber).getCell(3).value = Number(
    platformTransaction.serviceNumber
  );
  sheetName.getRow(rowNumber).getCell(4).value =
    platformTransaction.originalAmt;
  sheetName.getRow(rowNumber).getCell(5).value = platformTransaction.refundAmt;
  sheetName.getRow(rowNumber).getCell(6).value = platformTransaction.finalAmt;
  sheetName.getRow(rowNumber).getCell(7).value = platformTransaction.currency;
  sheetName.getRow(rowNumber).getCell(8).value = platformTransaction.country;
  sheetName.getRow(rowNumber).getCell(9).value = platformTransaction.billable
    ? "True"
    : "False";
  sheetName.getRow(rowNumber).getCell(10).value = platformTransaction.platform;
  sheetName.getRow(rowNumber).getCell(11).value =
    platformTransaction.custNumber;
  sheetName.getRow(rowNumber).getCell(12).value = String(
    platformTransaction.rcode
  );
  sheetName.getRow(rowNumber).getCell(13).value =
    platformTransaction.integratorName;
  sheetName.getRow(rowNumber).getCell(14).value =
    platformTransaction.programName;
  sheetName.getRow(rowNumber).getCell(15).value =
    platformTransaction.billingName;
  sheetName.getRow(rowNumber).getCell(16).value =
    platformTransaction.transactionID;
  sheetName.getRow(rowNumber).getCell(17).value = platformTransaction.receiptID;
  sheetName.getRow(rowNumber).getCell(18).value =
    platformTransaction.interacRef;
  sheetName.getRow(rowNumber).getCell(19).value = platformTransaction.fIName;
  sheetName.getRow(rowNumber).getCell(20).value = platformTransaction.status;
};
