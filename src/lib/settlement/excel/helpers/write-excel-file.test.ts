import { existsSync, mkdirSync } from "node:fs";

import { baseDirectoryName } from "@constants/filesystem";

import { writeExcelFile } from "./write-excel-file";

import type ExcelJS from "exceljs";

vi.mock("node:fs", () => ({
  existsSync: vi.fn(),
  mkdirSync: vi.fn(),
}));

const mockWorkbook = {
  xlsx: {
    writeFile: vi.fn(),
  },
} as unknown as ExcelJS.Workbook;

describe("writeExcelFile", () => {
  afterEach(() => {
    vi.resetAllMocks();
  });

  it("should create the folder if it does not exist", async () => {
    vi.mocked(existsSync).mockReturnValue(false);

    const folderLocation = "/test-folder";
    const fileName = "test-file";
    const expectedFolderPath = `${baseDirectoryName}/test-folder`;
    const expectedFilePath = `${baseDirectoryName}/test-folder/test-file.xlsx`;

    await writeExcelFile(mockWorkbook, folderLocation, fileName);

    expect(existsSync).toHaveBeenCalledWith(expectedFolderPath);
    expect(mkdirSync).toHaveBeenCalledWith(expectedFolderPath, {
      recursive: true,
    });
    expect(mockWorkbook.xlsx.writeFile).toHaveBeenCalledWith(expectedFilePath);
  });

  it("should not create the folder if it already exists", async () => {
    vi.mocked(existsSync).mockReturnValue(true);

    const folderLocation = "/test-folder";
    const fileName = "test-file";
    const expectedFolderPath = `${baseDirectoryName}/test-folder`;
    const expectedFilePath = `${baseDirectoryName}/test-folder/test-file.xlsx`;

    await writeExcelFile(mockWorkbook, folderLocation, fileName);

    expect(existsSync).toHaveBeenCalledWith(expectedFolderPath);
    expect(mkdirSync).not.toHaveBeenCalled();
    expect(mockWorkbook.xlsx.writeFile).toHaveBeenCalledWith(expectedFilePath);
  });

  it("should handle folderLocation with trailing slash", async () => {
    vi.mocked(existsSync).mockReturnValue(true);

    const folderLocation = "test-folder/";
    const fileName = "test-file";
    const expectedFolderPath = `${baseDirectoryName}/test-folder/`;
    const expectedFilePath = `${baseDirectoryName}/test-folder/test-file.xlsx`;

    await writeExcelFile(mockWorkbook, folderLocation, fileName);

    expect(existsSync).toHaveBeenCalledWith(expectedFolderPath);
    expect(mockWorkbook.xlsx.writeFile).toHaveBeenCalledWith(expectedFilePath);
  });

  it("should handle folderLocation with no slash", async () => {
    vi.mocked(existsSync).mockReturnValue(true);

    const folderLocation = "test-folder";
    const fileName = "test-file";
    const expectedFolderPath = `${baseDirectoryName}/test-folder`;
    const expectedFilePath = `${baseDirectoryName}/test-folder/test-file.xlsx`;

    await writeExcelFile(mockWorkbook, folderLocation, fileName);

    expect(existsSync).toHaveBeenCalledWith(expectedFolderPath);
    expect(mockWorkbook.xlsx.writeFile).toHaveBeenCalledWith(expectedFilePath);
  });
});
