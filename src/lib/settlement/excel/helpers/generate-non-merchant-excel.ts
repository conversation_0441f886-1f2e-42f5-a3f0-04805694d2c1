import { readFileSync } from "node:fs";

import { entities } from "@constants/entity";
import { baseDirectoryName } from "@constants/filesystem";
import { settlementTypes } from "@constants/settlement";
import { payInGroup, type Platform } from "@constants/transactions/platform";
import ExcelJS from "exceljs";

import {
  generateBuyRatePlatformSheet,
  generateBuyRateSummarySheet,
  type GroupedMerchantSummary,
} from "./non-merchant/buy-rate";
import { checkSettlementDatesMatch } from "./non-merchant/check-settlement-dates";
import {
  generateCommissionRatePlatformSheet,
  generateCommissionRateSummarySheet,
} from "./non-merchant/commission-rate";
import { writeExcelFile } from "./write-excel-file";

import type {
  NonMerchantFeesBreakdown,
  SettlementResult,
} from "../../functions/workers/types";
import type { Customer } from "@lib/customer/repository/types";
import type { MerchantPlatformCommission } from "@lib/settlement/functions/workers/calculate-non-merchant-settlement";
import type { DateOnly } from "@utils/date-only";

export const generateNonMerchantExcel = async (
  settlementResult: SettlementResult,
  customer: Customer,
  fileName: string
): Promise<void> => {
  const workbook = new ExcelJS.Workbook();

  if (!checkSettlementDatesMatch(settlementResult)) {
    throw new Error("Settlement dates do not match across platforms");
  }

  const firstPlatformKey = Object.keys(settlementResult.settlement)[0]!;
  const firstFeesBreakdown = settlementResult.settlement[firstPlatformKey]!
    .feesBreakdown as NonMerchantFeesBreakdown;
  const { interval } = firstFeesBreakdown;

  switch (customer.nonMerchantSettlementType) {
    case settlementTypes.commissionRate: {
      _generateCommissionRateExcel(
        settlementResult,
        customer,
        interval,
        workbook
      );
      break;
    }

    case settlementTypes.buyRate: {
      // Handle buy rate specific logic
      _generateBuyRateExcel(settlementResult, customer, interval, workbook);
      break;
    }

    case settlementTypes.combination: {
      // Handle combination specific logic
      break;
    }

    default: {
      throw new Error("Unknown non-merchant settlement type");
    }
  }

  const image = workbook.addImage({
    buffer: readFileSync(`${baseDirectoryName}/${customer.entityLogoName}`),
    extension: "png",
  });

  workbook.eachSheet((worksheet) => {
    if (customer.entityName === entities.gigadat) {
      worksheet.addImage(image, "B1:C4");
    } else {
      worksheet.addImage(image, "C1:C5");
    }
  });

  await writeExcelFile(workbook, customer.statementFolderLocation, fileName);
};

const _generateCommissionRateExcel = (
  settlementResult: SettlementResult,
  customer: Customer,
  interval: { fromDate: DateOnly; toDate: DateOnly },
  workbook: ExcelJS.Workbook
): void => {
  generateCommissionRateSummarySheet(
    settlementResult,
    customer,
    interval,
    workbook
  );

  for (const [platformKey, platformSettlement] of Object.entries(
    settlementResult.settlement
  )) {
    const platform = platformKey as Platform;

    // Hard coded to use only sales fee for pay-ins and only transaction fee for pay-outs
    // Update in FA-999 to allow for more flexibility
    const feeType = payInGroup.includes(platform)
      ? "salesFee"
      : "transactionFee";

    generateCommissionRatePlatformSheet({
      customer,
      platform,
      platformSettlement,
      workbook,
      options: {
        feeType,
      },
    });
  }
};

const _generateBuyRateExcel = (
  settlementResult: SettlementResult,
  customer: Customer,
  interval: { fromDate: DateOnly; toDate: DateOnly },
  workbook: ExcelJS.Workbook
): void => {
  const { customerTradingName } = customer;

  const merchantPlatformData = [];

  for (const [platformKey, platformSettlement] of Object.entries(
    settlementResult.settlement
  )) {
    const platform = platformKey as Platform;

    const { platformDescription } = platformSettlement;

    const feesBreakdown =
      platformSettlement.feesBreakdown as NonMerchantFeesBreakdown;

    for (const merchantPlatformCommission of Object.values(
      feesBreakdown.merchantBreakdown
    )) {
      merchantPlatformData.push({
        platform,
        platformDescription,
        merchantPlatformCommission,
      });
    }
  }

  // Sort by ascending alphabetical order of merchant names
  merchantPlatformData.sort((a, b) =>
    a.merchantPlatformCommission.merchantName.localeCompare(
      b.merchantPlatformCommission.merchantName
    )
  );

  const groupedMerchantSummary = _groupSummaryByMerchant(merchantPlatformData);

  generateBuyRateSummarySheet(
    groupedMerchantSummary,
    customerTradingName,
    interval,
    workbook
  );

  for (const {
    platform,
    platformDescription,
    merchantPlatformCommission,
  } of merchantPlatformData) {
    const data = {
      customerTradingName,
      fromDate: interval.fromDate,
      toDate: interval.toDate,
      platform,
      platformDescription,
      merchantPlatformCommission,
    };

    const options = {
      includeGatewayFee: payInGroup.includes(platform),
    };

    generateBuyRatePlatformSheet(data, workbook, options);
  }
};

const _groupSummaryByMerchant = (
  merchantPlatformData: Array<{
    platform: Platform;
    platformDescription: string;
    merchantPlatformCommission: MerchantPlatformCommission & {
      merchantName: string;
      serviceNumber: string;
      rateDeterminingInterval: {
        fromDate: DateOnly;
        toDate: DateOnly;
      };
    };
  }>
): GroupedMerchantSummary => {
  const groupedMerchantSummary: GroupedMerchantSummary = {};

  for (const data of merchantPlatformData) {
    const { platform, merchantPlatformCommission } = data;
    const { merchantName, serviceNumber } = merchantPlatformCommission;

    // Initialize the merchant summary if it doesn't exist
    groupedMerchantSummary[serviceNumber] ??= {
      merchantName,
      platformCommission: {},
    };

    groupedMerchantSummary[serviceNumber]!.platformCommission[platform] =
      merchantPlatformCommission.commission;
  }

  return groupedMerchantSummary;
};
