import { beforeEach, describe, expect, it, vi } from "vitest";

import { createTransactionRow } from "./create-transaction-excel-row";

import type { Transaction } from "../../../../services/blusky/transactions";
import type ExcelJS from "exceljs";

describe("createTransactionRow", () => {
  let mockWorksheet: ExcelJS.Worksheet;
  let mockRow: { getCell: ReturnType<typeof vi.fn> };
  let cellValues: Record<number, unknown>;

  beforeEach(() => {
    cellValues = {};

    mockRow = {
      getCell: vi.fn().mockImplementation((cellNumber: number) => ({
        set value(val: unknown) {
          cellValues[cellNumber] = val;
        },
        get value() {
          return cellValues[cellNumber];
        },
      })),
    };

    mockWorksheet = {
      getRow: vi.fn().mockReturnValue(mockRow),
    } as unknown as ExcelJS.Worksheet;
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("should populate all transaction fields in the correct cells", () => {
    const mockTransaction: Transaction = {
      createdDate: "2024-01-15T10:30:00Z",
      updatedDate: "2024-01-15T11:45:00Z",
      serviceNumber: "**********",
      originalAmt: 100.5,
      refundAmt: 0,
      finalAmt: 100.5,
      currency: "CAD",
      country: "CA",
      billable: true,
      platform: "ETI",
      custNumber: "CUST123",
      rcode: 200,
      integratorName: "Test Integrator",
      programName: "Test Program",
      billingName: "Test Billing",
      // eslint-disable-next-line @typescript-eslint/naming-convention
      transactionID: "TXN123456",
      // eslint-disable-next-line @typescript-eslint/naming-convention
      receiptID: "RCP789012",
      interacRef: "INT345678",
      // eslint-disable-next-line @typescript-eslint/naming-convention
      fIName: "Test Bank",
      status: "STATUS_SUCCESS",
    };

    const rowNumber = 5;

    createTransactionRow(mockWorksheet, mockTransaction, rowNumber);

    // Verify getRow was called with correct row number
    expect(mockWorksheet.getRow).toHaveBeenCalledWith(rowNumber);

    // Verify all cells were populated with correct values
    expect(mockRow.getCell).toHaveBeenCalledTimes(20);

    // Check each cell value
    expect(mockRow.getCell).toHaveBeenNthCalledWith(1, 1);
    expect(mockRow.getCell).toHaveBeenNthCalledWith(2, 2);
    expect(mockRow.getCell).toHaveBeenNthCalledWith(3, 3);
    expect(mockRow.getCell).toHaveBeenNthCalledWith(4, 4);
    expect(mockRow.getCell).toHaveBeenNthCalledWith(5, 5);
    expect(mockRow.getCell).toHaveBeenNthCalledWith(6, 6);
    expect(mockRow.getCell).toHaveBeenNthCalledWith(7, 7);
    expect(mockRow.getCell).toHaveBeenNthCalledWith(8, 8);
    expect(mockRow.getCell).toHaveBeenNthCalledWith(9, 9);
    expect(mockRow.getCell).toHaveBeenNthCalledWith(10, 10);
    expect(mockRow.getCell).toHaveBeenNthCalledWith(11, 11);
    expect(mockRow.getCell).toHaveBeenNthCalledWith(12, 12);
    expect(mockRow.getCell).toHaveBeenNthCalledWith(13, 13);
    expect(mockRow.getCell).toHaveBeenNthCalledWith(14, 14);
    expect(mockRow.getCell).toHaveBeenNthCalledWith(15, 15);
    expect(mockRow.getCell).toHaveBeenNthCalledWith(16, 16);
    expect(mockRow.getCell).toHaveBeenNthCalledWith(17, 17);
    expect(mockRow.getCell).toHaveBeenNthCalledWith(18, 18);
    expect(mockRow.getCell).toHaveBeenNthCalledWith(19, 19);
    expect(mockRow.getCell).toHaveBeenNthCalledWith(20, 20);

    // Verify the values were set correctly
    expect(cellValues[1]).toBe("2024-01-15T10:30:00Z"); // createdDate
    expect(cellValues[2]).toBe("2024-01-15T11:45:00Z"); // updatedDate
    expect(cellValues[3]).toBe(1_234_567_890); // serviceNumber as number
    expect(cellValues[4]).toBe(100.5); // originalAmt
    expect(cellValues[5]).toBe(0); // refundAmt
    expect(cellValues[6]).toBe(100.5); // finalAmt
    expect(cellValues[7]).toBe("CAD"); // currency
    expect(cellValues[8]).toBe("CA"); // country
    expect(cellValues[9]).toBe("True"); // billable as string
    expect(cellValues[10]).toBe("ETI"); // platform
    expect(cellValues[11]).toBe("CUST123"); // custNumber
    expect(cellValues[12]).toBe("200"); // rcode as string
    expect(cellValues[13]).toBe("Test Integrator"); // integratorName
    expect(cellValues[14]).toBe("Test Program"); // programName
    expect(cellValues[15]).toBe("Test Billing"); // billingName
    expect(cellValues[16]).toBe("TXN123456"); // transactionID
    expect(cellValues[17]).toBe("RCP789012"); // receiptID
    expect(cellValues[18]).toBe("INT345678"); // interacRef
    expect(cellValues[19]).toBe("Test Bank"); // fIName
    expect(cellValues[20]).toBe("STATUS_SUCCESS"); // status
  });

  it("should convert billable false to 'False' string", () => {
    const mockTransaction: Transaction = {
      createdDate: "2024-01-15T10:30:00Z",
      updatedDate: "2024-01-15T11:45:00Z",
      serviceNumber: "**********",
      originalAmt: 50.25,
      refundAmt: 10.0,
      finalAmt: 40.25,
      currency: "USD",
      country: "US",
      billable: false, // Testing false case
      platform: "RFM",
      custNumber: "CUST456",
      rcode: 404,
      integratorName: "Another Integrator",
      programName: "Another Program",
      billingName: "Another Billing",
      // eslint-disable-next-line @typescript-eslint/naming-convention
      transactionID: "TXN654321",
      // eslint-disable-next-line @typescript-eslint/naming-convention
      receiptID: "RCP210987",
      interacRef: "INT876543",
      // eslint-disable-next-line @typescript-eslint/naming-convention
      fIName: "Another Bank",
      status: "STATUS_REFUNDED",
    };

    createTransactionRow(mockWorksheet, mockTransaction, 3);

    // Check that billable false becomes "False"
    expect(cellValues[9]).toBe("False");
  });

  it("should handle numeric conversions correctly", () => {
    const mockTransaction: Transaction = {
      createdDate: "2024-01-15T10:30:00Z",
      updatedDate: "2024-01-15T11:45:00Z",
      serviceNumber: "**********", // String that should be converted to number
      originalAmt: 999.99,
      refundAmt: 50.5,
      finalAmt: 949.49,
      currency: "EUR",
      country: "DE",
      billable: true,
      platform: "IDP",
      custNumber: "CUST789",
      rcode: 500, // Number that should be converted to string
      integratorName: "Third Integrator",
      programName: "Third Program",
      billingName: "Third Billing",
      // eslint-disable-next-line @typescript-eslint/naming-convention
      transactionID: "TXN987654",
      // eslint-disable-next-line @typescript-eslint/naming-convention
      receiptID: "RCP456789",
      interacRef: "INT123456",
      // eslint-disable-next-line @typescript-eslint/naming-convention
      fIName: "Third Bank",
      status: "STATUS_FAILED",
    };

    createTransactionRow(mockWorksheet, mockTransaction, 7);

    // Check serviceNumber conversion to number
    expect(cellValues[3]).toBe(9_876_543_210);

    // Check rcode conversion to string
    expect(cellValues[12]).toBe("500");
  });
});
