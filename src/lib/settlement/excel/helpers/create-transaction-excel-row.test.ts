/* eslint-disable @typescript-eslint/naming-convention */
import { beforeEach, describe, expect, it, vi } from "vitest";

import { createTransactionRow } from "./create-transaction-excel-row";

import type { Transaction } from "../../../../services/blusky/transactions";
import type ExcelJS from "exceljs";

describe("createTransactionRow", () => {
  let mockWorksheet: ExcelJS.Worksheet;
  let mockRow: { getCell: ReturnType<typeof vi.fn> };
  let cellValues: Record<number, unknown>;

  beforeEach(() => {
    cellValues = {};

    mockRow = {
      getCell: vi.fn().mockImplementation((cellNumber: number) => ({
        get value() {
          return cellValues[cellNumber];
        },
        set value(v: unknown) {
          cellValues[cellNumber] = v;
        },
      })),
    };

    mockWorksheet = {
      getRow: vi.fn().mockReturnValue(mockRow),
    } as unknown as ExcelJS.Worksheet;
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("should populate all transaction fields in the correct cells", () => {
    const mockTransaction: Transaction = {
      createdDate: "2024-01-15T10:30:00Z",
      updatedDate: "2024-01-15T11:45:00Z",
      serviceNumber: "**********",
      originalAmt: 100.5,
      refundAmt: 0,
      finalAmt: 100.5,
      currency: "CAD",
      country: "CA",
      billable: true,
      platform: "ETI",
      custNumber: "CUST123",
      rcode: 200,
      integratorName: "Test Integrator",
      programName: "Test Program",
      billingName: "Test Billing",
      transactionID: "TXN123456",
      receiptID: "RCP789012",
      interacRef: "INT345678",
      fIName: "Test Bank",
      status: "STATUS_SUCCESS",
    };

    const rowNumber = 5;

    createTransactionRow(mockWorksheet, mockTransaction, rowNumber);

    expect(mockWorksheet.getRow).toHaveBeenCalledWith(rowNumber);

    expect(cellValues[1]).toBe("2024-01-15T10:30:00Z"); // CreatedDate
    expect(cellValues[2]).toBe("2024-01-15T11:45:00Z"); // UpdatedDate
    expect(cellValues[3]).toBe(1_234_567_890); // ServiceNumber as number
    expect(cellValues[4]).toBe(100.5); // OriginalAmt
    expect(cellValues[5]).toBe(0); // RefundAmt
    expect(cellValues[6]).toBe(100.5); // FinalAmt
    expect(cellValues[7]).toBe("CAD"); // Currency
    expect(cellValues[8]).toBe("CA"); // Country
    expect(cellValues[9]).toBe("True"); // Billable as string
    expect(cellValues[10]).toBe("ETI"); // Platform
    expect(cellValues[11]).toBe("CUST123"); // CustNumber
    expect(cellValues[12]).toBe("200"); // Rcode as string
    expect(cellValues[13]).toBe("Test Integrator"); // IntegratorName
    expect(cellValues[14]).toBe("Test Program"); // ProgramName
    expect(cellValues[15]).toBe("Test Billing"); // BillingName
    expect(cellValues[16]).toBe("TXN123456"); // TransactionID
    expect(cellValues[17]).toBe("RCP789012"); // ReceiptID
    expect(cellValues[18]).toBe("INT345678"); // InteracRef
    expect(cellValues[19]).toBe("Test Bank"); // FIName
    expect(cellValues[20]).toBe("STATUS_SUCCESS"); // Status
  });
});
