import { existsSync, mkdirSync } from "node:fs";
import path from "node:path";

import { baseDirectoryName } from "@constants/filesystem";
import { type PayInPlatform, payInPlatforms } from "@constants/settlement";
import { type ApprovalCustomerSettlement } from "@lib/customer-settlements/read/type";
import { toDateOnly } from "@utils/date-only";
import { format } from "date-fns";
import ExcelJS from "exceljs";

import { createSummarySheet } from "./helpers/merchant/create-summary-settlement-sheet";
import { createSuccessAndRefundsTransactionsSheets } from "./helpers/merchant/create-transactions-settlement-sheet";
import {
  getTransactions,
  type Transaction,
  transactionStatus,
} from "../../../services/blusky/transactions";
import { type ExcelConfig, type SettlementExcelData } from "../approve/types";
import { getAllSettlementsByCustomerAndPeriod } from "../repository/settlement-frequency";

import type { PrismaClient } from "@prisma/client";

export const generateSettlementExcelFiles = async (
  settlement: ApprovalCustomerSettlement,
  prisma: PrismaClient
): Promise<void> => {
  const settlements = await getAllSettlementsByCustomerAndPeriod(
    settlement.customerId,
    settlement.fromDate!,
    settlement.toDate!,
    prisma
  );

  if (!settlement.customerCustomerType.statementFolderLocation) {
    throw new Error("Statement folder location not configured for customer");
  }

  const baseFolderLocation =
    settlement.customerCustomerType.statementFolderLocation;

  const fullFolderPath = path.join(baseDirectoryName, baseFolderLocation);

  await ensureFolderExists(fullFolderPath);

  const { customerTradingName } = settlement.customer;
  const formattedFromDate = format(settlement.fromDate!, "yyyy-MM-dd");
  const formattedToDate = format(settlement.toDate!, "yyyy-MM-dd");

  const transactions = await getTransactions(
    settlement.customer.serviceNumber,
    {
      fromDate: toDateOnly(settlement.fromDate!),
      toDate: toDateOnly(settlement.toDate!),
    },
    {
      aggregate: {
        [transactionStatus.rejected1]: {
          aggregateForPlatforms: ["ETI", "RFM"],
          targetPlatform: "RTO",
          keepInOriginal: false,
        },
      },
    }
  );

  for (const settlementItem of settlements) {
    const platformCode = Object.keys(settlementItem)[0];

    if (!platformCode) {
      continue;
    }

    const platformData =
      settlementItem[platformCode as keyof typeof settlementItem];

    // For now we only generate Excel for pay-in platforms
    if (!payInPlatforms.includes(platformCode as PayInPlatform)) {
      continue;
    }

    if (!platformData?.labelName) {
      continue;
    }

    const excelConfig: ExcelConfig = {
      platformCode,
      customerTradingName,
      period: {
        fromDate: settlement.fromDate!,
        toDate: settlement.toDate!,
      },
      fileName: `${customerTradingName}-${formattedFromDate}-${formattedToDate}-${platformCode}.xlsx`,
      folderPath: path.join(
        baseDirectoryName,
        baseFolderLocation,
        platformCode
      ),
      sheetName: platformData.labelName ?? `${platformCode} Settlement`,
      data: platformData as SettlementExcelData,
    };

    // eslint-disable-next-line no-await-in-loop
    await generatePlatformExcelFile(
      transactions[platformCode as PayInPlatform],
      excelConfig
    );
  }
};

const generatePlatformExcelFile = async (
  platformTransactions: Transaction[] | undefined,
  config: ExcelConfig
): Promise<void> => {
  const { folderPath, fileName } = config;

  await ensureFolderExists(folderPath);

  const workbook = new ExcelJS.Workbook();

  createSummarySheet(workbook, config);

  createSuccessAndRefundsTransactionsSheets(
    workbook,
    config,
    platformTransactions
  );

  const fullFilePath = path.join(folderPath, fileName);

  await workbook.xlsx.writeFile(fullFilePath);
};

const ensureFolderExists = async (folderPath: string): Promise<void> => {
  if (!existsSync(folderPath)) {
    mkdirSync(folderPath, {
      recursive: true,
    });
  }
};
