import { generateExcel } from "./generate-excel";
import { generateNonMerchantExcel } from "./helpers/generate-non-merchant-excel";

import type { SettlementResult } from "../functions/workers/types";
import type { Customer } from "@lib/customer/repository/types";

vi.mock("./helpers/generate-non-merchant-excel", () => ({
  generateNonMerchantExcel: vi.fn(),
}));

describe("generateExcel", () => {
  const settlementResult = {} as unknown as SettlementResult;
  const fileName = "test-file";

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("should call generateNonMerchantExcel for non-merchant customer", async () => {
    const customer: Customer = {
      customerTypeName: "Integrator",
    } as unknown as Customer;

    await generateExcel(settlementResult, customer, fileName);

    expect(generateNonMerchantExcel).toHaveBeenCalledWith(
      settlementResult,
      customer,
      fileName
    );
  });

  it("should not call generateNonMerchantExcel for merchant customer", async () => {
    const customer: Customer = {
      customerTypeName: "Merchant",
    } as unknown as Customer;

    await generateExcel(settlementResult, customer, fileName);

    expect(generateNonMerchantExcel).not.toHaveBeenCalled();
  });
});
