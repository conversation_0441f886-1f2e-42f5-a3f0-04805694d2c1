import { getSettlementState } from "./settlement-state";

import type { PrismaClient } from "@prisma/client";

describe("getSettlementState", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  const mockPrisma = {
    settlementState: {
      findFirst: vi.fn(),
    },
  } as unknown as PrismaClient;

  it("should return the settlement state when found", async () => {
    const generationType = "INITIAL";
    const generationStatus = "COMPLETE";
    const mockResult = {
      settlementStateId: 1,
      generationType,
      generationStatus,
      stateName: "Approval Pending",
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    };

    vi.mocked(mockPrisma.settlementState.findFirst).mockResolvedValue(
      mockResult
    );

    const result = await getSettlementState(
      generationType,
      generationStatus,
      mockPrisma
    );

    expect(mockPrisma.settlementState.findFirst).toHaveBeenCalledWith({
      where: {
        generationType,
        generationStatus,
      },
    });
    expect(result).toBe(mockResult.stateName);
  });

  it("should return undefined when no settlement state is found", async () => {
    const result = await getSettlementState("INITIAL", "FAILED", mockPrisma);

    expect(mockPrisma.settlementState.findFirst).toHaveBeenCalledWith({
      where: {
        generationType: "INITIAL",
        generationStatus: "FAILED",
      },
    });
    expect(result).toBeUndefined();
  });
});
