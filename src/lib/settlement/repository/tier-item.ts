import { type PrismaClient } from "@prisma/client";

export type TierItem = {
  tierItemId: number;
  fee: number;
  transactionFee: number;
};

export const getTierItemForCount = async (
  prisma: PrismaClient,
  tierSetId: number,
  count: number
): Promise<TierItem | undefined> => {
  let tier;

  const tierItems = await prisma.tierItem.findMany({
    where: { tierSetId },
  });

  for (const item of tierItems) {
    const maxAmount = Number(item.maxAmount);
    const minAmount = Number(item.minAmount);

    if (count < maxAmount && count >= minAmount) {
      tier = {
        fee: Number(item.salesFee),
        tierItemId: item.tierItemId,
        transactionFee: Number(item.transactionFee),
      };
      break;
    }
  }

  return tier;
};
