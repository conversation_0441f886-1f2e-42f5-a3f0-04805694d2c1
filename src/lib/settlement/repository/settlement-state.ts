import { type SettlementState } from "./types";

import type { Prisma, PrismaClient } from "@prisma/client";

const getSettlementState = async (
  generationType: string,
  generationStatus: string,
  prisma: PrismaClient | Prisma.TransactionClient
): Promise<SettlementState | undefined> => {
  const settlementState = await prisma.settlementState.findFirst({
    where: {
      generationType,
      generationStatus,
    },
  });

  if (!settlementState) {
    return;
  }

  return settlementState.stateName as SettlementState;
};

export { getSettlementState };
