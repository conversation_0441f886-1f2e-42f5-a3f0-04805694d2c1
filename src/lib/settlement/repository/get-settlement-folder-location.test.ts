import { getSettlementFolderLocation } from "./get-settlement-folder-location";

describe("getSettlementFolderLocation", () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it("should return the correct folder location for a given customerCustomerTypeId", async () => {
    const mockCustomerCustomerTypeId = 123;
    const mockPrisma = {
      customerCustomerType: {
        findFirst: vi.fn().mockResolvedValue({
          statementFolderLocation: "/mock/path/to/settlement/folder",
        }),
      },
    };

    const result = await getSettlementFolderLocation(
      mockCustomerCustomerTypeId,
      // @ts-expect-error mocking prisma type
      mockPrisma
    );

    expect(mockPrisma.customerCustomerType.findFirst).toHaveBeenCalledWith({
      where: { customerCustomerTypeId: mockCustomerCustomerTypeId },
      select: { statementFolderLocation: true },
    });
    expect(result).toBe("/mock/path/to/settlement/folder");
  });

  it("should return undefined if no customerCustomerType is found", async () => {
    const mockCustomerCustomerTypeId = 456;
    const mockPrisma = {
      customerCustomerType: {
        findFirst: vi.fn().mockResolvedValue(null),
      },
    };

    const result = await getSettlementFolderLocation(
      mockCustomerCustomerTypeId,
      // @ts-expect-error mocking prisma type
      mockPrisma
    );

    expect(mockPrisma.customerCustomerType.findFirst).toHaveBeenCalledWith({
      where: { customerCustomerTypeId: mockCustomerCustomerTypeId },
      select: { statementFolderLocation: true },
    });
    expect(result).toBeUndefined();
  });

  it("should return undefined if the found record has no statementFolderLocation", async () => {
    const mockCustomerCustomerTypeId = 789;
    const mockPrisma = {
      customerCustomerType: {
        findFirst: vi.fn().mockResolvedValue({
          statementFolderLocation: null,
        }),
      },
    };

    const result = await getSettlementFolderLocation(
      mockCustomerCustomerTypeId,
      // @ts-expect-error mocking prisma type
      mockPrisma
    );

    expect(result).toBeUndefined();
  });

  it("should throw an error if customerCustomerTypeId is not provided", async () => {
    const mockPrisma = {
      customerCustomerType: {
        findFirst: vi.fn(),
      },
    };

    await expect(
      getSettlementFolderLocation(
        // @ts-expect-error testing error case
        undefined,
        mockPrisma
      )
    ).rejects.toThrow("customerCustomerType ID is required");

    expect(mockPrisma.customerCustomerType.findFirst).not.toHaveBeenCalled();
  });
});
