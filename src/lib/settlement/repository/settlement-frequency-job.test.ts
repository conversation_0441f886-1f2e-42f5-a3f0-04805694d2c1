import { PrismaClient } from "@prisma/client";

import {
  createFrequencySettlementJob,
  updateFrequencySettlementJob,
  getFrequencySettlementJobSuccess,
  getFrequencySettlementJobInProgress,
  getLatestJobsByFrequency,
} from "./settlement-frequency-job";
import { SettlementStatus } from "./types";

vi.mock("@prisma/client", () => {
  const mockPrismaClient = {
    customerSettlementJob: {
      create: vi.fn(),
      update: vi.fn(),
      findFirst: vi.fn(),
      findMany: vi.fn(),
      groupBy: vi.fn(),
    },
  };

  // eslint-disable-next-line @typescript-eslint/naming-convention
  return { PrismaClient: vi.fn(() => mockPrismaClient) };
});

const prisma = new PrismaClient();

afterEach(() => {
  vi.restoreAllMocks();
});

describe("getLatestJobsByFrequency", () => {
  it("should return the latest successful jobs grouped by frequencyId", async () => {
    const mockLatestDates = [
      { frequencyId: 1, _max: { toDate: new Date("2023-01-31") } },
      { frequencyId: 2, _max: { toDate: new Date("2023-01-15") } },
    ];

    const mockJobs = [
      {
        customerSettlementJobId: 1,
        fromDate: new Date("2023-01-01"),
        toDate: new Date("2023-01-31"),
        status: "SUCCESS",
        frequencyId: 1,
        user: {
          userId: 1,
          userName: "user1",
        },
        statementFrequency: {
          statementFrequencyName: "Monthly",
        },
        createdAt: new Date("2023-02-01"),
      },
      {
        customerSettlementJobId: 3,
        fromDate: new Date("2023-01-01"),
        toDate: new Date("2023-01-15"),
        status: SettlementStatus.SUCCESS,
        frequencyId: 2,
        user: {
          userId: 2,
          userName: "user2",
        },
        statementFrequency: {
          statementFrequencyName: "Semi-Monthly",
        },
        createdAt: new Date("2023-04-01"),
      },
    ];

    prisma.customerSettlementJob.groupBy = vi
      .fn()
      .mockResolvedValue(mockLatestDates);

    prisma.customerSettlementJob.findMany = vi.fn().mockResolvedValue(mockJobs);

    const result = await getLatestJobsByFrequency(prisma);

    expect(prisma.customerSettlementJob.groupBy).toHaveBeenCalledWith({
      by: ["frequencyId"],
      where: {
        status: SettlementStatus.SUCCESS,
        deletedAt: null,
        frequencyId: { not: null },
      },
      _max: {
        toDate: true,
      },
    });

    expect(prisma.customerSettlementJob.findMany).toHaveBeenCalledWith({
      where: {
        status: SettlementStatus.SUCCESS,
        deletedAt: null,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        OR: [
          { frequencyId: 1, toDate: new Date("2023-01-31") },
          { frequencyId: 2, toDate: new Date("2023-01-15") },
        ],
      },
      select: {
        customerSettlementJobId: true,
        fromDate: true,
        toDate: true,
        status: true,
        frequencyId: true,
        user: {
          select: {
            userId: true,
            userName: true,
          },
        },
        statementFrequency: {
          select: {
            statementFrequencyName: true,
          },
        },
        createdAt: true,
      },
    });

    expect(result).toEqual({
      // eslint-disable-next-line @typescript-eslint/naming-convention
      1: mockJobs[0], // Job for frequencyId 1
      // eslint-disable-next-line @typescript-eslint/naming-convention
      2: mockJobs[1], // Job for frequencyId 2
    });
  });

  it("should return an empty object when no jobs are found", async () => {
    prisma.customerSettlementJob.groupBy = vi.fn().mockResolvedValue([]);

    prisma.customerSettlementJob.findMany = vi.fn().mockResolvedValue([]);

    const result = await getLatestJobsByFrequency(prisma);

    expect(result).toEqual({});
  });
});

describe("createFrequencySettlementJob", () => {
  it("should create a new frequency settlement job", async () => {
    const period = { fromDate: new Date(), toDate: new Date() };
    const frequencyId = 2;
    const userId = 1;
    const jobId = 1;

    vi.mocked(prisma.customerSettlementJob.create).mockResolvedValue({
      customerSettlementJobId: jobId,
      fromDate: period.fromDate,
      toDate: period.toDate,
      status: SettlementStatus.PROGRESS,
      frequencyId,
      userId,
      meta: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    });

    const result = await createFrequencySettlementJob(
      period,
      frequencyId,
      userId,
      prisma
    );

    expect(result).toBe(jobId);
    expect(prisma.customerSettlementJob.create).toHaveBeenCalledWith({
      data: {
        fromDate: period.fromDate,
        toDate: period.toDate,
        frequencyId,
        userId,
      },
    });
  });
});

describe("updateFrequencySettlementJob", () => {
  it("should update the frequency settlement job", async () => {
    const jobId = 1;
    const meta = { timeTakenInSec: 120 };
    const status = SettlementStatus.SUCCESS;

    vi.mocked(prisma.customerSettlementJob.update).mockResolvedValue({
      customerSettlementJobId: jobId,
      fromDate: new Date(),
      toDate: new Date(),
      status,
      frequencyId: 2,
      userId: 1,
      meta,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    });

    const result = await updateFrequencySettlementJob(
      jobId,
      meta,
      status,
      prisma
    );

    expect(result).toBe(jobId);
    expect(prisma.customerSettlementJob.update).toHaveBeenCalledWith({
      where: { customerSettlementJobId: jobId },
      data: {
        status,
        meta: JSON.stringify(meta),
      },
    });
  });
});

describe("getFrequencySettlementJobSuccess", () => {
  it("should return a successful frequency settlement job", async () => {
    const fromDate = new Date();
    const toDate = new Date();
    const job = {
      customerSettlementJobId: 1,
      fromDate,
      toDate,
      status: SettlementStatus.SUCCESS,
      frequencyId: 2,
      userId: 1,
      user: { userId: 1, userName: "testUser" },
      statementFrequency: { statementFrequencyName: "Monthly" },
      meta: { timeTakenInSec: 120 },
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    };

    vi.mocked(prisma.customerSettlementJob.findFirst).mockResolvedValue(job);

    const result = await getFrequencySettlementJobSuccess(
      fromDate,
      toDate,
      prisma
    );

    expect(result).toEqual({
      jobId: job.customerSettlementJobId,
      fromDate: job.fromDate,
      toDate: job.toDate,
      status: job.status,
      userId: job.user.userId,
      userName: job.user.userName,
      frequencyName: job.statementFrequency.statementFrequencyName,
      createdAt: job.createdAt,
    });
    expect(prisma.customerSettlementJob.findFirst).toHaveBeenCalledWith({
      where: { fromDate, toDate, status: SettlementStatus.SUCCESS },
      select: {
        customerSettlementJobId: true,
        fromDate: true,
        toDate: true,
        status: true,
        user: { select: { userId: true, userName: true } },
        statementFrequency: { select: { statementFrequencyName: true } },
        createdAt: true,
      },
    });
  });
});

describe("getFrequencySettlementJobInProgress", () => {
  it("should return a frequency settlement job in progress", async () => {
    const job = {
      customerSettlementJobId: 1,
      fromDate: new Date(),
      toDate: new Date(),
      status: SettlementStatus.PROGRESS,
      frequencyId: 2,
      userId: 1,
      user: { userId: 1, userName: "testUser" },
      statementFrequency: { statementFrequencyName: "Monthly" },
      meta: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    };

    vi.mocked(prisma.customerSettlementJob.findFirst).mockResolvedValue(job);

    const result = await getFrequencySettlementJobInProgress(prisma);

    expect(result).toEqual({
      jobId: job.customerSettlementJobId,
      fromDate: job.fromDate,
      toDate: job.toDate,
      status: job.status,
      userId: job.user.userId,
      userName: job.user.userName,
      frequencyName: job.statementFrequency.statementFrequencyName,
      createdAt: job.createdAt,
    });
    expect(prisma.customerSettlementJob.findFirst).toHaveBeenCalledWith({
      where: { status: SettlementStatus.PROGRESS },
      select: {
        customerSettlementJobId: true,
        fromDate: true,
        toDate: true,
        status: true,
        user: { select: { userId: true, userName: true } },
        statementFrequency: { select: { statementFrequencyName: true } },
        createdAt: true,
      },
    });
  });
});
