import { type Prisma, type PrismaClient } from "@prisma/client";
import { endOfMonth } from "date-fns";

import { updateMonthlySettlementJob } from "./settlement-monthly-job";
import { type SettlementsResult } from "./types";
import {
  type SettlementResult,
  type NonMerchantFeesBreakdown,
} from "../functions/workers/types";

const updateMonthlyCustomerSettlement = async (
  monthlyCustomerSettlementId: number,
  settlementResult: {
    status: "SUCCESS" | "ERROR" | "SKIPPED";
    message?: string;
    data?: { result: SettlementResult; timeTakenInSeconds: number };
  },
  previousStatus: "SUCCESS" | "ERROR" | "SKIPPED",
  prisma: PrismaClient
) => {
  await prisma.$transaction(
    async (tx) => {
      const record = await tx.monthlyCustomerSettlement.findUnique({
        where: {
          monthlyCustomerSettlementId,
        },
        select: {
          customerCustomerTypeId: true,
          month: true,
          year: true,
          jobId: true,
        },
      });

      if (!record) {
        throw new Error("Record not found");
      }

      if (!record.jobId) {
        throw new Error("Record does not have a correlated job");
      }

      const { month, year, customerCustomerTypeId } = record;

      const adjustmentTotal = await _getAdjustmentsTotal(
        Number(customerCustomerTypeId),
        month,
        year,
        tx
      );

      // Update the monthly customer settlement
      const settlement = await tx.monthlyCustomerSettlement.update({
        where: {
          monthlyCustomerSettlementId,
        },
        data: {
          customerCustomerTypeId: Number(customerCustomerTypeId),
          status: settlementResult.status,
          adjustmentTotal: adjustmentTotal["SUMMARY"] ?? 0,
          meta: {
            message: settlementResult?.message,
            timeTakenInSeconds: settlementResult?.data?.timeTakenInSeconds,
          },
        },
      });

      if (previousStatus !== settlementResult.status) {
        const statuses = {
          previous: previousStatus,
          current: settlementResult.status,
        };
        // Update the record for the entire month
        await updateRecordCountOnMonthlyAllSettlementJob(
          tx,
          record.jobId,
          statuses,
          prisma
        );
      }

      if (settlement) {
        await tx.monthlyPlatformSettlement.deleteMany({
          where: {
            monthlyCustomerSettlementId,
          },
        });
      }

      if (settlementResult?.data?.result) {
        await _savePlatformSettlements(
          monthlyCustomerSettlementId,
          settlementResult.data.result,
          adjustmentTotal,
          tx
        );
      }
    },
    { timeout: 10_000, maxWait: 5000 }
  );
};

const saveMonthlySettlements = async (
  jobId: number,
  period: { month: number; year: number },
  settlements: SettlementsResult,
  prisma: PrismaClient
) => {
  const { month, year } = period;

  for (const [customerCustomerTypeId, result] of Object.entries(settlements)) {
    const merchantSettlement = result.data?.result;
    // eslint-disable-next-line no-await-in-loop
    await prisma.$transaction(
      async (transactionPrisma) => {
        const adjustmentTotal = await _getAdjustmentsTotal(
          Number(customerCustomerTypeId),
          month,
          year,
          transactionPrisma
        );

        // Create the monthly customer settlement
        const settlement =
          await transactionPrisma.monthlyCustomerSettlement.create({
            data: {
              customerCustomerTypeId: Number(customerCustomerTypeId),
              status: result.status,
              adjustmentTotal: adjustmentTotal["SUMMARY"] ?? 0,
              month,
              year,
              jobId,
              meta: {
                message: result?.message,
                timeTakenInSeconds: result?.data?.timeTakenInSeconds,
              },
            },
          });

        if (merchantSettlement) {
          await _savePlatformSettlements(
            settlement.monthlyCustomerSettlementId,
            merchantSettlement,
            adjustmentTotal,
            transactionPrisma
          );
        }
      },
      { timeout: 50_000, maxWait: 10_000 }
    );
  }
};

const _savePlatformSettlements = async (
  monthlyCustomerSettlementId: number,
  merchantSettlement: SettlementResult,
  adjustmentTotal: Record<string, number>,
  transactionPrisma: Prisma.TransactionClient
) => {
  const platformSettlements = Object.entries(merchantSettlement.settlement).map(
    async ([platformCode, platformSettlement]) => {
      return transactionPrisma.monthlyPlatformSettlement.create({
        data: {
          monthlyCustomerSettlementId,
          transactionFeeTotal:
            platformSettlement.totalChargedFees.transactionFeeTotal,
          salesFeeTotal: platformSettlement.totalChargedFees.salesFeeTotal,
          refundFeeTotal: platformSettlement.totalChargedFees.refundFeeTotal,
          rejected1FeeTotal:
            platformSettlement.totalChargedFees.rejected1FeeTotal,
          minimumFeeTotal: platformSettlement.totalChargedFees.minimumFeeTotal,
          partialReturnFeeTotal:
            platformSettlement.totalChargedFees.partialReturnFeeTotal,
          commissionTotal:
            (platformSettlement?.feesBreakdown as NonMerchantFeesBreakdown)
              ?.totalCommission ?? 0,
          isContractChanged: platformSettlement.isContractChange,
          settlementMeta: {
            feesBreakdown: platformSettlement.feesBreakdown,
            totalTransactionSummary: platformSettlement.totalTransactionSummary,
          },
          adjustmentTotal: adjustmentTotal[platformCode] ?? 0,
          platformId: platformSettlement.platformId,
          ...(platformSettlement.trueUp && {
            trueUpMeta: platformSettlement.trueUp,
          }),
        },
      });
    }
  );
  await Promise.all(platformSettlements);
};

const _getAdjustmentsTotal = async (
  customerCustomerTypeId: number,
  month: number,
  year: number,
  prisma: Prisma.TransactionClient
): Promise<Record<string, number>> => {
  const startOfMonthDate = new Date(year, month - 1, 1);
  const endOfMonthDate = endOfMonth(startOfMonthDate);

  const settlements = await prisma.customerSettlements.findMany({
    where: {
      deletedAt: null,
      customerCustomerTypeId,
      status: "APPROVED",
      toDate: {
        gte: startOfMonthDate,
        lte: endOfMonthDate,
      },
    },
    select: {
      customerId: true,
      platformId: true,
      platform: {
        select: {
          platformCode: true,
          platformId: true,
        },
      },
      customerSettlementAdjustments: {
        where: { deletedAt: null },
        select: { amount: true },
      },
    },
  });

  const adjustmentTotals: Record<string, number> = {};

  for (const settlement of settlements) {
    for (const adjustment of settlement.customerSettlementAdjustments) {
      const { platformCode } = settlement.platform;

      adjustmentTotals[platformCode] ||= 0;

      if (adjustment.amount) {
        adjustmentTotals[platformCode] += adjustment.amount.toNumber() ?? 0;
      }
    }
  }

  return adjustmentTotals;
};

async function updateRecordCountOnMonthlyAllSettlementJob(
  transaction: Prisma.TransactionClient,
  jobId: number,
  statuses: {
    previous: "SUCCESS" | "ERROR" | "SKIPPED";
    current: "SUCCESS" | "ERROR" | "SKIPPED";
  },
  prisma: PrismaClient
) {
  const entireMonthRecord =
    await transaction.monthlyAllSettlementJob.findUnique({
      where: {
        monthlyAllSettlementJobId: jobId,
      },
      select: {
        status: true,
        meta: true,
      },
    });

  if (!entireMonthRecord) {
    throw new Error("Job record not found");
  }

  if (entireMonthRecord.status === "ERROR") {
    throw new Error("Job record found is with an error");
  } else if (entireMonthRecord.status === "PROGRESS") {
    throw new Error("Job record found is in progress");
  }

  const { meta } = entireMonthRecord as {
    meta: {
      totalCount: number;
      successCount: number;
      errorCount: number;
      skippedCount: number;
      timeTakenInSeconds: number;
    };
    status: "PROGRESS" | "SUCCESS" | "ERROR";
  };

  switch (statuses.previous) {
    case "SUCCESS": {
      meta.successCount -= 1;
      break;
    }

    case "ERROR": {
      meta.errorCount -= 1;

      break;
    }

    case "SKIPPED": {
      meta.skippedCount -= 1;

      break;
    }
    // No default
  }

  switch (statuses.current) {
    case "SUCCESS": {
      meta.successCount += 1;

      break;
    }

    case "ERROR": {
      meta.errorCount += 1;

      break;
    }

    case "SKIPPED": {
      meta.skippedCount += 1;

      break;
    }
    // No default
  }

  await updateMonthlySettlementJob(
    jobId,
    meta,
    entireMonthRecord.status,
    prisma
  );
}

export { saveMonthlySettlements, updateMonthlyCustomerSettlement };
