import { type PrismaClient } from "@prisma/client";

import { SettlementStatus } from "./types";

type FrequencySettlementJob = {
  jobId: number;
  fromDate: Date;
  toDate: Date;
  status: string;
  userId: number;
  userName: string;
  frequencyName: string;
  createdAt: Date;
};

const createFrequencySettlementJob = async (
  period: { fromDate: Date; toDate: Date },
  frequencyId: number,
  userId: number,
  prisma: PrismaClient
): Promise<number> => {
  const job = await prisma.customerSettlementJob.create({
    data: {
      fromDate: period.fromDate,
      toDate: period.toDate,
      frequencyId,
      userId,
    },
  });

  return job.customerSettlementJobId;
};

const updateFrequencySettlementJob = async (
  jobId: number,
  meta: {
    timeTakenInSec: number;
  },
  status: SettlementStatus,
  prisma: PrismaClient
): Promise<number> => {
  const job = await prisma.customerSettlementJob.update({
    where: {
      customerSettlementJobId: jobId,
    },
    data: {
      status,
      meta: JSON.stringify(meta),
    },
  });

  return job.customerSettlementJobId;
};

const getFrequencySettlementJobSuccess = async (
  fromDate: Date,
  toDate: Date,
  prisma: PrismaClient
): Promise<FrequencySettlementJob | undefined> => {
  const job = await prisma.customerSettlementJob.findFirst({
    where: {
      fromDate,
      toDate,
      status: SettlementStatus.SUCCESS,
    },
    select: {
      customerSettlementJobId: true,
      fromDate: true,
      toDate: true,
      status: true,
      user: {
        select: {
          userId: true,
          userName: true,
        },
      },
      statementFrequency: {
        select: {
          statementFrequencyName: true,
        },
      },
      createdAt: true,
    },
  });

  if (!job) {
    return;
  }

  return {
    jobId: job.customerSettlementJobId,
    fromDate: job.fromDate,
    toDate: job.toDate,
    status: job.status,
    userId: job.user!.userId,
    userName: job.user!.userName,
    frequencyName: job.statementFrequency!.statementFrequencyName,
    createdAt: job.createdAt!,
  };
};

const getLatestJobsByFrequency = async (prisma: PrismaClient) => {
  const latestDates = await prisma.customerSettlementJob.groupBy({
    by: ["frequencyId"],
    where: {
      status: SettlementStatus.SUCCESS,
      deletedAt: null,
      frequencyId: { not: null },
    },
    _max: {
      toDate: true,
    },
  });

  const jobs = await prisma.customerSettlementJob.findMany({
    where: {
      status: SettlementStatus.SUCCESS,
      deletedAt: null,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      OR: latestDates.map(({ frequencyId, _max }) => ({
        frequencyId: frequencyId!,
        toDate: _max.toDate!,
      })),
    },
    select: {
      customerSettlementJobId: true,
      fromDate: true,
      toDate: true,
      status: true,
      frequencyId: true,
      user: {
        select: {
          userId: true,
          userName: true,
        },
      },
      statementFrequency: {
        select: {
          statementFrequencyName: true,
        },
      },
      createdAt: true,
    },
  });

  // Convert to record format
  const result: Record<number, (typeof jobs)[0]> = {};

  for (const job of jobs) {
    result[job.frequencyId!] = job;
  }

  return result;
};

const getFrequencySettlementJobInProgress = async (
  prisma: PrismaClient
): Promise<FrequencySettlementJob | undefined> => {
  const job = await prisma.customerSettlementJob.findFirst({
    where: {
      status: "PROGRESS",
    },
    select: {
      customerSettlementJobId: true,
      fromDate: true,
      toDate: true,
      status: true,
      user: {
        select: {
          userId: true,
          userName: true,
        },
      },
      statementFrequency: {
        select: {
          statementFrequencyName: true,
        },
      },
      createdAt: true,
    },
  });

  if (!job) {
    return;
  }

  return {
    jobId: job.customerSettlementJobId,
    fromDate: job.fromDate,
    toDate: job.toDate,
    status: job.status,
    userId: job.user!.userId,
    userName: job.user!.userName,
    frequencyName: job.statementFrequency!.statementFrequencyName,
    createdAt: job.createdAt!,
  };
};

export {
  createFrequencySettlementJob,
  updateFrequencySettlementJob,
  getLatestJobsByFrequency,
  getFrequencySettlementJobSuccess,
  getFrequencySettlementJobInProgress,
};
