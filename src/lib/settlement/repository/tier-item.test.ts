import { describe, expect, it, vi } from "vitest";

import { getTierItemForCount } from "./tier-item";

import type { PrismaClient } from "@prisma/client";

const createFakePrisma = (
  tierItems: Array<{
    tierItemId: number;
    tierSetId: number;
    maxAmount: string;
    minAmount: string;
    salesFee: string;
    transactionFee: string;
  }>
): PrismaClient => {
  return {
    tierItem: {
      findMany: vi.fn().mockResolvedValue(tierItems),
    },
  } as unknown as PrismaClient;
};

const tierItemsData = [
  {
    tierItemId: 1,
    tierSetId: 1,
    minAmount: "0.00",
    maxAmount: "2000000.00",
    salesFee: "0.00300000",
    transactionFee: "0.00000000",
  },
  {
    tierItemId: 2,
    tierSetId: 1,
    minAmount: "2000000.00",
    maxAmount: "4000000.00",
    salesFee: "0.00350000",
    transactionFee: "0.00000000",
  },
  {
    tierItemId: 3,
    tierSetId: 1,
    minAmount: "4000000.00",
    maxAmount: "100000000.00",
    salesFee: "0.00450000",
    transactionFee: "0.00000000",
  },
  {
    tierItemId: 4,
    tierSetId: 2,
    minAmount: "0.00",
    maxAmount: "2000000.00",
    salesFee: "0.06000000",
    transactionFee: "0.00000000",
  },
  {
    tierItemId: 5,
    tierSetId: 2,
    minAmount: "2000000.00",
    maxAmount: "4000000.00",
    salesFee: "0.08000000",
    transactionFee: "0.00000000",
  },
];

describe("getTierItemForCount", () => {
  describe("for tierSetId 1", () => {
    const tierSet1Data = tierItemsData.filter((item) => item.tierSetId === 1);
    const prisma = createFakePrisma(tierSet1Data);

    it("should return tierItemId 1 for count within range [0, 2000000)", async () => {
      const count = 1_000_000;
      const result = await getTierItemForCount(prisma, 1, count);
      expect(result).toEqual({
        tierItemId: 1,
        fee: 0.003,
        transactionFee: 0,
      });
    });

    it("should return tierItemId 2 for count within range [2000000, 4000000)", async () => {
      const count = 2_000_000;
      const result = await getTierItemForCount(prisma, 1, count);
      expect(result).toEqual({
        tierItemId: 2,
        fee: 0.0035,
        transactionFee: 0,
      });
    });

    it("should return tierItemId 3 for count within range [4000000, 100000000)", async () => {
      const count = 4_000_000;
      const result = await getTierItemForCount(prisma, 1, count);
      expect(result).toEqual({
        tierItemId: 3,
        fee: 0.0045,
        transactionFee: 0,
      });
    });

    it("should return undefined when count does not match any tier", async () => {
      const count = 100_000_000;
      const result = await getTierItemForCount(prisma, 1, count);
      expect(result).toBeUndefined();
    });
  });

  describe("for tierSetId 2", () => {
    const tierSet2Data = tierItemsData.filter((item) => item.tierSetId === 2);
    const prisma = createFakePrisma(tierSet2Data);

    it("should return tierItemId 4 for count within range [0, 2000000)", async () => {
      const count = 1_000_000;
      const result = await getTierItemForCount(prisma, 2, count);
      expect(result).toEqual({
        tierItemId: 4,
        fee: 0.06,
        transactionFee: 0,
      });
    });

    it("should return tierItemId 5 for count within range [2000000, 4000000)", async () => {
      const count = 2_000_000;
      const result = await getTierItemForCount(prisma, 2, count);
      expect(result).toEqual({
        tierItemId: 5,
        fee: 0.08,
        transactionFee: 0,
      });
    });

    it("should return undefined when count does not match any tier for tierSetId 2", async () => {
      const count = 4_000_000;
      const result = await getTierItemForCount(prisma, 2, count);
      expect(result).toBeUndefined();
    });
  });

  it("should return undefined when no tierItems are found", async () => {
    const prisma = createFakePrisma([]);
    const count = 1000;
    const result = await getTierItemForCount(prisma, 999, count);
    expect(result).toBeUndefined();
  });
});
