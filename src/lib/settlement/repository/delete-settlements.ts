import type { Prisma } from "@prisma/client";

const getSummarySettlement = async (
  settlementId: number,
  prisma: Prisma.TransactionClient
): Promise<
  | {
      customerId: number;
      fromDate: Date;
      toDate: Date;
    }
  | undefined
> => {
  const settlement = await prisma.customerSettlements.findUnique({
    where: {
      customerSettlementsId: settlementId,
    },
    select: {
      customerId: true,
      fromDate: true,
      toDate: true,
    },
  });

  if (!settlement) {
    return;
  }

  return settlement;
};

const getPlatformSettlements = async (
  customerId: number,
  fromDate: Date,
  toDate: Date,
  prisma: Prisma.TransactionClient
): Promise<number[]> => {
  const settlements = await prisma.customerSettlements.findMany({
    where: {
      customerId,
      fromDate,
      toDate,
    },
    select: {
      customerSettlementsId: true,
    },
  });

  return settlements.map((settlement) => settlement.customerSettlementsId);
};

const getSettlementWires = async (
  settlementIds: number[],
  prisma: Prisma.TransactionClient
): Promise<
  Array<
    Prisma.wireGetPayload<{
      select: {
        wireId: true;
        wireApprovedById: true;
        isCancelled: true;
      };
    }>
  >
> => {
  return prisma.wire.findMany({
    where: {
      settlementId: {
        in: settlementIds,
      },
    },
    select: {
      wireId: true,
      wireApprovedById: true,
      isCancelled: true,
    },
  });
};

const hardDeleteSettlementEmails = async (
  settlementWireIds: number[],
  prisma: Prisma.TransactionClient
): Promise<void> => {
  await prisma.email.deleteMany({
    where: {
      wireId: {
        in: settlementWireIds,
      },
    },
  });
};

const hardDeleteSettlementWires = async (
  settlementWireIds: number[],
  prisma: Prisma.TransactionClient
): Promise<void> => {
  await prisma.wire.deleteMany({
    where: {
      wireId: {
        in: settlementWireIds,
      },
    },
  });
};

const hardDeleteSettlementKycs = async (
  settlementIds: number[],
  prisma: Prisma.TransactionClient
): Promise<void> => {
  await prisma.customerSettlementKyc.deleteMany({
    where: {
      customerSettlementsId: {
        in: settlementIds,
      },
    },
  });
};

const hardDeleteSettlementAdjustments = async (
  settlementIds: number[],
  prisma: Prisma.TransactionClient
): Promise<void> => {
  await prisma.customerSettlementAdjustments.deleteMany({
    where: {
      customerSettlementsId: {
        in: settlementIds,
      },
    },
  });
};

const hardDeleteSettlements = async (
  settlementIds: number[],
  prisma: Prisma.TransactionClient
): Promise<void> => {
  await prisma.customerSettlements.deleteMany({
    where: {
      customerSettlementsId: {
        in: settlementIds,
      },
    },
  });
};

export {
  getSummarySettlement,
  getPlatformSettlements,
  getSettlementWires,
  hardDeleteSettlementEmails,
  hardDeleteSettlementWires,
  hardDeleteSettlementKycs,
  hardDeleteSettlementAdjustments,
  hardDeleteSettlements,
};
