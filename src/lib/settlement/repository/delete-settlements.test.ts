import {
  getSummarySettlement,
  getPlatformSettlements,
  getSettlementWires,
  hardDeleteSettlementEmails,
  hardDeleteSettlementWires,
  hardDeleteSettlementKycs,
  hardDeleteSettlementAdjustments,
  hardDeleteSettlements,
} from "./delete-settlements";

import type { Prisma } from "@prisma/client";

const prisma = {
  customerSettlements: {
    findUnique: vi.fn(),
    findMany: vi.fn(),
    deleteMany: vi.fn(),
  },
  wire: {
    findMany: vi.fn(),
    deleteMany: vi.fn(),
  },
  email: {
    deleteMany: vi.fn(),
  },
  customerSettlementKyc: {
    deleteMany: vi.fn(),
  },
  customerSettlementAdjustments: {
    deleteMany: vi.fn(),
  },
} as unknown as Prisma.TransactionClient;

describe("Delete Settlements Repository", () => {
  afterEach(() => {
    vi.resetAllMocks();
  });

  describe("getSummarySettlement", () => {
    it("returns summary if settlement exists", async () => {
      const summary = {
        customerId: 1,
        fromDate: new Date(),
        toDate: new Date(),
      };

      prisma.customerSettlements.findUnique = vi
        .fn()
        .mockResolvedValue(summary);

      const result = await getSummarySettlement(1, prisma);

      expect(result).toEqual(summary);
    });

    it("returns undefined if settlement does not exist", async () => {
      const result = await getSummarySettlement(1, prisma);

      expect(result).toBeUndefined();
    });
  });

  describe("getPlatformSettlements", () => {
    it("returns array of settlement ids if they exist", async () => {
      prisma.customerSettlements.findMany = vi
        .fn()
        .mockResolvedValue([
          { customerSettlementsId: 1 },
          { customerSettlementsId: 2 },
        ]);

      const result = await getPlatformSettlements(
        1,
        new Date(),
        new Date(),
        prisma
      );

      expect(result).toEqual([1, 2]);
    });

    it("returns empty array if no settlements", async () => {
      prisma.customerSettlements.findMany = vi.fn().mockResolvedValue([]);

      const result = await getPlatformSettlements(
        1,
        new Date(),
        new Date(),
        prisma
      );

      expect(result).toEqual([]);
    });
  });

  describe("getSettlementWires", () => {
    it("returns wires for settlement ids", async () => {
      const wires = [
        { wireId: 1, wireApprovedById: 2, isCancelled: false },
        { wireId: 2, wireApprovedById: 3, isCancelled: true },
      ];

      prisma.wire.findMany = vi.fn().mockResolvedValue(wires);

      const result = await getSettlementWires([1, 2], prisma);

      expect(result).toEqual(wires);
    });

    it("returns empty array if no wires found", async () => {
      prisma.wire.findMany = vi.fn().mockResolvedValue([]);

      const result = await getSettlementWires([1, 2], prisma);

      expect(result).toEqual([]);
    });
  });

  describe("hardDeleteSettlementEmails", () => {
    it("calls deleteMany with correct wireIds", async () => {
      await hardDeleteSettlementEmails([1, 2], prisma);

      expect(prisma.email.deleteMany).toHaveBeenCalledWith({
        where: { wireId: { in: [1, 2] } },
      });
    });
  });

  describe("hardDeleteSettlementWires", () => {
    it("calls deleteMany with correct wireIds", async () => {
      await hardDeleteSettlementWires([1, 2], prisma);

      expect(prisma.wire.deleteMany).toHaveBeenCalledWith({
        where: { wireId: { in: [1, 2] } },
      });
    });
  });

  describe("hardDeleteSettlementKycs", () => {
    it("calls deleteMany with correct settlementIds", async () => {
      await hardDeleteSettlementKycs([1, 2], prisma);

      expect(prisma.customerSettlementKyc.deleteMany).toHaveBeenCalledWith({
        where: { customerSettlementsId: { in: [1, 2] } },
      });
    });
  });

  describe("hardDeleteSettlementAdjustments", () => {
    it("calls deleteMany with correct settlementIds", async () => {
      await hardDeleteSettlementAdjustments([1, 2], prisma);

      expect(
        prisma.customerSettlementAdjustments.deleteMany
      ).toHaveBeenCalledWith({
        where: { customerSettlementsId: { in: [1, 2] } },
      });
    });
  });

  describe("hardDeleteSettlements", () => {
    it("calls deleteMany with correct settlementIds", async () => {
      await hardDeleteSettlements([1, 2], prisma);

      expect(prisma.customerSettlements.deleteMany).toHaveBeenCalledWith({
        where: { customerSettlementsId: { in: [1, 2] } },
      });
    });
  });
});
