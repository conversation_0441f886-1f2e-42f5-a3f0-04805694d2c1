import { type Prisma, type PrismaClient } from "@prisma/client";

const getAllSettlementJobs = async (
  prisma: PrismaClient,
  offset = 0,
  limit = 20
): Promise<
  | {
      jobs: Array<{
        jobId: number;
        toDate: Date;
        fromDate: Date;
        status: string;
        userId: number | undefined;
        userName: string | undefined;
        generatedAt: Date;
        meta: Prisma.JsonValue;
      }>;
      totalCount: number;
    }
  | undefined
> => {
  const [jobs, totalCount] = await prisma.$transaction(async (tx) => {
    const jobs = await tx.customerSettlementJob.findMany({
      where: {
        deletedAt: null,
      },
      skip: offset,
      take: limit,
      select: {
        customerSettlementJobId: true,
        fromDate: true,
        toDate: true,
        status: true,
        createdAt: true,
        user: {
          select: {
            userId: true,
            userName: true,
          },
        },
        meta: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    const count = await tx.customerSettlementJob.count({
      where: {
        deletedAt: null,
      },
    });

    return [jobs, count];
  });

  if (!jobs) {
    return;
  }

  const flattenedResult = jobs.map((job) => ({
    jobId: job.customerSettlementJobId,
    fromDate: job.fromDate,
    toDate: job.toDate,
    status: job.status,
    userId: job.user?.userId ?? undefined,
    userName: job.user?.userName ?? undefined,
    generatedAt: job.createdAt!,
    meta: JSON.parse(job.meta as string) as Prisma.JsonValue,
  }));

  return {
    jobs: flattenedResult,
    totalCount,
  };
};

export { getAllSettlementJobs };
