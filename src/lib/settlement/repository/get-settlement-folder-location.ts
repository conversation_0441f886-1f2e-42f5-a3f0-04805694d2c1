import { type Prisma, type PrismaClient } from "@prisma/client";

async function getSettlementFolderLocation(
  customerCustomerTypeId: number,
  prisma: PrismaClient | Prisma.TransactionClient
): Promise<string | undefined> {
  if (customerCustomerTypeId === undefined || customerCustomerTypeId === null) {
    throw new Error("customerCustomerType ID is required");
  }

  const customerCustomerType = await prisma.customerCustomerType.findFirst({
    where: {
      customerCustomerTypeId,
    },
    select: {
      statementFolderLocation: true,
    },
  });

  return customerCustomerType?.statementFolderLocation ?? undefined;
}

export { getSettlementFolderLocation };
