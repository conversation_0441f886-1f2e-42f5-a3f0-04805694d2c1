/* eslint-disable @typescript-eslint/naming-convention */
const selectClause = (startDate: Date, endDate: Date) => {
  return {
    select: {
      customerCustomerTypeId: true,
      includeIDPTierSet: true,
      includeACHTierSet: true,
      includeRTOTierSet: true,
      statementFolderLocation: true,
      customerType: {
        select: {
          customerTypeId: true,
          customerTypeName: true,
        },
      },
      volumeCombination: {
        take: 2,
        select: {
          fromDate: true,
          paymentType: {
            select: {
              paymentTypeName: true,
            },
          },
          volumeCombinationPlatform: {
            select: {
              enabled: true,
              platform: {
                select: {
                  platformCode: true,
                  platformName: true,
                },
              },
            },
          },
        },
        orderBy: {
          fromDate: "desc" as const,
        },
        where: {
          deletedAt: null,
          fromDate: {
            lte: endDate,
          },
        },
      },
      statementFrequency: {
        select: {
          statementFrequencyId: true,
          statementFrequencyName: true,
          statementFrequencyCode: true,
        },
      },
      customerCustomerTypeServiceNumber: {
        where: {
          deletedAt: null,
          AND: [
            {
              fromDate: {
                lte: endDate,
              },
            },
            {
              OR: [
                {
                  toDate: {
                    gte: startDate,
                  },
                },
                {
                  toDate: null,
                },
              ],
            },
          ],
        },
      },
      customer: {
        select: {
          customerId: true,
          serviceNumber: true,
          customerName: true,
          customerTradingName: true,
          multipleServiceNoTierSet: true,
          multipleServiceNoTierSetKyc: true,
          firstTransactionDate: true,
          customerSettlementType: {
            select: {
              description: true,
            },
          },
          merchantPlatform_merchantPlatform_clientCustomerIdTocustomer: {
            where: {
              deletedAt: null,
              AND: [
                {
                  fromDate: {
                    lte: endDate,
                  },
                },
                {
                  OR: [
                    {
                      toDate: {
                        gte: startDate,
                      },
                    },
                    {
                      toDate: null,
                    },
                  ],
                },
              ],
            },
            select: {
              merchantPlatformId: true,
              clientDelayMonths: true,
              clientTransactionFee: true,
              clientGatewayFee: true,
              clientRefundFee: true,
              hasMinimum: true,
              minimumThreshold: true,
              minimumCharge: true,
              fromDate: true,
              toDate: true,
              platform: {
                select: {
                  platformId: true,
                  platformName: true,
                  platformCode: true,
                  settlementDescription: true,
                  displaySequence: true,
                  paymentType: {
                    select: {
                      paymentTypeName: true,
                    },
                  },
                },
              },
              tierSet_merchantPlatform_clientTierSetIdTotierSet: {
                select: {
                  tierSetId: true,
                  tierSetName: true,
                  tierItem: true,
                },
              },
              tierSet_merchantPlatform_rejectOneTierSetIdTotierSet: {
                select: {
                  tierSetId: true,
                  tierSetName: true,
                  tierItem: true,
                },
              },
              tierItem: true,
              integratorCustomerId: true,
              tierSet_merchantPlatform_integratorTierSetIdTotierSet: {
                select: {
                  tierSetId: true,
                  tierSetName: true,
                  tierItem: true,
                },
              },
              tierSet_merchantPlatform_integratorSaleTierSetIdTotierSet: {
                select: {
                  tierSetId: true,
                  tierSetName: true,
                  tierItem: true,
                },
              },
              integratorCumulativeOrSplit: true,
              integratorGatewayFee: true,
              agentCustomerId: true,
              tierSet_merchantPlatform_agentTierSetIdTotierSet: {
                select: {
                  tierSetId: true,
                  tierSetName: true,
                  tierItem: true,
                },
              },
              tierSet_merchantPlatform_agentSaleTierSetIdTotierSet: {
                select: {
                  tierSetId: true,
                  tierSetName: true,
                  tierItem: true,
                },
              },
              agentCumulativeOrSplit: true,
              agentGatewayFee: true,
              subAgentCustomerId: true,
              tierSet_merchantPlatform_subAgentTierSetIdTotierSet: {
                select: {
                  tierSetId: true,
                  tierSetName: true,
                  tierItem: true,
                },
              },
              tierSet_merchantPlatform_subAgentSaleTierSetIdTotierSet: {
                select: {
                  tierSetId: true,
                  tierSetName: true,
                  tierItem: true,
                },
              },
              subAgentCumulativeOrSplit: true,
              subAgentGatewayFee: true,
            },
          },
        },
      },
    },
  };
};

export { selectClause };
