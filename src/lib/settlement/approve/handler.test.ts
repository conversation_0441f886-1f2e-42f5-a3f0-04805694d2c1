import { type PrismaClient } from "@prisma/client";
import { type FastifyReply, type FastifyRequest } from "fastify";
import { beforeEach, describe, expect, it, vi } from "vitest";

import { handler } from "./handler";
import * as ApproveService from "./service";
import { type ApproveParameters } from "./types";

type MockPrisma = {
  $transaction: ReturnType<typeof vi.fn>;
};

type MockFastifyRequest = FastifyRequest<{ Body: ApproveParameters }> & {
  server: {
    prisma: PrismaClient & MockPrisma;
  };
  userProfile: {
    id: number;
  };
  log: {
    error: ReturnType<typeof vi.fn>;
  };
};

describe("approve settlement handler", () => {
  let mockRequest: MockFastifyRequest;
  let mockReply: FastifyReply;

  beforeEach(() => {
    mockRequest = {
      body: {
        serviceNumber: "SN123456",
        fromDate: "2025-01-01",
        toDate: "2025-01-31",
        customerCustomerTypeId: 1,
        customerSettlementsId: 100,
        netPayout: 1000.5,
        userId: 1,
      },
      server: {
        prisma: {
          $transaction: vi.fn(),
        } as unknown as PrismaClient & MockPrisma,
      },
      userProfile: {
        id: 123,
      },
      log: {
        error: vi.fn(),
      },
    } as unknown as MockFastifyRequest;

    mockReply = {
      send: vi.fn(),
      code: vi.fn().mockReturnThis(),
    } as unknown as FastifyReply;
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it("should successfully approve settlement and return 200", async () => {
    const spyApproveSettlement = vi
      .spyOn(ApproveService, "approveSettlement")
      .mockResolvedValue({
        success: true,
        message: "Settlement approved successfully",
        scheduleId: 123,
      });

    await handler(mockRequest, mockReply);

    expect(spyApproveSettlement).toHaveBeenCalledWith(
      mockRequest.server.prisma,
      {
        serviceNumber: "SN123456",
        fromDate: "2025-01-01",
        toDate: "2025-01-31",
        customerCustomerTypeId: 1,
        customerSettlementsId: 100,
        netPayout: 1000.5,
        userId: 123,
      }
    );

    expect(mockReply.code).toHaveBeenCalledWith(200);
    expect(mockReply.send).toHaveBeenCalledWith();
  });

  it("should return 404 when settlement is not found", async () => {
    const errorMessage = "settlement was not found for the given parameters";
    const spyApproveSettlement = vi
      .spyOn(ApproveService, "approveSettlement")
      .mockRejectedValue(new Error(errorMessage));

    await handler(mockRequest, mockReply);

    expect(spyApproveSettlement).toHaveBeenCalled();
    expect(mockRequest.log.error).toHaveBeenCalledWith(new Error(errorMessage));
    expect(mockReply.code).toHaveBeenCalledWith(404);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: "Settlement not found.",
    });
  });

  it("should return 500 for other errors", async () => {
    const errorMessage = "Database connection failed";
    const spyApproveSettlement = vi
      .spyOn(ApproveService, "approveSettlement")
      .mockRejectedValue(new Error(errorMessage));

    await handler(mockRequest, mockReply);

    expect(spyApproveSettlement).toHaveBeenCalled();
    expect(mockRequest.log.error).toHaveBeenCalledWith(new Error(errorMessage));
    expect(mockReply.code).toHaveBeenCalledWith(500);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: errorMessage,
    });
  });

  it("should handle partial settlement not found error message", async () => {
    const errorMessage = "The settlement was not found in the database";
    const spyApproveSettlement = vi
      .spyOn(ApproveService, "approveSettlement")
      .mockRejectedValue(new Error(errorMessage));

    await handler(mockRequest, mockReply);

    expect(spyApproveSettlement).toHaveBeenCalled();
    expect(mockRequest.log.error).toHaveBeenCalledWith(new Error(errorMessage));
    expect(mockReply.code).toHaveBeenCalledWith(404);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: "Settlement not found.",
    });
  });

  it("should use userProfile.id instead of body.userId", async () => {
    const spyApproveSettlement = vi
      .spyOn(ApproveService, "approveSettlement")
      .mockResolvedValue({
        success: true,
        message: "Settlement approved successfully",
        scheduleId: 123,
      });

    mockRequest.body.userId = 999;

    await handler(mockRequest, mockReply);

    expect(spyApproveSettlement).toHaveBeenCalledWith(
      mockRequest.server.prisma,
      expect.objectContaining({
        userId: 123,
      })
    );
  });

  it("should pass all parameters correctly to approveSettlement", async () => {
    const spyApproveSettlement = vi
      .spyOn(ApproveService, "approveSettlement")
      .mockResolvedValue({
        success: true,
        message: "Settlement approved successfully",
        scheduleId: 123,
      });

    await handler(mockRequest, mockReply);

    expect(spyApproveSettlement).toHaveBeenCalledWith(
      mockRequest.server.prisma,
      {
        serviceNumber: "SN123456",
        fromDate: "2025-01-01",
        toDate: "2025-01-31",
        customerCustomerTypeId: 1,
        customerSettlementsId: 100,
        netPayout: 1000.5,
        userId: 123,
      }
    );
  });
});
