export type ApproveParameters = {
  serviceNumber: string;
  fromDate: string;
  toDate: string;
  customerCustomerTypeId: number;
  customerSettlementsId: number;
  netPayout: number;
  userId: number;
};

/**
 * Excel generation configuration for each platform
 */
export type ExcelConfig = {
  platformCode: string;
  customerTradingName: string;
  period: {
    fromDate: Date;
    toDate: Date;
  };
  fileName: string;
  folderPath: string;
  sheetName: string;
  data: SettlementExcelData;
};

export type SettlementExcelData = {
  labelName: string;
  transactionCount: number;
  totalTransactionAmount: number;
  refundCount: number;
  totalRefundAmount: number;
  gatewayFee: number;
  transactionFee: number;
  salesFee: number;
  refundFee: number;
  totalFailedAmount: number;
  endBalance: number;
  total2FaRejectAmount: number;
  total2FaRejectCount: number;
  txnAmountRTO_R: number;
  txnCountETI_R1: number;
  minimumFeeTotal: number;
  minimumFeeCount: number;
  totalMinimumAmount: number;
  partialReturnAmountRTO: number;
  partialReturnCountRTO: number;
  isAdjusted: boolean;
  adjustments: Array<{
    label: string;
    amount: number;
    displayCommentExcel: boolean;
    comment: string;
  }>;
};
