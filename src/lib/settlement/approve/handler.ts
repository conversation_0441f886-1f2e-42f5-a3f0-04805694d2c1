import { approveSettlement } from "@lib/settlement/approve/service";
import { type ApproveParameters } from "@lib/settlement/approve/types";

import type { FastifyReply, FastifyRequest } from "fastify";

export const handler = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const body = request.body as ApproveParameters;

    const approveParameters = {
      serviceNumber: body.serviceNumber,
      fromDate: body.fromDate,
      toDate: body.toDate,
      customerCustomerTypeId: body.customerCustomerTypeId,
      customerSettlementsId: body.customerSettlementsId,
      netPayout: body.netPayout,
      userId: request.userProfile.id,
    };

    await approveSettlement(request.server.prisma, approveParameters);

    return await reply.code(200).send();
  } catch (error) {
    request.log.error(error);

    if ((error as Error).message.includes("settlement was not found")) {
      return reply.code(404).send({
        message: "Settlement not found.",
      });
    }

    await reply.code(500).send({
      message: (error as Error).message,
    });
  }
};
