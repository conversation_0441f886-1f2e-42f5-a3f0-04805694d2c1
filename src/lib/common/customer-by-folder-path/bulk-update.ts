import { type Prisma, type PrismaClient } from "@prisma/client";

export async function bulkUpdateStatementFolderLocation(
  prisma: PrismaClient | Prisma.TransactionClient,
  updates: Array<{
    customerCustomerTypeId: number;
    statementFolderLocation: string;
  }>
): Promise<void> {
  if (updates.length === 0) {
    return;
  }

  const selectStatements: string[] = [];
  const parameters: Array<number | string> = [];

  for (const update of updates) {
    selectStatements.push(
      "SELECT ? AS customerCustomerTypeId, ? AS statementFolderLocation"
    );
    parameters.push(
      update.customerCustomerTypeId,
      update.statementFolderLocation
    );
  }

  const query = `
    UPDATE \`customerCustomerType\` AS c
    JOIN (${selectStatements.join("\n UNION ALL\n ")}) AS v
    ON c.\`customerCustomerTypeId\` = v.customerCustomerTypeId
    SET c.\`statementFolderLocation\` = v.statementFolderLocation;
  `;

  // A raw query is used here for performance reasons. Doing this through the ORM would result in
  // multiple queries being sent to the database, which is time consuming.
  // According to Prisma documentation, parameterized queries are safe from SQL injection.
  // https://www.prisma.io/docs/orm/prisma-client/using-raw-sql/raw-queries#parameterized-queries
  await prisma.$executeRawUnsafe(query, ...parameters);
}
