import { bulkUpdateStatementFolderLocation } from "@lib/common/customer-by-folder-path/bulk-update";
import { type PrismaClient, type Prisma } from "@prisma/client";

const getCustomersFromDatabaseByFolderPath = async (
  prisma: PrismaClient | Prisma.TransactionClient,
  folderPath: string
): Promise<
  Array<{
    customerId: number;
    name: string;
    serviceNumber: string;
    customerCustomerTypeId: number;
    statementFolderLocation: string;
    customerType: string;
  }>
> => {
  const customers = await prisma.customerCustomerType.findMany({
    where: {
      statementFolderLocation: {
        startsWith: folderPath,
      },
    },
    select: {
      customerCustomerTypeId: true,
      statementFolderLocation: true,
      customer: {
        select: {
          customerId: true,
          customerName: true,
          serviceNumber: true,
        },
      },
      customerType: {
        select: {
          customerTypeName: true,
        },
      },
    },
  });

  if (customers.length > 0) {
    return customers.map((customer) => ({
      customerId: customer.customer?.customerId,
      name: customer.customer?.customerName,
      serviceNumber: customer.customer?.serviceNumber,
      customerCustomerTypeId: customer.customerCustomerTypeId,
      statementFolderLocation: customer.statementFolderLocation ?? "",
      customerType: customer.customerType?.customerTypeName,
    }));
  }

  return [];
};

const updateFolderPathInDatabase = async (
  prisma: PrismaClient | Prisma.TransactionClient,
  oldFolderPath: string,
  newFolderPath: string
): Promise<boolean> => {
  const recordsToUpdate = await getCustomersFromDatabaseByFolderPath(
    prisma,
    oldFolderPath
  );

  // If no records are found, return false
  if (recordsToUpdate.length === 0) {
    return false;
  }

  const updates = recordsToUpdate.map((record) => {
    const currentFolderPath = record.statementFolderLocation;

    // Replace only the prefix of the folder path
    const updatedFolderPath = _replacePrefix(
      currentFolderPath,
      oldFolderPath,
      newFolderPath
    );

    return {
      customerCustomerTypeId: record.customerCustomerTypeId,
      statementFolderLocation: updatedFolderPath,
    };
  });

  await bulkUpdateStatementFolderLocation(prisma, updates);

  return true;
};

/**
 * Replaces the starting prefix of a given string with a new replacement,
 * if the string begins with the specified prefix. Only the leading part
 * that matches the prefix is replaced, leaving any following segments intact.
 *
 * @param {string} filePath - The original string, potentially containing the prefix.
 * @param {string} prefix - The prefix to be replaced if it matches the start of the string.
 * @param {string} replacement - The new string to replace the prefix with.
 * @returns {string} - The modified string with the prefix replaced,
 * or the original string if no match.
 *
 * @example
 * // Replaces "/a/b" in the path "/a/b/c/d" with "/a/e"
 * replacePrefix("/a/b/c/d", "/a/b", "/a/e");
 * // Returns: "/a/e/c/d"
 *
 * @example
 * // Exact match, replaces "/a/b" entirely with "/a/e"
 * replacePrefix("/a/b", "/a/b", "/a/e");
 * // Returns: "/a/e"
 *
 * @example
 * // No match; "/a/b1/c/d" does not start with "/a/b"
 * replacePrefix("/a/b1/c/d", "/a/b", "/a/e");
 * // Returns: "/a/b1/c/d"
 */

function _replacePrefix(
  filePath: string,
  prefix: string,
  replacement: string
): string {
  if (filePath.startsWith(prefix)) {
    return replacement + filePath.slice(prefix.length);
  }

  return filePath;
}

export { getCustomersFromDatabaseByFolderPath, updateFolderPathInDatabase };
