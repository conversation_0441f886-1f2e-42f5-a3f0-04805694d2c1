import { type PrismaClient, type Prisma } from "@prisma/client";

import {
  getCustomersFromDatabaseByFolderPath,
  updateFolderPathInDatabase,
} from "./repository";

/**
 * Retrieves all customers associated with the given folder path.
 * If the folder path exists in the database, it returns an array of customer information.
 * If no customers are associated with the folder path, it returns an empty array.
 *
 * @param prisma - The Prisma client instance used to interact with the database.
 * @param folderPath - The folder path to check for associated customers.
 * @returns An array of all customers information associated with the folder path,
 * or an empty array if none are found.
 */
const getCustomersByFolderPath = async (
  prisma: PrismaClient | Prisma.TransactionClient,
  folderPath: string
): Promise<
  Array<{
    customerId: number;
    name: string;
    serviceNumber: string;
    statementFolderLocation: string;
    customerType: string;
  }>
> => {
  // Ensure folderPath starts with '/'
  const folderPathWithSlash = _ensureLeadingSlash(folderPath);

  return getCustomersFromDatabaseByFolderPath(prisma, folderPathWithSlash);
};

const updateFolderPathForAllCustomers = async (
  prisma: PrismaClient | Prisma.TransactionClient,
  oldFolderPath: string,
  newFolderPath: string
): Promise<boolean> => {
  // Ensure folderPath starts with '/'
  const oldFolderPathWithSlash = _ensureLeadingSlash(oldFolderPath);
  const newFolderPathWithSlash = _ensureLeadingSlash(newFolderPath);

  return updateFolderPathInDatabase(
    prisma,
    oldFolderPathWithSlash,
    newFolderPathWithSlash
  );
};

function _ensureLeadingSlash(path: string): string {
  // Removes all slashes at the start, and then prepend a single a slash
  return `/${path.replace(/^\/+/, "")}`;
}

export { getCustomersByFolderPath, updateFolderPathForAllCustomers };
