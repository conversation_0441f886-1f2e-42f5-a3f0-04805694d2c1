import { type Platform } from "@lib/platform/read/type";
import { type PlatformCode } from "@lib/settlement/repository/types";

import type { PrismaClient } from "@prisma/client";

export async function getPaymentEnabledPlatformCodes(
  prisma: PrismaClient
): Promise<Platform[]> {
  return prisma.platform.findMany({
    select: {
      platformCode: true,
    },
    where: {
      paymentTypeId: { not: null },
    },
  });
}

export const getPlatformIdByCode = async (
  platformCode: PlatformCode,
  prisma: PrismaClient
): Promise<number> => {
  const platform = await prisma.platform.findUnique({
    where: {
      platformCode,
    },
    select: {
      platformId: true,
    },
  });

  if (!platform) {
    throw new Error(`Platform code ${platformCode} not found.`);
  }

  return platform.platformId;
};
