import { type PrismaClient } from "@prisma/client";
import { describe, it, expect, vi, beforeEach } from "vitest";

import { getPaymentEnabledPlatformCodes } from "./repositories";
import { type Platform } from "./type";

type MockPrismaClient = {
  platform: {
    findMany: ReturnType<typeof vi.fn>;
  };
};

describe("getPaymentEnabledPlatformCodes", () => {
  let mockPrisma: MockPrismaClient;
  let mockFindMany: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    mockFindMany = vi.fn();
    mockPrisma = {
      platform: {
        findMany: mockFindMany,
      },
    };
  });

  it("should return platforms with payment enabled", async () => {
    const mockPlatforms: Platform[] = [
      { platformCode: "ETI" },
      { platformCode: "RFM" },
      { platformCode: "RTO" },
    ];

    mockFindMany.mockResolvedValue(mockPlatforms);

    const result = await getPaymentEnabledPlatformCodes(
      mockPrisma as unknown as PrismaClient
    );

    expect(result).toEqual(mockPlatforms);
    expect(mockFindMany).toHaveBeenCalledWith({
      select: {
        platformCode: true,
      },
      where: {
        paymentTypeId: { not: null },
      },
    });
  });

  it("should return empty array when no platforms have payment enabled", async () => {
    mockFindMany.mockResolvedValue([]);

    const result = await getPaymentEnabledPlatformCodes(
      mockPrisma as unknown as PrismaClient
    );

    expect(result).toEqual([]);
    expect(mockFindMany).toHaveBeenCalledWith({
      select: {
        platformCode: true,
      },
      where: {
        paymentTypeId: { not: null },
      },
    });
  });

  it("should handle single platform with payment enabled", async () => {
    const mockPlatform: Platform[] = [{ platformCode: "ETI" }];

    mockFindMany.mockResolvedValue(mockPlatform);

    const result = await getPaymentEnabledPlatformCodes(
      mockPrisma as unknown as PrismaClient
    );

    expect(result).toEqual(mockPlatform);
    expect(result).toHaveLength(1);
    expect(result[0]?.platformCode).toBe("ETI");
  });

  it("should propagate database errors", async () => {
    const databaseError = new Error("Database connection failed");
    mockFindMany.mockRejectedValue(databaseError);

    await expect(
      getPaymentEnabledPlatformCodes(mockPrisma as unknown as PrismaClient)
    ).rejects.toThrow("Database connection failed");

    expect(mockFindMany).toHaveBeenCalledWith({
      select: {
        platformCode: true,
      },
      where: {
        paymentTypeId: { not: null },
      },
    });
  });

  it("should call prisma.platform.findMany with correct parameters", async () => {
    mockFindMany.mockResolvedValue([]);

    await getPaymentEnabledPlatformCodes(mockPrisma as unknown as PrismaClient);

    expect(mockFindMany).toHaveBeenCalledTimes(1);
    expect(mockFindMany).toHaveBeenCalledWith({
      select: {
        platformCode: true,
      },
      where: {
        paymentTypeId: { not: null },
      },
    });
  });

  it("should return platforms with various platform codes", async () => {
    const mockPlatforms: Platform[] = [
      { platformCode: "ETI" },
      { platformCode: "RFM" },
      { platformCode: "ETO" },
      { platformCode: "ACH" },
    ];

    mockFindMany.mockResolvedValue(mockPlatforms);

    const result = await getPaymentEnabledPlatformCodes(
      mockPrisma as unknown as PrismaClient
    );

    expect(result).toEqual(mockPlatforms);
    expect(result).toHaveLength(4);
    expect(result.map((p) => p.platformCode)).toEqual([
      "ETI",
      "RFM",
      "ETO",
      "ACH",
    ]);
  });
});
