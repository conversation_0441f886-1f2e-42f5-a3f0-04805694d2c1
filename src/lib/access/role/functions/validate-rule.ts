import { permissions } from "@constants/access/permissions";
import { resources } from "@constants/access/resources";

function validateRule(rule: Record<string, string[]>): boolean {
  for (const [resource, permissionsArray] of Object.entries(rule)) {
    if (!(resource in resources)) {
      return false;
    }

    for (const permission of permissionsArray) {
      if (!(permission in permissions)) {
        return false;
      }
    }
  }

  return true;
}

function isRuleEmpty(rule: Record<string, string[]>): boolean {
  if (Object.keys(rule).length === 0) {
    return true;
  }

  return Object.values(rule).every(
    (permissionsArray) => !permissionsArray?.length
  );
}

export { validateRule, isRuleEmpty };
