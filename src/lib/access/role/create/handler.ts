import { createRole } from "./repository";

import type { FastifyReply, FastifyRequest } from "fastify";

type RequestBody = {
  name: string;
};

export const handler = async (
  request: FastifyRequest<{ Body: RequestBody }>,
  reply: FastifyReply
) => {
  try {
    const { name } = request.body;
    const roleId = await createRole(name, request.server.prisma);

    return await reply.send({ roleId });
  } catch (error) {
    request.log.error(error);

    return reply.code(500).send({ message: (error as Error)?.message });
  }
};
