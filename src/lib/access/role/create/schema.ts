import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Create an new access role",
  description: "Create an new access role",
  tags: [tags.access],
  body: {
    type: "object",
    additionalProperties: false,
    properties: {
      name: { type: "string" },
    },
    required: ["name"],
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful role creation",
      type: "object",
      properties: { roleId: { type: "number" } },
      additionalProperties: false,
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Internal server error",
      type: "object",
      properties: {
        message: {
          type: "string",
          description: "Error message explaining the failure",
        },
      },
      additionalProperties: false,
    },
  },
};
