import { type PrismaClient, Prisma } from "@prisma/client";

const createRole = async (name: string, prisma: PrismaClient) => {
  const existingRole = await prisma.role.findFirst({
    where: {
      name,
      deletedAt: null,
    },
  });

  if (existingRole) {
    throw new Error(`Role with name "${name}" already exists.`);
  }

  const role = await prisma.role.upsert({
    where: { name },
    update: {
      deletedAt: null,
      rule: Prisma.JsonNull,
    },
    create: {
      name,
    },
    select: {
      roleId: true,
    },
  });

  return role.roleId;
};

export { createRole };
