import { updateRole } from "./repository";
import { validateRule, isRuleEmpty } from "../functions/validate-rule";

import type { FastifyReply, FastifyRequest } from "fastify";

type RequestBody = {
  roleId: number;
  name: string;
  rule: Record<string, string[]>;
};

export const handler = async (
  request: FastifyRequest<{ Body: RequestBody }>,
  reply: FastifyReply
) => {
  try {
    const { roleId, name, rule } = request.body;

    const updateData: { name?: string; rule?: Record<string, string[]> } = {};

    if (rule) {
      if (isRuleEmpty(rule)) {
        return await reply.code(400).send({
          message:
            "Rule cannot be empty. Please provide at least one valid permission for the role.",
        });
      }

      if (!validateRule(rule)) {
        return await reply.code(400).send({
          message:
            "Invalid rule provided. Please ensure that the rule contains valid resources and permissions.",
        });
      }

      updateData.rule = rule;
    }

    if (name) {
      updateData.name = name;
    }

    if (Object.keys(updateData).length === 0) {
      return await reply.code(400).send({ message: "Nothing to update" });
    }

    await updateRole(roleId, updateData, request.server.prisma);

    return await reply.send({});
  } catch (error) {
    request.log.error(error);

    return reply.code(500).send({ message: (error as Error)?.message });
  }
};
