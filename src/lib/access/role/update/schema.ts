import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary:
    "Update an existing access role by modifying its name, permissions, or both",
  description: `
    You can modify the role's name and its associated permissions (rule). 
    The 'roleId' field is required to identify the role to update. 
    Either the 'name' or 'rule' field (or both) must be provided. 
    The 'rule' field should be a mapping of permission types to an array of resources.
    The response will be an empty object on successful update.
  `,
  tags: [tags.access],
  body: {
    type: "object",
    additionalProperties: false,
    properties: {
      roleId: {
        type: "number",
        description: "The ID of the role to update",
      },
      name: { type: "string", description: "The new name of the access role" },
      rule: {
        type: "object",
        additionalProperties: {
          type: "array",
          items: { type: "string" },
        },
        description:
          "Mapping of permission types to arrays of resources (e.g., 'read', 'write')",
      },
    },
    required: ["roleId"],
    anyOf: [{ required: ["name"] }, { required: ["rule"] }], // Either name or rule is required
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful update",
      type: "object",
      properties: {},
      additionalProperties: false,
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Internal server error",
      type: "object",
      properties: {
        message: {
          type: "string",
          description: "Error message explaining the failure",
        },
      },
      additionalProperties: false,
    },
  },
};
