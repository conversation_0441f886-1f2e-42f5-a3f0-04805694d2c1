import { type PrismaClient } from "@prisma/client";

const updateRole = async (
  roleId: number,
  data: { name?: string; rule?: Record<string, string[]> },
  prisma: PrismaClient
) => {
  // Check if a role with the same name exists for a different roleId
  if (data.name) {
    const existingRole = await prisma.role.findFirst({
      where: {
        name: data.name,
        roleId: {
          not: roleId,
        },
        deletedAt: null,
      },
    });

    if (existingRole) {
      throw new Error(`Role with name "${data.name}" already exists.`);
    }
  }

  await prisma.role.update({
    where: { roleId },
    data,
  });
};

export { updateRole };
