import { deleteRole } from "./repository";

import type { FastifyReply, FastifyRequest } from "fastify";

type RequestBody = {
  roleId: number;
};

export const handler = async (
  request: FastifyRequest<{ Body: RequestBody }>,
  reply: FastifyReply
) => {
  try {
    const { roleId } = request.body;
    await deleteRole(roleId, request.server.prisma);

    return await reply.send({});
  } catch (error) {
    request.log.error(error);

    return reply.code(500).send({ message: (error as Error)?.message });
  }
};
