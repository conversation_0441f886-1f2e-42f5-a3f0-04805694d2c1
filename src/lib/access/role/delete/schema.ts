import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Delete an access role",
  description: `
    This endpoint allows you to delete an existing access role using its roleId.
    The role cannot be deleted if it has associated user roles.
  `,
  tags: [tags.access],
  body: {
    type: "object",
    additionalProperties: false,
    properties: {
      roleId: {
        type: "number",
        description: "The ID of the access role to delete",
      },
    },
    required: ["roleId"],
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful deletion",
      type: "object",
      properties: {},
      additionalProperties: false,
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Internal server error",
      type: "object",
      properties: {
        message: {
          type: "string",
          description: "Error message explaining the failure",
        },
      },
      additionalProperties: false,
    },
  },
};
