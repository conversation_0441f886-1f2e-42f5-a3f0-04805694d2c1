import { type PrismaClient } from "@prisma/client";

const deleteRole = async (roleId: number, prisma: PrismaClient) => {
  const existingRole = await prisma.role.findFirst({
    where: {
      roleId,
      deletedAt: null,
    },
    include: {
      userRole: true,
    },
  });

  if (!existingRole) {
    throw new Error(`Role with id "${roleId}" does not exist.`);
  }

  if (existingRole.userRole && existingRole.userRole.length > 0) {
    throw new Error(
      `Role with name "${existingRole.name}" cannot be deleted because it has associated user roles. 
      Please reassign or remove these roles before deleting.`
    );
  }

  await prisma.role.delete({
    where: { roleId },
  });
};

export { deleteRole };
