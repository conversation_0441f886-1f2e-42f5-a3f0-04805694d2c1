import { type PrismaClient } from "@prisma/client";

const getRoles = async (prisma: PrismaClient) => {
  const roles = await prisma.role.findMany({
    where: { deletedAt: null },
    select: {
      roleId: true,
      name: true,
      rule: true,
      isEditable: true,
      userRole: {
        select: {
          user: {
            select: {
              userId: true,
              userName: true,
              userEmail: true,
            },
          },
        },
      },
    },
  });

  return roles.map((role) => ({
    id: role.roleId,
    name: role.name,
    rule: role.rule,
    isEditable: role.isEditable,
    users: role.userRole.map((userRole) => ({
      id: userRole.user.userId,
      fullName: userRole.user.userName,
      email: userRole.user.userEmail,
    })),
  }));
};

export { getRoles };
