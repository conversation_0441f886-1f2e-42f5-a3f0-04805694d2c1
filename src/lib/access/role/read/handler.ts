import { getRoles } from "./repository";

import type { FastifyReply, FastifyRequest } from "fastify";

export const handler = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const roles = await getRoles(request.server.prisma);

    return await reply.send({ roles });
  } catch (error) {
    request.log.error(error);

    return reply.code(500).send({ message: (error as Error)?.message });
  }
};
