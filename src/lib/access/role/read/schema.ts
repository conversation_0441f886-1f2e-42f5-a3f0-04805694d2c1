import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Fetch a list of all access roles with their users and permissions",
  description: `
    Each role contains its ID, name, permissions (rule), editability status, 
    and the associated users who are assigned to the role, including their user ID, name, and email.
  `,
  tags: [tags.access],
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description:
        "An object containing a list of roles with associated users and permissions",
      type: "object",
      properties: {
        roles: {
          type: "array",
          description: "An array of roles",
          items: {
            type: "object",
            properties: {
              id: {
                type: "number",
                description: "Unique identifier for the role",
              },
              name: { type: "string", description: "Name of the role" },
              rule: {
                type: "object",
                additionalProperties: {
                  type: "array",
                  items: { type: "string" },
                },
                description:
                  "Mapping of permission types to an array of resources",
              },
              isEditable: {
                type: "boolean",
                description: "Flag indicating if the role is editable",
              },
              users: {
                type: "array",
                description: "List of users associated with this role",
                items: {
                  type: "object",
                  properties: {
                    id: {
                      type: "number",
                      description: "Unique identifier for the user",
                    },
                    fullName: {
                      type: "string",
                      description: "Name of the user",
                    },
                    email: { type: "string", description: "Email of the user" },
                  },
                  required: ["id", "fullName", "email"],
                },
              },
            },
            required: ["id", "name", "rule", "isEditable", "users"],
          },
        },
      },
      additionalProperties: false,
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Internal server error",
      type: "object",
      properties: {
        message: {
          type: "string",
          description: "Error message explaining the failure",
        },
      },
      additionalProperties: false,
    },
  },
};
