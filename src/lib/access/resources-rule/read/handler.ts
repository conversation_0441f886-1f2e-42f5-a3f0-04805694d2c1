import { permissions } from "@constants/access/permissions";
import { resources, resourcesRule } from "@constants/access/resources";

import type { FastifyReply, FastifyRequest } from "fastify";

export const handler = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    return await reply.send({ permissions, resources, resourcesRule });
  } catch (error) {
    request.log.error(error);

    return reply.code(500).send({ message: (error as Error)?.message });
  }
};
