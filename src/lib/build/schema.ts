import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Fetch Backend Build",
  description: "Fetches the Git SHA of the current backend build.",
  tags: [tags.build],
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful response",
      type: "object",
      properties: {
        gitHash: {
          type: "string",
          example: "1414fae",
        },
      },
      required: ["gitHash"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Error response",
      type: "object",
      properties: {
        message: { type: "string" },
        error: {
          type: "object",
          properties: {
            message: { type: "string" },
          },
          required: ["message"],
        },
      },
      required: ["message", "error"],
    },
  },
};
