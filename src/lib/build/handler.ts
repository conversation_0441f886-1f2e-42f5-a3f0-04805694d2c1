import { environment } from "@constants/environment";

import type { FastifyReply, FastifyRequest } from "fastify";

export const handler = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    await reply.send({
      gitHash: environment.githubSha,
    });
  } catch (error) {
    request.log.error(error);
    await reply.code(500).send({
      message: "Error fetching the build, please try again.",
      error: { message: (error as Error).message },
    });
  }
};
