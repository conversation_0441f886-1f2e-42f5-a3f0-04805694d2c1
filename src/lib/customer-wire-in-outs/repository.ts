import { type WirePayment } from "@lib/customer-wire-in-outs/type";
import { type PrismaClient } from "@prisma/client";
import { fromDateOnly, type DateOnly } from "@utils/date-only";

const getWirePayments = async (
  customerId: number,
  fromDate: DateOnly,
  toDate: DateOnly,
  prisma: PrismaClient
): Promise<WirePayment[]> => {
  const payments = await prisma.customerWireInOuts.findMany({
    where: {
      customerId,
      transactionDate: {
        gte: fromDateOnly(fromDate),
        lte: fromDateOnly(toDate),
      },
      deletedAt: null,
    },
  });

  return payments.map((payment) => ({
    id: payment.customerWireInPayOutId,
    transactionAmount: payment.transactionAmount!.toNumber(),
    transactionType: payment.transactionType,
  }));
};

export { getWirePayments };
