import { type CustomerTypeEmailFilter } from "../../../types/customerType/customer-type";

import type { PrismaClient } from "@prisma/client";

export async function getCustomerTypes(
  prisma: PrismaClient
): Promise<CustomerTypeEmailFilter[]> {
  return prisma.customerType.findMany({
    select: {
      customerTypeId: true,
      customerTypeName: true,
    },
  }) as unknown as CustomerTypeEmailFilter[];
}
