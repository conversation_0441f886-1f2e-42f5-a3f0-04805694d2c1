import { createWireRecord } from "@lib/wire/create/repository";
import { type CreateWireProperties, type Wire } from "@lib/wire/create/types";
import { type PrismaClient } from "@prisma/client";
import { addDays, addMonths, endOfMonth, getDate, setDate } from "date-fns";

export const createWire = async (
  prisma: PrismaClient,
  { settlement, wireAmount, toDate }: CreateWireProperties
): Promise<Wire> => {
  const frequency = settlement?.customerCustomerType?.statementFrequency;
  const expectedWireDate = getExpectedWireDate(
    frequency?.statementFrequencyCode,
    toDate
  );
  const beneficiary = settlement.customer?.wireGroup?.beneficiary;
  const wireConfiguration = settlement.customer?.wireGroup?.wireConfiguration;
  const bank = beneficiary?.bankAccount;
  const crypto = beneficiary?.cryptoAccount;
  const paymentRailId = crypto
    ? crypto?.cryptoPaymentRailId
    : bank?.bankPaymentRailId;
  const isHold =
    (settlement?.endBalance ?? 0) <
      (wireConfiguration?.thresholdAmount ?? 5000) ||
    (wireConfiguration?.isHold ?? false);

  const wireData = {
    expectedWireDate,
    finalWireDate: expectedWireDate,
    expectedWireAmount: wireAmount,
    finalWireAmount: wireAmount,
    customerId: settlement.customerId,
    settlementId: settlement.customerSettlementsId,
    frequencyId: frequency?.statementFrequencyId,
    isHold,
    reference: beneficiary?.reference ?? undefined,
    paymentRailId: paymentRailId ?? undefined,
  };

  return createWireRecord(prisma, wireData);
};

export const getExpectedWireDate = (
  frequencyCode: string | undefined,
  toDate: string
): Date => {
  const date = new Date(toDate);

  switch (frequencyCode) {
    case "M": {
      return addDays(date, 15);
    }

    case "SM": {
      if (getDate(date) === 15) {
        return endOfMonth(date);
      }

      return setDate(addMonths(date, 1), 15);
    }

    case "TaW": {
      return addDays(date, 4);
    }

    case "WM": {
      return addDays(date, 8);
    }

    case "WF": {
      return addDays(date, 7);
    }

    default: {
      return addDays(date, 1);
    }
  }
};
