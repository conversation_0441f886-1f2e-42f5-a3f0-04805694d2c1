import {
  type CreateWireRecordProperties,
  type Wire,
} from "@lib/wire/create/types";
import { type PrismaClient } from "@prisma/client";

export const createWireRecord = async (
  prisma: PrismaClient,
  {
    expectedWireDate,
    finalWireDate,
    expectedWireAmount,
    finalWireAmount,
    customerId,
    settlementId,
    frequencyId,
    isHold,
    reference,
    paymentRailId,
  }: CreateWireRecordProperties
): Promise<Wire> => {
  return (await prisma.wire.create({
    data: {
      expectedWireDate,
      finalWireDate,
      expectedWireAmount,
      finalWireAmount,
      customerId,
      settlementId,
      frequencyId,
      isHold,
      reference: reference ?? null,
      paymentRailId: paymentRailId ?? null,
    },
  })) as unknown as Wire;
};
