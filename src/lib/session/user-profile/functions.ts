import { type PrismaClient } from "@prisma/client";

import { getUserProfile as getUserProfileFromDB } from "./repositiory/read-profile";
import {
  type UserProfile,
  type Role,
  type Rule,
} from "../../../types/user-profile";

const getUserProfile = async (
  prisma: PrismaClient,
  userNameOrId: string | number
): Promise<{ userKey: string; userProfile: UserProfile } | undefined> => {
  const fullProfile = await getUserProfileFromDB(prisma, userNameOrId);

  if (!fullProfile) {
    return;
  }

  const mappedUserRoles: Role[] = fullProfile.userRole.map(
    (roleData: {
      role: {
        roleId: number;
        name: string;
        isEditable: boolean;
        rule: unknown;
      };
    }) => ({
      id: roleData.role.roleId,
      name: roleData.role.name,
      isEditable: roleData.role.isEditable,
      rule: roleData.role.rule as Rule,
    })
  );

  const userProfile: UserProfile = {
    id: fullProfile.userId,
    fullName: fullProfile.userName,
    email: fullProfile.userEmail,
    roles: mappedUserRoles,
  };

  return {
    userKey: fullProfile.userKey,
    userProfile,
  };
};

export { getUserProfile };
