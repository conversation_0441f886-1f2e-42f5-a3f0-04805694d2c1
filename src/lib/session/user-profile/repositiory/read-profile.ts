import { type PrismaClient, type Prisma } from "@prisma/client";

const getUserProfile = async (
  prisma: PrismaClient,
  userNameOrId: string | number
): Promise<
  | Prisma.userGetPayload<{
      select: typeof userProfileSelect;
    }>
  | undefined
> => {
  // Determine whether to search by userId or userName
  const whereClause =
    typeof userNameOrId === "number"
      ? { userId: userNameOrId, deletedAt: null }
      : { userName: userNameOrId, deletedAt: null };

  const userProfile = await prisma.user.findFirst({
    where: whereClause,
    select: userProfileSelect,
  });

  if (!userProfile) {
    return;
  }

  return userProfile;
};

const userProfileSelect = {
  userId: true,
  userName: true,
  userEmail: true,
  userKey: true,
  userRole: {
    select: {
      role: {
        select: {
          roleId: true,
          name: true,
          isEditable: true,
          rule: true,
        },
      },
    },
  },
};

export { getUserProfile };
