import { type PrismaClient } from "@prisma/client";

const updateUserRole = async (
  userId: number,
  addRoleIds: number[],
  removeRoleIds: number[],
  prisma: PrismaClient
) => {
  const existingUserRole = await prisma.userRole.findMany({
    where: {
      userId,
    },
    select: {
      roleId: true,
    },
  });

  const existingRoleIds = new Set(existingUserRole.map((role) => role.roleId));

  const rolesToAdd = addRoleIds.filter(
    (roleId) => !existingRoleIds.has(roleId)
  );

  const rolesToRemove = removeRoleIds.filter((roleId) =>
    existingRoleIds.has(roleId)
  );

  // Add new roles
  if (rolesToAdd.length > 0) {
    await prisma.userRole.createMany({
      data: rolesToAdd.map((roleId) => ({
        userId,
        roleId,
      })),
    });
  }

  // Remove roles
  if (rolesToRemove.length > 0) {
    await prisma.userRole.deleteMany({
      where: {
        userId,
        roleId: {
          in: rolesToRemove,
        },
      },
    });
  }

  return { addedRoles: rolesToAdd, removedRoles: rolesToRemove };
};

export { updateUserRole };
