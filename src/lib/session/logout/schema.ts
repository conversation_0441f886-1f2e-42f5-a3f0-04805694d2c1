import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "User Logout",
  description: "Logs out the authenticated user, ending their session.",
  tags: [tags.session],
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful response",
      type: "object",
      properties: {
        message: { type: "string", example: "Logout successful" },
      },
      required: ["message"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Error response - this should never happen",
      type: "object",
      properties: {
        message: { type: "string", example: "Logout Error" },
      },
      required: ["message"],
    },
  },
};
