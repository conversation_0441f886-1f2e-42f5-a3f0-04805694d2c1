import { clearTokenCookies } from "../token/clear-token-cookies.js";

import type { FastifyReply, FastifyRequest } from "fastify";

export const handler = async (
  _request: FastifyRequest,
  reply: FastifyReply
) => {
  try {
    clearTokenCookies(reply);
    await reply.code(200).send({ message: "Logout successful" });
  } catch {
    await reply.code(500).send({ message: "Failed to logout" });
  }
};
