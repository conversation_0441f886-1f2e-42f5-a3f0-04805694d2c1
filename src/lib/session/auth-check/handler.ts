import { getUserProfile } from "@lib/session/user-profile/functions";

import type { FastifyReply, FastifyRequest } from "fastify";

export const handler = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const user = await getUserProfile(
      request.server.prisma,
      request.userProfile.id
    );

    return await reply.send({ userProfile: user?.userProfile });
  } catch (error) {
    request.log.error(error);

    return reply.code(500).send({
      message: "Error validating credentials, please try again.",
    });
  }
};
