import { tags } from "@constants/schema/tags";
import { type FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Auth Check",
  description: "Checks if the user is logged in and returns their profile.",
  tags: [tags.session],
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful response containing the user's profile.",
      type: "object",
      properties: {
        userProfile: {
          type: "object",
          properties: {
            id: { type: "number", description: "User's unique identifier." },
            fullName: { type: "string", description: "User's full name." },
            email: { type: "string", description: "User's email address." },
            roles: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  id: { type: "number", description: "Role ID." },
                  name: { type: "string", description: "Role name." },
                  isEditable: {
                    type: "boolean",
                    description: "Whether the role is editable.",
                  },
                  rule: {
                    type: "object",
                    additionalProperties: {
                      type: "array",
                      items: {
                        type: "string",
                        description: "Permissions granted to this role.",
                      },
                    },
                  },
                },
                required: ["id", "name", "isEditable", "rule"],
              },
            },
          },
          required: ["id", "fullName", "email", "roles"],
          additionalProperties: false,
        },
      },
      required: ["userProfile"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    401: {
      description: "Unauthorized response. User is not logged in.",
      type: "object",
      properties: {
        message: {
          type: "string",
          description: "Error message explaining unauthorized access.",
        },
      },
      required: ["message"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Error response. Server error occurred during auth check.",
      type: "object",
      properties: {
        message: {
          type: "string",
          description: "Error message explaining the server error.",
        },
      },
      required: ["message"],
    },
  },
};
