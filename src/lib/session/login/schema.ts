import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "User Login",
  description:
    "Authenticates the user with provided username and password, and logs them in.",
  tags: [tags.session],

  body: {
    type: "object",
    additionalProperties: false,
    properties: {
      username: {
        type: "string",
        minLength: 1,
        description:
          "The username of the user, must be atleast one characters.",
      },
      password: {
        type: "string",
        minLength: 1,
        description:
          "The password of the user, must be atleast one characters.",
      },
    },
    required: ["username", "password"],
  },

  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful response",
      type: "object",
      properties: {
        userProfile: {
          type: "object",
          properties: {
            id: { type: "number" },
            fullName: { type: "string" },
            email: { type: "string" },
            roles: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  id: { type: "number" },
                  name: { type: "string" },
                  isEditable: { type: "boolean" },
                  rule: {
                    type: "object",
                    additionalProperties: {
                      type: "array",
                      items: { type: "string" },
                    },
                  },
                },
                required: ["id", "name", "isEditable", "rule"],
              },
            },
          },
          required: ["id", "fullName", "email", "roles"],
          additionalProperties: false,
        },
      },
      required: ["userProfile"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    401: {
      description: "Unauthorized response",
      type: "object",
      properties: {
        message: {
          type: "string",
          description: "Error message explaining the unauthorized access.",
        },
      },
      required: ["message"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Error response",
      type: "object",
      properties: {
        message: {
          type: "string",
          description: "Error message explaining the server error.",
        },
      },
      required: ["message"],
    },
  },
};
