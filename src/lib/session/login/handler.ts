import { getUserProfile } from "@lib/session/user-profile/functions";

import { setTokenCookies } from "../token/set-token-cookies.js";
import { verifyPassword } from "../utils/verify-password.js";

import type { FastifyReply, FastifyRequest } from "fastify";

type RequestBody = {
  username: string;
  password: string;
};

export const handler = async (
  request: FastifyRequest<{ Body: RequestBody }>,
  reply: FastifyReply
) => {
  try {
    const { username, password } = request.body;
    const user = await getUserProfile(request.server.prisma, username);

    if (user && verifyPassword(password, user.userKey)) {
      const { userProfile } = user;

      await setTokenCookies(reply, userProfile);

      return await reply.send({ userProfile });
    }

    return await reply.code(401).send({
      message: "Invalid credentials",
    });
  } catch (error) {
    request.log.error(error);
    await reply.code(500).send({
      message: "Error validating credentials, please try again.",
    });
  }
};
