import { environment } from "@constants/environment/index.js";
import { type FastifyRequest } from "fastify";

import { type UserProfile } from "../../../types/user-profile";

type DecodedTokenPayload = {
  accessKey?: boolean;
  userProfile: UserProfile;
  nounce: string;
};

/**
 * Validates and decodes the access token in the request cookies.
 *
 * @param {FastifyRequest} request - The fastify request instance.
 * @returns {Promise<DecodedTokenPayload>} - The decoded token payload if the token is valid.
 * @throws Will throw an error if the token is invalid.
 */
const validateCookieAccessToken = async (
  request: FastifyRequest
): Promise<DecodedTokenPayload> => {
  const accessToken = request.cookies[environment.accessTokenCookieName];
  const decoded: DecodedTokenPayload = await request.accessJwtVerify(
    String(accessToken)
  );

  if (Boolean(decoded?.accessKey) && decoded.userProfile && decoded.nounce) {
    return decoded;
  }

  throw new Error("Invalid access token");
};

/**
 * Validates and decodes the refresh token in the request cookies.
 * @param {FastifyRequest} request - The Fastify request object.
 * @returns {Promise<DecodedTokenPayload>} - The decoded refresh token payload.
 * @throws Will throw an error if the refresh token is invalid.
 */
const validateCookieRefreshToken = async (
  request: FastifyRequest
): Promise<DecodedTokenPayload> => {
  const accessToken = request.cookies[environment.accessTokenCookieName];
  const refreshToken = request.cookies[environment.refreshTokenCookieName];

  const refreshDecoded: DecodedTokenPayload = await request.refreshJwtVerify(
    String(refreshToken)
  );

  const accessDecoded: DecodedTokenPayload = await request.accessJwtDecode(
    String(accessToken)
  )!;

  if (
    !refreshDecoded?.accessKey &&
    accessDecoded.nounce === refreshDecoded.nounce &&
    refreshDecoded.userProfile
  ) {
    return refreshDecoded;
  }

  throw new Error("Invalid refresh token");
};

export { validateCookieAccessToken, validateCookieRefreshToken };
