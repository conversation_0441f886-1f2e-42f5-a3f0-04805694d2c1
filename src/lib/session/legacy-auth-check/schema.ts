import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Authenticate Legacy System User",
  description: `
    Authenticates users who have already been authenticated by the legacy system, allowing them access
    to the current system without re-authentication.
    `,
  tags: [tags.legacy],
  body: {
    type: "object",
    additionalProperties: false,
    properties: {
      token: {
        type: "string",
        description:
          "JWT token from the legacy system to authenticate the user.",
        minLength: 10,
      },
    },
    required: ["token"],
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful response",
      type: "object",
      properties: {
        userProfile: {
          type: "object",
          properties: {
            id: { type: "number" },
            fullName: { type: "string" },
            email: { type: "string" },
            roles: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  id: { type: "number" },
                  name: { type: "string" },
                  isEditable: { type: "boolean" },
                  rule: {
                    type: "object",
                    additionalProperties: {
                      type: "array",
                      items: { type: "string" },
                    },
                  },
                },
                required: ["id", "name", "isEditable", "rule"],
              },
            },
          },
          required: ["id", "fullName", "email", "roles"],
          additionalProperties: false,
        },
      },
      required: ["userProfile"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    401: {
      description: "Unauthorized response",
      type: "object",
      properties: {
        message: { type: "string", example: "Invalid token" },
      },
      required: ["message"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Internal server error",
      type: "object",
      properties: {
        message: {
          type: "string",
          example: "Error validating credentials, please try again.",
        },
      },
      required: ["message"],
    },
  },
};
