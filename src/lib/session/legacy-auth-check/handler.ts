import { environment } from "@constants/environment/index.js";
import { getUserProfile } from "@lib/session/user-profile/functions";
import jwt, { type JwtPayload } from "jsonwebtoken";

import { setTokenCookies } from "../token/set-token-cookies.js";

import type { FastifyReply, FastifyRequest } from "fastify";

type DecodedTokenPayload = {
  id: number;
};

type RequestBody = {
  token: string;
};

export const handler = async (
  request: FastifyRequest<{ Body: RequestBody }>,
  reply: FastifyReply
) => {
  try {
    const { token } = request.body;
    // Note: jsonwebtoken is used solely for legacy login because, in legacy login,
    // the token is present in the request body instead of a cookie.
    // Once legacy login is no longer needed, both jsonwebtoken and @types/jsonwebtoken
    // can be safely removed.

    const decoded = jwt.verify(token, environment.tokenSecret) as JwtPayload;

    const decodedTokenPayload: DecodedTokenPayload =
      decoded as DecodedTokenPayload;

    const user = await getUserProfile(
      request.server.prisma,
      decodedTokenPayload.id
    );

    if (!user) {
      return await reply.code(401).send({ message: "Invalid token" });
    }

    await setTokenCookies(reply, user.userProfile);

    return await reply.send({
      userProfile: user.userProfile,
    });
  } catch (error) {
    request.log.error(error);

    return reply.code(500).send({
      message: "Error validating credentials, please try again.",
    });
  }
};
