import { clearTokenCookies } from "@lib/session/token/clear-token-cookies";
import { setTokenCookies } from "@lib/session/token/set-token-cookies.js";
import {
  validateCookieAccessToken,
  validateCookieRefreshToken,
} from "@lib/session/token/validate-token-cookies";

import type { FastifyError, FastifyReply, FastifyRequest } from "fastify";

const authenticate = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const decoded = await validateCookieAccessToken(request);

    request.userProfile = decoded.userProfile;
  } catch (error) {
    request.log.error(
      error,
      "Access token error detected. Expired error will be followed by attempting to refresh the token."
    );
    await (isFastifyError(error)
      ? handleAuthenticationError(error, request, reply)
      : unauthorizedReply(reply, "Invalid token"));
  }
};

function isFastifyError(error: unknown): error is FastifyError {
  return (error as FastifyError).code !== undefined;
}

const unauthorizedReply = async (reply: FastifyReply, message: string) => {
  clearTokenCookies(reply);
  await reply.code(401).send({
    success: false,
    message,
  });
};

const handleAuthenticationError = async (
  error: FastifyError,
  request: FastifyRequest,
  reply: FastifyReply
) => {
  if (error.code === "FST_JWT_AUTHORIZATION_TOKEN_EXPIRED") {
    return handleExpiredAccessToken(request, reply);
  }

  await unauthorizedReply(reply, "Invalid token");
};

const handleExpiredAccessToken = async (
  request: FastifyRequest,
  reply: FastifyReply
) => {
  try {
    const refreshDecoded = await validateCookieRefreshToken(request);
    const { userProfile } = refreshDecoded;

    clearTokenCookies(reply);
    await setTokenCookies(reply, userProfile);

    request.userProfile = userProfile;
    request.log.info(
      `Refreshing access token for ${userProfile.email}  was successful`
    );
  } catch (error) {
    request.log.error(error, "Failed to refresh access token");

    return unauthorizedReply(reply, "Invalid token");
  }
};

export { authenticate };
