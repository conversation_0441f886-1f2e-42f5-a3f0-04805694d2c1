import { fastifyHelmet } from "@fastify/helmet";
import fp from "fastify-plugin";

import type { FastifyPluginAsync } from "fastify";

const helmetPlugin: FastifyPluginAsync = fp(async (server) => {
  await server.register(fastifyHelmet, {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    enableCSPNonces: true,
    hsts: {
      maxAge: 31_536_000,
      includeSubDomains: true,
      preload: true,
    },
    crossOriginResourcePolicy: { policy: "same-origin" },
  });
});

export default helmetPlugin;
