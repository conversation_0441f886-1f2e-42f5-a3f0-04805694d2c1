import { join } from "node:path";

import { baseDirectoryName } from "@constants/filesystem";
import fastifyStatic from "@fastify/static";
import { type FastifyPluginAsync } from "fastify";
import fp from "fastify-plugin";

const staticPlugin: FastifyPluginAsync = fp(async (server) => {
  await server.register(fastifyStatic, {
    root: join(process.cwd(), baseDirectoryName),
    prefix: "/filesystem/",
  });
});

export default staticPlugin;
