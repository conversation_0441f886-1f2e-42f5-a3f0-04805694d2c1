import { fastifySwagger } from "@fastify/swagger";
import fastifySwagger<PERSON> from "@fastify/swagger-ui";
import fp from "fastify-plugin";

import type { FastifyPluginAsync } from "fastify";

const swaggerPlugin: FastifyPluginAsync = fp(async (server) => {
  // This registers the Fastify Swagger plugin, which is necessary for Fastify Swagger UI.
  await server.register(fastifySwagger);

  // This registers the Fastify Swagger UI plugin.
  await server.register(fastifySwaggerUI, {
    routePrefix: "/documentation",
    uiConfig: {
      docExpansion: "none",
      deepLinking: false,
    },
    uiHooks: {
      onRequest(_request, _reply, next) {
        next();
      },
      preHandler(_request, _reply, next) {
        next();
      },
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    staticCSP: true,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    transformStaticCSP: (header) => header,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    transformSpecification(swaggerObject, _request, _reply) {
      return swaggerObject;
    },
    transformSpecificationClone: true,
  });
});

export default swaggerPlugin;
