import { environment } from "@constants/environment";
import jwt from "@fastify/jwt";
import fp from "fastify-plugin";

import type { FastifyPluginAsync } from "fastify";

const jwtPlugin: FastifyPluginAsync = fp(async (server) => {
  await server.register(jwt, {
    secret: environment.tokenSecret,
    namespace: "access",
    cookie: {
      cookieName: environment.accessTokenCookieName,
      signed: environment.isProduction,
    },
  });

  await server.register(jwt, {
    secret: environment.tokenSecret,
    namespace: "refresh",
    cookie: {
      cookieName: environment.refreshTokenCookieName,
      signed: environment.isProduction,
    },
  });
});

export default jwtPlugin;
