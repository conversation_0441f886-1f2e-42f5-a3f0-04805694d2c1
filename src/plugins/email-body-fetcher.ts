/* eslint-disable @typescript-eslint/naming-convention */
import {
  LambdaClient,
  InvokeCommand,
  type InvokeCommandInput,
  type InvokeCommandOutput,
} from "@aws-sdk/client-lambda";
import { environment } from "@constants/environment";
import { type FastifyPluginAsync } from "fastify";
import fp from "fastify-plugin";

const awsLambdaPlugin: FastifyPluginAsync = fp(async (fastify) => {
  const region = environment.emailBodyLambdaRegion!;
  const accessKeyId = environment.emailBodyLambdaAccessKey!;
  const secretAccessKey = environment.emailBodyLambdaSecret!;

  const lambdaClient = new LambdaClient({
    region,
    credentials: {
      accessKeyId,
      secretAccessKey,
    },
  });

  const getEmailBody = async (
    emailId: number
  ): Promise<{
    emailBodyHtml: string;
    error?: string;
  }> => {
    const parameters: InvokeCommandInput = {
      FunctionName: environment.emailBodyLambdaFunctionName,
      InvocationType: "RequestResponse",
      Payload: new TextEncoder().encode(JSON.stringify({ emailId })),
    };

    try {
      const command = new InvokeCommand(parameters);
      const response: InvokeCommandOutput = await lambdaClient.send(command);

      if (response.FunctionError) {
        throw new Error(
          `Email body Lambda function error: ${response.FunctionError}`
        );
      }

      if (response.Payload) {
        const payloadString = new TextDecoder("utf8").decode(response.Payload);
        const result = JSON.parse(payloadString) as {
          emailBodyHtml: string;
          error?: string;
        };

        return result;
      }

      throw new Error("No payload received from Email body Lambda function");
    } catch (error) {
      fastify.log.error(
        `Error invoking email body Lambda: ${(error as Error).message}`
      );
      throw error;
    }
  };

  fastify.decorate("getEmailBody", getEmailBody);
});

export default awsLambdaPlugin;
