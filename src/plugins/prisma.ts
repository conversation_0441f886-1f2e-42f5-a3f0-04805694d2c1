import { environment } from "@constants/environment/index.js";
import { PrismaClient } from "@prisma/client";
import fp from "fastify-plugin";

import type { FastifyPluginAsync } from "fastify";

const prismaPlugin: FastifyPluginAsync = fp(async (server) => {
  let logOptions = {};

  if (environment.isDevelopment) {
    logOptions = { log: ["error"] };
  }

  const prisma = new PrismaClient({
    ...logOptions,
    datasourceUrl: environment.databaseUrl,
  });

  if (environment.isProduction || environment.isDevelopment) {
    await prisma.$connect();
  }

  // Make Prisma Client available through the fastify server instance: server.prisma
  server.decorate("prisma", prisma);

  server.addHook("onClose", async (server) => {
    await server.prisma.$disconnect();
  });
});

export default prismaPlugin;
