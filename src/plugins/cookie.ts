import { environment } from "@constants/environment";
import cookies from "@fastify/cookie";
import fp from "fastify-plugin";

import type { FastifyPluginAsync } from "fastify";

const cookiePlugin: FastifyPluginAsync = fp(async (server) => {
  await server.register(cookies, {
    secret: environment.tokenSecret,
    parseOptions: {
      signed: environment.isProduction,
      secure: environment.isProduction,
      sameSite: "strict",
    },
  });
});

export default cookiePlugin;
