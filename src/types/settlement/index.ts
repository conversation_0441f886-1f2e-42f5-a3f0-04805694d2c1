import { type DateOnly } from "@utils/date-only";

import { type TransactionStatus } from "../../services/blusky/transactions";

type DeterminedInterval = {
  fromDate: DateOnly;
  toDate: DateOnly;
};

type TrueUpOptions = {
  rateDeterminingInterval: DeterminedInterval;
};

type AggregateOptions = Record<
  TransactionStatus,
  {
    aggregateForPlatforms: string[];
    targetPlatform: string;
  }
>;

type CalculationOptions = {
  rateDeterminingInterval?: DeterminedInterval;
  aggregate?: AggregateOptions;
  trueUp?: TrueUpOptions;
  includeKyc?: boolean;
};

export {
  type DeterminedInterval,
  type CalculationOptions,
  type TrueUpOptions,
  type AggregateOptions,
};
