import { type Decimal } from "@prisma/client/runtime/library";

export type TemplateData = {
  clientName: string;
  wireAmount: Decimal | number;
  isSftpEnabled?: boolean;
  isBluskyEnabled?: boolean;
  showEndBalanceInTemplate?: boolean;
  fundsGoingOut?: boolean;
  endBalance?: number;
  comment?: string | undefined;
  entityName: string;
  subjectNotification: string;
  entityLogo: string;
  entityNotificationStyle: string;
  copyrightNotice: string;
};
