export type EmailFilterParameters = {
  status: string;
  subject?: string;
  startSendDate?: string;
  endSendDate?: string;
  toEmail?: string;
  entity?: string;
  frequency?: string;
  merchantType?: string;
  sortColumnName?: string;
  sortDirection?: string;
};

export type Customer = {
  customerName: string;
  serviceNumber: string;
  emailConfiguration: EmailConfiguration | undefined;
  entity: Entity;
};

export type EmailConfiguration = {
  toMeta: string;
  ccMeta: string;
};

export type Entity = {
  entityName: string;
};

export type Wire = {
  finalWireAmount: number;
  finalWireDate: string;
  settlementId: number | undefined;
  customer: Customer;
};

export type CustomerWireInOuts = {
  transactionAmount: number;
  transactionDate: string;
  customer: Customer;
};

export type EmailRecord = {
  emailId: number;
  toMeta: string | undefined;
  ccMeta: string | undefined;
  subject: string;
  sentAt: string | undefined;
  scheduledSendDate: string;
  transferInId: number | undefined;
  wireId: number;
  wire: Wire | undefined;
  customerWireInOuts: CustomerWireInOuts | undefined;
};

export type MandatoryEmailFields = {
  wireId?: number;
  transferInId?: number;
  subject: string;
  scheduledSendDate: Date;
};

export type EndBalanceInfo = {
  customerName: string;
  endBalance: number;
  currentBalance: number;
  totalBalance: number;
};

export type EndBalanceMap = Record<string, EndBalanceInfo>;

export type FormattedEmailData = {
  cc: string[];
  serviceNumber: string;
  wireAmount: number;
  clientName: string;
  sendDate: string | undefined;
  subject: string;
  endBalance: number;
  entityName: string;
  id: number;
  to: string[];
};
