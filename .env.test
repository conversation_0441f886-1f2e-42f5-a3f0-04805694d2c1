NODE_ENV="development"
NODE_OPTIONS="--max-old-space-size=4096"
# Separate multiple domains with comma
ALLOWED_CORS_DOMAINS="http://localhost:5173,http://localhost:3000,http://localhost:4173"
#JWT SECRET KEY
JWT_SECRET="finance"
JWT_ACCESS_TOKEN_COOKIE_NAME="token"
JWT_REFRESH_TOKEN_COOKIE_NAME="refresh_token"
JWT_REFRESH_TOKEN_DURATION="1d"
JWT_ACCESS_TOKEN_DURATION="15m"
#Database
DATABASE_URL=

#Blusky
BLUSKY_BASE_URL='baseurl'
BLUSKY_CUBE_ID=
BLUSKY_USERNAME=
BLUSKY_PASSWORD=
BLUSKY_DATASOURCE='test-datasource'

#Email
SENDER_EMAIL_USERNAME="finance"

GITHUB_SHA="be_sha_test"

MONTHS_TO_HISTORICAL="15"

TZ=UTC
