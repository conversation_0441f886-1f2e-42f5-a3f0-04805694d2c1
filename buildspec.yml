version: 0.2
phases:
  install:
    commands:
      - echo Install starting
      - echo Logging into DockerHub...
      - echo "${DOCKERHUB_PASSWORD}" | docker login -u "${DOCKERHUB_USERNAME}" --password-stdin

  pre_build:
    commands:
      - echo pre_build starting
      - cp .env.example .env
      - echo Logging in to Amazon ECR...
      - aws --version
      - echo "Logging in to Amazon ECR..." && aws ecr get-login-password | docker login --username AWS --password-stdin ${REPOSITORY_URL}

      # Get the current task definition revision
      - echo "Fetching current ECS task definition revision..."
      - OLD_ECS_TASK_DEFINITION_REVISION=$(aws ecs describe-task-definition --task-definition $ECS_TASK_DEFINITION --output text --query taskDefinition.revision)
      - NEW_ECS_TASK_DEFINITION_REVISION=$(($OLD_ECS_TASK_DEFINITION_REVISION + 1))

      # Get short SHA for tagging
      - echo "Codebuild Resolved Source Version:" $CODEBUILD_RESOLVED_SOURCE_VERSION
      - SHA_SHORT=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
      - echo "Commit Hash:" $SHA_SHORT

      # Construct the new image tag
      - COMMITID_TAG="v$NEW_ECS_TASK_DEFINITION_REVISION.$SHA_SHORT"
      - echo "COMMITID_TAG=$COMMITID_TAG"
      - IMAGE_TAG=${COMMITID_TAG}

  build:
    commands:
      - echo Build started on `date`
      - echo Building the Docker images...
      - docker build -t ${REPOSITORY_URL}:latest .
      - docker tag ${REPOSITORY_URL}:latest ${REPOSITORY_URL}:$IMAGE_TAG

  post_build:
    commands:
      - echo Build completed on `date`
      - echo pushing to the repos...
      - docker push ${REPOSITORY_URL}:latest
      - docker push ${REPOSITORY_URL}:$IMAGE_TAG

      - echo Fetching current task definition...
      - aws ecs describe-task-definition --task-definition $ECS_TASK_DEFINITION --query taskDefinition --output json > task-definition.json

      - echo Cleaning up task definition...
      - jq 'del(.taskDefinitionArn, .revision, .status, .requiresAttributes, .compatibilities, .registeredAt, .registeredBy)' task-definition.json > cleaned-task-definition.json

      - echo Updating GITHUB_SHA in task definition...
      - jq --arg SHA "$IMAGE_TAG" '(.containerDefinitions[0].environment[] | select(.name=="GITHUB_SHA") .value) = $SHA' cleaned-task-definition.json > updated-task-definition.json

      - echo Registering updated task definition...
      - NEW_TASK_DEF_ARN=$(aws ecs register-task-definition --cli-input-json file://updated-task-definition.json --query taskDefinition.taskDefinitionArn --output text)

      - echo Updating ECS service...
      - aws ecs update-service --cluster $ECS_CLUSTER --service $ECS_SERVICE --task-definition $NEW_TASK_DEF_ARN --force-new-deployment

      - echo Writing image definitions file...
      - printf '[{"name":"app","imageUri":"%s"}]' ${REPOSITORY_URL}:$IMAGE_TAG > image-definitions.json

artifacts:
  files:
    - image-definitions.json
