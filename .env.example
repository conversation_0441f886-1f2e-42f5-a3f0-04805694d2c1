NODE_ENV="development"
NODE_OPTIONS="--max-old-space-size=4096"

# Separate multiple domains with comma
ALLOWED_CORS_DOMAINS=*

#JWT SECRET KEY
JWT_SECRET="random_string"

JWT_ACCESS_TOKEN_COOKIE_NAME="token"
JWT_REFRESH_TOKEN_COOKIE_NAME="refresh_token"
JWT_REFRESH_TOKEN_DURATION="7d"
JWT_TOKEN_DURATION="30m"

#Database
DATABASE_URL="mysql://USER:PASSWORD@HOST:PORT/finance"

#Blusky
BLUSKY_BASE_URL='https://www.bluskyreporting.com/api'
BLUSKY_CUBE_ID='603962ba1f8559002d8098a9'
BLUSKY_USERNAME='<EMAIL>'
BLUSKY_PASSWORD=password
BLUSKY_DATASOURCE=ecFAWh

# Filesystem
BASE_FILE_LOCATION="./filesystem"

#Email
SENDER_EMAIL_USERNAME="finance"

# Git SHA of current backend build
GITHUB_SHA="be_sha_test"

# Email Body Lambda
EMAIL_BODY_AWS_ACCESS_KEY_ID=SAMPLEdKId2RSXTRRXBKKRUY
EMAIL_BODY_AWS_SECRET_ACCESS_KEY=SECRETazcOlikIZXUnk6wKAmrUgZdEoCqEa8qh
EMAIL_BODY_REGION=us-east-2
EMAIL_BODY_AWS_FUNCTION_NAME="internal-dynamic-email-body-lambda"

MONTHS_TO_HISTORICAL="15"

