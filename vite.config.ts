import { defineConfig, loadEnv } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";

export default defineConfig({
  plugins: [tsconfigPaths()],
  test: {
    env: {
      ...loadEnv("test", process.cwd(), ""),
      // eslint-disable-next-line @typescript-eslint/naming-convention
      NODE_ENV: "test",
    },
    globals: true,
    passWithNoTests: true,
    environment: "node",
    server: {
      deps: {
        inline: ["@fastify/autoload"], // This is crucial for Fastify routes to work
      },
    },
    coverage: {
      all: true,
      exclude: [
        "tests/*",
        "dist/*",
        "src/constants",
        "**/*.test.ts",
        "**/*.d.ts",
        "**/*.config.{js,cjs,mjs,ts}",
        "src/index.ts",
        "src/plugins/*",
      ],
      provider: "v8",
      reportOnFailure: true,
      reporter: ["html", "text"],
    },
  },
});
