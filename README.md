# The new Finance Automation Tool as micro-service

# Starter template for Node.js/Fastify back-end projects

![badge](https://img.shields.io/endpoint?url=https://gist.githubusercontent.com/al<PERSON><PERSON><PERSON>/8e0541513c13b94172328dca056d403e/raw/backend-starter-template-coverage-badge.json)

Click the green "**Use this template**" button above on GitHub to create a new repository based on this template.

### Development

To run in development (watch) mode, use:

```bash
nvm use
npm install
npm run dev
```

### Building

To build this project:

```bash
nvm use
npm install
npm run build
```

After building, there is a `dist` folder that contains the compiled JavaScript code that is transpiled and ready to work in Node.js runtime.

If you want to run this compiled code, use:

```bash
npm run start:prod
```

Or to run the compiled code using environment variables from your local `.env` file:

```bash
npm run start:dev
```

### Deployment

This project should be deployed using Docker. The Dockerfile is included in the project root.

### Environment variables

File `src/constants/environment/index.ts` contains the environment variables that are required by this project. For local development,
you can set the env vars in your `.env` file, and they will be read by `dotenv/config`. Note that `DEBUG=true`
will enable detailed debug logging (tracing).

### Set up Local Database

#### Prerequisites (macOS)

1. Ensure you have Homebrew installed on your macOS. If not, install it by following the instructions at brew.sh.
2. If MySQL is not already installed, use Homebrew to install it:

   ```bash
   brew install mysql
   ```

3. Start the MySQL Service

   To ensure MySQL is running, start the MySQL service:

   ```bash
   brew services start mysql
   ```

   Check if MySQL is running:

   ```bash
   brew services list
   ```

   The status of MySQL should show as started.

4. Access the MySQL Prompt

   Log in to the MySQL shell using the default root user:

   ```bash
   mysql -u root
   ```

   If you have set a root password previously, use:

   ```bash
   mysql -u root -p
   ```

   Enter the password when prompted.

#### Prerequisites (Windows)

1. Download and install MySQL Server from https://dev.mysql.com/downloads/installer/.
2. Open MySQL 8.0 Command Line Client.
3. Enter the root password that you set during the installation of MySQL Server.

#### Steps to Set Up MySQL and Configure the Database

1. Create the Database

   Create a database named finance_automation:

   ```bash
   CREATE DATABASE finance_automation;
   ```

   Verify the database was created:

   ```bash
   SHOW DATABASES;
   ```

2. Create a New User

   Create a new MySQL user named localUser with the password fa-password:

   ```bash
   CREATE USER 'localUser'@'localhost' IDENTIFIED BY 'fa-password';
   ```

3. Grant Privileges to the User

   Grant all privileges on the finance_automation database to localUser:

   ```bash
   GRANT ALL PRIVILEGES ON finance_automation.* TO 'localUser'@'localhost';
   ```

   To enable localUser to handle shadow databases required by Prisma, grant global privileges to localUser for creating, dropping, altering, and referencing databases.

   ```bash
   GRANT CREATE, DROP, ALTER, REFERENCES ON *.* TO 'localUser'@'localhost';
   ```

   Apply the changes:

   ```bash
   FLUSH PRIVILEGES;
   ```

4. Test the New User

   - macOS

     Exit the MySQL shell:

     ```bash
     EXIT;
     ```

     Log in as the new user to verify access:

     ```bash
     mysql -u localUser -p
     ```

     Enter fa-password when prompted. Verify access to the database.

   - Windows

     Log in as the new user:

     ```bash
     SYSTEM mysql -u localUser -p
     ```

     Enter fa-password when prompted. Verify access to the database.

5. **Import the Database Dump**

   You should have a SQL dump file (e.g., `dump.sql`) that creates tables and inserts data. Import it into your local database with:

   ```bash
   mysql -u localUser -p finance_automation < path/to/dump.sql
   ```

   - Replace `path/to/dump.sql` with the actual path to your dump file.
   - on **Enter password** provide a password to the DB: `fa-password`

     **Note:** The password used for the `admin` user within your dump is `fa-admin`.

6. Configure Prisma

   In the project, update the .env file with the following DATABASE_URL:

   ```bash
   DATABASE_URL="mysql://localUser:fa-password@localhost:3306/finance_automation"
   ```

   Generate Prisma Client:

   ```bash
   npx prisma generate
   ```

   Pull the Database Schema:

   ```bash
   npx prisma db pull
   ```

### Creating dynamic test coverage badge

1. Go to [create a new GitHub Gist](https://gist.github.com/). Use filename `repo-name-coverage-badge.json` (where `repo-name` is the name of your repository). Write anything in the contents. Click on Create **secret** gist.
2. Click on Raw button, note down the URL to the newly created gist (e.g. `https://gist.githubusercontent.com/albertbronsky/8e0541513c13b94172328dca056d403e/raw/a477c32cc8bdac0354138fa26943ea34e2ca0537/backend-starter-template-coverage-badge.json`). Remove revision ID part between `raw` and the filename.
3. Go to [Tokens in your GitHub settings](https://github.com/settings/tokens/new) and create a new token (use 'classic' one). Name it `Dynamic Badges`. Make it non-expiring. Select `gist` scope only. Save and note down that token.
4. Go to Repo settings -> Secrets and variables -> Actions. Create New repository secrets:
   - Name: `GIST_TOKEN`
   - Value: `<your generated token from before>`
   - Name: `GIST_ID`
   - Value: `<your gist ID from the URL>` (e.g. `8e0541513c13b94172328dca056d403e`)
   - Name: `GIST_FILENAME`
   - Value: `<your gist filename.json>` (e.g. `backend-starter-template-coverage-badge.json`)
5. Now in `README.md` of your repo put the badge as a link to `shields.io` (change the url parameter accordingly) under the title like this:

```
![badge](https://img.shields.io/endpoint?url=https://gist.githubusercontent.com/albertbronsky/8e0541513c13b94172328dca056d403e/raw/backend-starter-template-coverage-badge.json)
```
