{
  "$schema": "https://json.schemastore.org/tsconfig",
  "display": "Node 20 + ESM + Strictest",
  "include": [
    // TypeScript compiler will compile all '.ts' files from 'src' directory
    "src"
  ],
  "exclude": [
    // Avoids unnecessary files in the builds
    "node_modules"
  ],
  "compilerOptions": {
    "paths": {
      "@constants/*": ["./src/constants/*"],
      "@lib/*": ["./src/lib/*"],
      "@types/*": ["./src/types/*"],
      "@utils/*": ["./src/utils/*"]
    },
    // The resulting compiled '.js' files are placed into 'dist' directory
    "outDir": "dist",
    "lib": ["es2022"],
    "module": "es2022",
    "target": "es2022",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "node",
    "allowUnusedLabels": false,
    "allowSyntheticDefaultImports": true,
    "allowUnreachableCode": false,
    "exactOptionalPropertyTypes": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitOverride": true,
    "noImplicitReturns": true,
    "noPropertyAccessFromIndexSignature": true,
    "noUncheckedIndexedAccess": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "allowJs": true,
    "isolatedModules": true,
    "checkJs": true,
    "types": ["vitest/globals"]
  },
  "tsc-alias": {
    "resolveFullPaths": true
  }
}
