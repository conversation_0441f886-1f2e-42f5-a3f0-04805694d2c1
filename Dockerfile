FROM node:20.18.0-alpine
WORKDIR /var/www
RUN apk update && apk add --no-cache dos2unix
RUN addgroup --system --gid 1006 fauser
RUN adduser --system --disabled-password --home /home/<USER>
    --uid 1006 --ingroup fauser fauser
RUN chown -R fauser:fauser .
USER fauser
RUN mkdir filesystem
COPY ["package.json", "./"]
RUN npm install
COPY --chown=fauser:fauser . .
RUN chmod 755 entrypoint.sh
RUN npm run build
EXPOSE 8081
# RUN npx prisma generate
# CMD ["npm", "run", "start:prod"]
CMD ["sh", "-c", "./entrypoint.sh"]

